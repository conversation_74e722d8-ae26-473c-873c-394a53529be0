/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by vite-plugin-uni-components
// Read more: https://github.com/vuejs/core/pull/3399
import '@vue/runtime-core'

export {}

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    AppTest: typeof import('./src/components/AppTest.vue')['default']
    AppTest2: typeof import('./src/components/app-test-dir/AppTest2.vue')['default']
    FlyContent: typeof import('./src/components/fly-content/fly-content.vue')['default']
    FlyHeader: typeof import('./src/components/fly-header/fly-header.vue')['default']
    FlyLogin: typeof import('./src/components/fly-login/fly-login.vue')['default']
    FlyNavbar: typeof import('./src/components/fly-navbar/fly-navbar.vue')['default']
    SvgIcon: typeof import('./src/components/SvgIcon.vue')['default']
  }
}
