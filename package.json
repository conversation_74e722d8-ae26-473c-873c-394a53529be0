{"name": "coffee-mini", "type": "commonjs", "version": "1.5.0", "license": "MIT", "homepage": "https://codercup.github.io/unibest/", "packageManager": "pnpm@8.15.1", "engines": {"node": ">=18", "pnpm": ">=7.30"}, "scripts": {"dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev": "uni", "dev-dev": "uni --mode development", "dev-test": "uni --mode test", "dev-prod": "uni --mode production", "dev:h5": "uni", "dev:mp-weixin": "cross-env VITE_PLATFORM=wx uni -p mp-weixin", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:h5": "uni build", "build": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-weixin": "uni build -p mp-weixin", "prepare": "node ./shell/postinstall.js", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4000820240401001", "@dcloudio/uni-app-plus": "3.0.0-4000820240401001", "@dcloudio/uni-components": "3.0.0-4000820240401001", "@dcloudio/uni-h5": "3.0.0-4000820240401001", "@dcloudio/uni-mp-weixin": "3.0.0-4000820240401001", "@dcloudio/uni-ui": "1.5.0", "@vueuse/core": "10.11.1", "big.js": "^7.0.1", "crypto-js": "^4.2.0", "dayjs": "1.11.10", "pinia": "2.0.36", "pinia-plugin-persistedstate": "3.2.1", "qs": "6.5.3", "vue": "3.3.11", "wot-design-uni": "^1.8.0", "z-paging": "^2.7.10"}, "devDependencies": {"@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-4000820240401001", "@dcloudio/uni-cli-shared": "3.0.0-4000820240401001", "@dcloudio/uni-stacktracey": "3.0.0-4000820240401001", "@dcloudio/vite-plugin-uni": "3.0.0-4000820240401001", "@huolala-tech/page-spy-uniapp": "^2.2.3", "@huolala-tech/page-spy-wechat": "^2.2.1", "@iconify-json/carbon": "^1.1.31", "@types/node": "^20.12.7", "@types/wechat-miniprogram": "^3.4.7", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@uni-helper/uni-ui-types": "^0.5.13", "@uni-helper/vite-plugin-uni-components": "^0.0.8", "@uni-helper/vite-plugin-uni-layouts": "^0.1.7", "@uni-helper/vite-plugin-uni-manifest": "^0.2.3", "@uni-helper/vite-plugin-uni-pages": "^0.2.18", "@uni-helper/vite-plugin-uni-platform": "^0.0.4", "@unocss/preset-legacy-compat": "0.58.9", "@vue/runtime-core": "^3.4.21", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.19", "cross-env": "^7.0.3", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-config-standard": "^17.1.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.24.1", "postcss": "^8.4.38", "postcss-html": "^1.6.0", "postcss-scss": "^4.0.9", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.74.1", "stylelint": "^16.3.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.6.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^35.0.0", "stylelint-config-standard-scss": "^12.0.0", "stylelint-prettier": "^5.0.0", "terser": "^5.30.3", "typescript": "^4.9.5", "unocss": "^0.58.9", "unocss-applet": "^0.7.8", "unplugin-auto-import": "^0.17.5", "vite": "4.3.5", "vite-plugin-mock": "^3.0.1", "vite-plugin-restart": "^0.4.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend": "^0.4.0", "vite-svg-loader": "^5.1.0", "vue-tsc": "^1.8.27"}, "pnpm": {"patchedDependencies": {"wot-design-uni@1.8.0": "patches/<EMAIL>"}}}