import { defineUniPages } from '@uni-helper/vite-plugin-uni-pages'

export default defineUniPages({
  globalStyle: {
    navigationStyle: 'default',
    navigationBarTitleText: 'lanao',
    navigationBarBackgroundColor: '#fff',
    navigationBarTextStyle: 'black',
    backgroundColor: '#FFFFFF',
  },
  easycom: {
    autoscan: true,
    custom: {
      '^uni-(.*)': '@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue',
      '^wd-(.*)': 'wot-design-uni/components/wd-$1/wd-$1.vue',
      '^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)':
        'z-paging/components/z-paging$1/z-paging$1.vue',
    },
  },
  tabBar: {
    color: '#ACB2C4',
    selectedColor: '#356AFD',
    backgroundColor: '#FFFFFF',
    borderStyle: 'black',
    fontSize: '10px',
    iconWidth: '24px',
    spacing: '3px',
    custom: true,
    list: [
      {
        iconPath: 'static/tabbar/home.png',
        selectedIconPath: 'static/tabbar/homeHL.png',
        pagePath: 'pages/index/index',
        text: '首页',
      },
      {
        iconPath: 'static/tabbar/home.png',
        selectedIconPath: 'static/tabbar/homeHL.png',
        pagePath: 'pages/order/index',
        text: '订单',
      },
      {
        iconPath: 'static/tabbar/personalCenter.png',
        selectedIconPath: 'static/tabbar/personalCenterHL.png',
        pagePath: 'pages/my/index',
        text: '我的',
      },
    ],
  },
})
