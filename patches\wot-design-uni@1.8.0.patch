diff --git a/.idea/.gitignore b/.idea/.gitignore
new file mode 100644
index 0000000000000000000000000000000000000000..a7cdac76c182c2f5bce8533edd496b92e5335d33
--- /dev/null
+++ b/.idea/.gitignore
@@ -0,0 +1,8 @@
+# 默认忽略的文件
+/shelf/
+/workspace.xml
+# 基于编辑器的 HTTP 客户端请求
+/httpRequests/
+# Datasource local storage ignored files
+/dataSources/
+/dataSources.local.xml
diff --git a/.idea/6eb430695478e280ae0c9186277d4b39.iml b/.idea/6eb430695478e280ae0c9186277d4b39.iml
new file mode 100644
index 0000000000000000000000000000000000000000..0b872d82d9c39f70bff219b3b4b60145430f8984
--- /dev/null
+++ b/.idea/6eb430695478e280ae0c9186277d4b39.iml
@@ -0,0 +1,12 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<module type="WEB_MODULE" version="4">
+  <component name="NewModuleRootManager">
+    <content url="file://$MODULE_DIR$">
+      <excludeFolder url="file://$MODULE_DIR$/.tmp" />
+      <excludeFolder url="file://$MODULE_DIR$/temp" />
+      <excludeFolder url="file://$MODULE_DIR$/tmp" />
+    </content>
+    <orderEntry type="inheritedJdk" />
+    <orderEntry type="sourceFolder" forTests="false" />
+  </component>
+</module>
\ No newline at end of file
diff --git a/.idea/modules.xml b/.idea/modules.xml
new file mode 100644
index 0000000000000000000000000000000000000000..7a06edcb0d1d2031892b08be4b4b6cf4515245bc
--- /dev/null
+++ b/.idea/modules.xml
@@ -0,0 +1,8 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<project version="4">
+  <component name="ProjectModuleManager">
+    <modules>
+      <module fileurl="file://$PROJECT_DIR$/.idea/6eb430695478e280ae0c9186277d4b39.iml" filepath="$PROJECT_DIR$/.idea/6eb430695478e280ae0c9186277d4b39.iml" />
+    </modules>
+  </component>
+</project>
\ No newline at end of file
diff --git a/.idea/workspace.xml b/.idea/workspace.xml
new file mode 100644
index 0000000000000000000000000000000000000000..ede79b44891fcdd2260dba26eedd3c52090ce555
--- /dev/null
+++ b/.idea/workspace.xml
@@ -0,0 +1,62 @@
+<?xml version="1.0" encoding="UTF-8"?>
+<project version="4">
+  <component name="AutoImportSettings">
+    <option name="autoReloadType" value="SELECTIVE" />
+  </component>
+  <component name="ChangeListManager">
+    <list default="true" id="4a428407-5518-448d-b9e3-b6634d384040" name="更改" comment="" />
+    <option name="SHOW_DIALOG" value="false" />
+    <option name="HIGHLIGHT_CONFLICTS" value="true" />
+    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
+    <option name="LAST_RESOLUTION" value="IGNORE" />
+  </component>
+  <component name="ProjectColorInfo">{
+  &quot;associatedIndex&quot;: 6
+}</component>
+  <component name="ProjectId" id="314lPByQ7G6VtCNFrbYSTkC4Bs5" />
+  <component name="ProjectViewState">
+    <option name="autoscrollFromSource" value="true" />
+    <option name="hideEmptyMiddlePackages" value="true" />
+    <option name="showLibraryContents" value="true" />
+  </component>
+  <component name="PropertiesComponent">{
+  &quot;keyToString&quot;: {
+    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
+    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
+    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
+    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
+    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
+    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
+    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
+    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
+    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.fileTypes&quot;,
+    &quot;to.speed.mode.migration.done&quot;: &quot;true&quot;,
+    &quot;ts.external.directory.path&quot;: &quot;E:\\webstorm\\WebStorm 2024.1.3\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
+    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
+  }
+}</component>
+  <component name="SharedIndexes">
+    <attachedChunks>
+      <set>
+        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-WS-251.26927.40" />
+      </set>
+    </attachedChunks>
+  </component>
+  <component name="TaskManager">
+    <task active="true" id="Default" summary="默认任务">
+      <changelist id="4a428407-5518-448d-b9e3-b6634d384040" name="更改" comment="" />
+      <created>1754791199221</created>
+      <option name="number" value="Default" />
+      <option name="presentableId" value="Default" />
+      <updated>1754791199221</updated>
+      <workItem from="1754791200656" duration="38000" />
+      <workItem from="1754791587749" duration="499000" />
+      <workItem from="1754792468129" duration="59000" />
+      <workItem from="1754792852105" duration="107000" />
+    </task>
+    <servers />
+  </component>
+  <component name="TypeScriptGeneratedFilesManager">
+    <option name="version" value="3" />
+  </component>
+</project>
\ No newline at end of file
diff --git a/components/wd-select-picker/wd-select-picker.vue b/components/wd-select-picker/wd-select-picker.vue
index 98335347dae7862d47ce9a70d4031ae734459668..649bff1d5408d016bda5a9ab0f062965e3b9feaa 100644
--- a/components/wd-select-picker/wd-select-picker.vue
+++ b/components/wd-select-picker/wd-select-picker.vue
@@ -89,7 +89,9 @@
                   </block>
                 </block>
                 <block v-else>
-                  {{ item[labelKey] }}
+                  <slot name="item" :item="item">
+                    {{ item[labelKey] }}
+                  </slot>
                 </block>
               </wd-radio>
             </view>
diff --git a/components/wd-swiper/wd-swiper.vue b/components/wd-swiper/wd-swiper.vue
index 4869c63cbe163953cd8399e74d0bb80c4b39df86..fa91d4b1458995cbde1773b4ed74a9e6471b34de 100644
--- a/components/wd-swiper/wd-swiper.vue
+++ b/components/wd-swiper/wd-swiper.vue
@@ -32,11 +32,12 @@
             :class="`wd-swiper__video ${customItemClass} ${getCustomItemClass(currentValue, index, list)}`"
             @play="handleVideoPaly"
             @pause="handleVideoPause"
+            :controls="false"
             :enable-progress-gesture="false"
             :loop="videoLoop"
             :muted="muted"
-            :autoplay="autoplayVideo"
             objectFit="cover"
+            :autoplay="autoplayVideo"
             @click="handleClick(index, item)"
           />
           <image
@@ -114,6 +115,7 @@ const { proxy } = getCurrentInstance() as any
 
 const uid = ref<string>(uuid())
 
+
 watch(
   () => props.current,
   (val) => {