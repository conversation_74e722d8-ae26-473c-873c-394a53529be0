<script lang="ts" setup>
import { onLaunch, onShow, onHide, onPageNotFound, onBackPress } from '@dcloudio/uni-app'
import { useUserStore, useGlobalStore } from '@/store'
import { storeToRefs } from 'pinia'
const userStore = useUserStore()
const { isMaintenance } = storeToRefs(userStore)
onLaunch(() => {
  // 初始化设置
  const { setJumpSelecting } = useGlobalStore()
  setJumpSelecting(true)
})

onShow(() => {
  console.log('请求地址', import.meta.env.VITE_SERVER_BASEURL)
  // 获取用户权限，跳转页面
  const { isJumpSelecting } = useGlobalStore()

  // 跳过触发的 onShow（文件选择等）
  if (!isJumpSelecting || !!window) return
  if (isMaintenance.value) {
    // 跳转运维端
    const timer = setTimeout(() => {
      clearTimeout(timer)
      // 判断是否在维修端, 不在则跳转
      const isMaintenancePage = getCurrentPages()
        .map((item) => item.route)
        .some((route) => route.startsWith('pages-maintenance'))

      if (!isMaintenancePage) {
        uni.redirectTo({ url: '/pages-maintenance/index/index' })
      }
    }, 200)
  }
})
onPageNotFound(() => {
  console.log('页面不存在监听函数')
  // 重定向登录
  uni.navigateTo({ url: '/' })
})
</script>
<!--wot组件库的样式穿透-->
<script lang="ts">
export default {
  options: {
    styleIsolation: 'shared',
  },
}
</script>
<style lang="scss">
page {
  height: 100vh;
  --page-bg-color: #eff1f5;
  --wot-text-info-color: #1a1a1a;
  --wot-button-large-radius: 10px;
  --wot-button-medium-radius: 6px;
  background-color: var(--page-bg-color);
  --theme-color-user: #6d3202;
  --theme-color-ops: #356afd;
}

/* stylelint-disable selector-type-no-unknown */
button::after {
  border: none;
}

//swiper,
//scroll-view {
//  flex: 1;
//  height: 100%;
//  overflow: hidden;
//}

image {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}

uni-page-body {
  height: 100%;
}

// 单行省略，优先使用 unocss: text-ellipsis
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 两行省略
.ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

// 三行省略
.ellipsis-3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.message-box {
  --wot-message-box-padding: 0;

  .wd-message-box__title {
    @apply h-12.5 box-border flex items-center justify-left p-4 font-bold text-lg border-b-1 border-b-solid border-[#EFF1F5];
  }

  .wd-message-box__content {
    @apply p-4;
  }

  .wd-button {
    border-radius: 8px !important;

    &.is-info {
      background-color: #f8f9fc;
    }

    &.is-primary {
      background-color: #356afd;
    }
  }
}

.message-box-confirm {
  .wd-message-box__title {
    font-weight: bold !important;
  }

  .wd-message-box__content {
    @apply p-4;
  }

  .wd-button {
    &.is-info {
      height: 40px !important;
      background-color: #f8f9fc;
    }

    &.is-primary {
      height: 40px !important;
    }
  }
}
.ops-calendar {
  :deep(.wd-calendar__confirm.wd-calendar__confirm .wd-button) {
    height: 40px;
    background: var(--theme-color-ops, #356afd);
    border-radius: 10px;
  }
}
</style>
