<template>
  <wd-button v-bind="$attrs" @click="onClick">{{ btnName }}</wd-button>
</template>

<script lang="ts" setup>
/**
 * @file 弹窗二次确认组件
 * <AUTHOR>
 * @date 2024/1/2
 */
import { useMessage, useToast } from 'wot-design-uni'
const props = withDefaults(
  defineProps<{
    // 标题
    title?: string
    // 提示信息
    msg?: string
    // 按钮名称
    btnName?: string
    // 主题
    theme?: 'dark' | 'light'
    // 提交请求
    fetch?: () => Promise<any>
    // 提交成功回调
    onSubmitSuccess?: () => void
    // 是否显示弹窗
    isShowDialog?: boolean
  }>(),
  {
    theme: 'light',
    btnName: '按钮',
    title: '提示',
    msg: '是否确认操作',
  },
)
// 提示弹窗
const message = useMessage()
const toast = useToast()
const onClick = () => {
  if (props.isShowDialog) return

  message
    .confirm({
      title: props.title,
      msg: props.msg,
      beforeConfirm: async ({ resolve }) => {
        if (props.fetch) {
          await props
            .fetch?.()
            .then((res) => {
              if (res.statusCode === 101) {
                resolve(true)
                toast.success('操作成功')
                props.onSubmitSuccess?.()
              } else {
                resolve(false)
                toast.error('操作失败')
              }
            })
            .catch((error) => {
              console.log('error', error)
            })
        }
        resolve(true)
      },
    })
    .then(() => {
      // refreshData()
      // props.onSubmitSuccess?.()
    })
    .catch((error) => {
      console.log(error)
    })
}
</script>

<style scoped></style>
