<template>
  <wd-popup :z-index="10000" v-model="show" :close-on-click-modal="false"
    custom-style="width: 100%; height: 100%;background-color:rgba(0,0,0,0.4);">
    <view class="relative w-full h-full flex items-center justify-center">
      <view @click="close()" class="absolute right-6 top-6">
        <wd-icon name="close-circle" color="#fff" size="26px"></wd-icon>
      </view>
      <image style="width: 70%" :src="couponImg" mode="widthFix" @click="onReceive()" />
    </view>
  </wd-popup>
</template>
<script lang="ts" setup>
import { useToast } from 'wot-design-uni'
import { fetchReceiveCoupon } from '@/service/api'

const toast = useToast()
const show = ref(false)
const couponImg = ref()
const couponId = ref()
const open = (payload: any) => {
  show.value = true
  couponId.value = payload.id
  couponImg.value = payload.img
}
const close = () => {
  show.value = false
}

const onReceive = () => {
  fetchReceiveCoupon({ id: couponId.value }).then((res) => {
    if (res.success) {
      show.value = false
      toast.success('领取成功')
    } else {
      show.value = false
      toast.warning(res.message)
    }
  })
}

defineExpose({
  open,
})
</script>

<style lang="scss" scoped></style>
