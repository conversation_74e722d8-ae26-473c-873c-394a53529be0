<template>
  <wd-overlay :show="modelValue" @click="emit('update:modelValue', false)">
    <view class="flex flex-col h-screen justify-center" @click.stop="">
      <wd-card>
        <template v-if="title && mode !== 'delete'" #title>
          <view class="text-lg font-bold">
            <view>{{ title }}</view>
          </view>
        </template>
        <view v-if="mode === 'delete'" class="flex flex-col items-center">
          <wd-text
            :text="title"
            bold
            class="text-lg font-bold"
            color="var(--text-primary-color)"
            size="32rpx"
          />
          <view class="my-2 flex flex-col items-center">
            <wd-text v-for="item of content.split('\\n')" :key="item" :text="item" />
          </view>
        </view>
        <slot />
        <template v-if="footer" #footer>
          <view class="flex justify-end gap-2">
            <wd-button
              v-if="(footer as Footer)?.cancel"
              :loading="cancelLoading"
              :round="false"
              class="flex-1"
              size="large"
              type="info"
              @click="onCancel"
            >
              {{ (footer as Footer)?.cancel?.text }}
            </wd-button>
            <wd-button
              v-if="(footer as Footer)?.confirm"
              :loading="submitLoading"
              :round="false"
              class="flex-1"
              size="large"
              type="primary"
              @click="onSubmit"
            >
              {{ (footer as Footer)?.confirm?.text }}
            </wd-button>
          </view>
        </template>
      </wd-card>
    </view>
  </wd-overlay>
</template>
<script lang="ts" setup>
import { useToast } from 'wot-design-uni'
const emit = defineEmits(['update:modelValue'])
type Footer = {
  cancel?: { text: string; onClick?: (close: () => void, defaultOnClick: () => void) => void }
  confirm?: { text: string; onClick?: (close: () => void, defaultOnClick: () => void) => void }
}
const childrenPromises = new Set<() => Promise<any>>()
provide('beforeSubmit', (promise: () => Promise<any>) => {
  childrenPromises.add(promise)
})
// 如果是列表编辑，则需要刷新列表
const reloadPageList = inject<() => Promise<any>>('reloadPageList', () => Promise.resolve())
const { success: showSuccess, error: showError } = useToast()
const cancelLoading = ref(false)
const submitLoading = ref(false)
const onCancel = async () => {
  const defaultOnCancel = () => {
    emit('update:modelValue', false)
  }
  if (props.footer?.cancel?.onClick) {
    cancelLoading.value = true
    await props.footer.cancel.onClick(defaultOnCancel, defaultOnCancel)
    cancelLoading.value = false
  } else {
    defaultOnCancel()
  }
}
const onSubmit = async () => {
  // 默认行为 点击之后提交表单并刷新列表数据
  const defaultOnSubmit = () => {
    submitLoading.value = true
    if (props.mode === 'delete') {
      return props
        .submitRequest?.()
        .then(() => {
          showSuccess({ msg: '操作成功' })
          const timer = setTimeout(() => {
            emit('update:modelValue', false)
            reloadPageList()
            clearTimeout(timer)
          }, 1000)
        })
        .catch((error) => {
          showError({ msg: '操作失败' })
        })
        .finally(() => {
          submitLoading.value = false
        })
    }
    // 提交成功之后刷新列表数据
    return Promise.all(Array.from(childrenPromises).map((promise) => promise()))
      .then(() => {
        reloadPageList()
        emit('update:modelValue', false)
      })
      .finally(() => {
        submitLoading.value = false
      })
  }
  if (props.footer?.confirm?.onClick) {
    await props.footer.confirm.onClick(() => {
      emit('update:modelValue', false)
    }, defaultOnSubmit)
    submitLoading.value = false
  } else {
    defaultOnSubmit()
  }
}
const props = withDefaults(
  defineProps<{
    title: string
    content?: string
    footer?: Footer
    modelValue?: boolean
    mode: 'delete' | 'edit'
    submitRequest?: () => Promise<any>
  }>(),
  {
    footer: (): Footer => ({
      cancel: { text: '取消' },
      confirm: { text: '确定' },
    }),
    mode: 'edit',
  },
)
</script>
