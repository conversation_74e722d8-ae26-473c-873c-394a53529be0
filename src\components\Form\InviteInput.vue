<template>
  <wd-password-input
    v-bind="$attrs"
    :gutter="10"
    :mask="false"
    :length="6"
    :focused="showKeyboard"
    @focus="showKeyboard = true"
    :info="info"
  />
  <wd-number-keyboard
    v-bind="$attrs"
    :show="showKeyboard"
    :maxlength="6"
    @blur="showKeyboard = false"
  />
</template>
<script lang="ts" setup>
const props = defineProps({
  info: { type: String, default: '请输入邀请码' },
})
const showKeyboard = ref(false)
</script>
