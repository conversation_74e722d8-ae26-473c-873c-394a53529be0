<template>
  <view class="flex w-full bg-white box-border" v-if="searchForm.show || searchForm.showAdd">
    <wd-search
      v-model="searchValue"
      :placeholder="placeholder"
      hide-cancel
      placeholder-left
      @change="onSearchChange"
      @clear="handleSearchClear"
    />
    <view
      v-if="searchForm.showAdd"
      class="flex items-center pr-[10px]"
      style="color: rgba(53, 106, 253, 1)"
      @click="toForm"
    >
      <wd-icon name="add" size="14px"></wd-icon>
      <view class="ml-1 line-height-normal">新增</view>
    </view>
  </view>

  <view v-if="finalOptions?.length">
    <wd-drop-menu>
      <template v-for="item in finalOptions" :key="item.prop">
        <!--普通下拉选项-->
        <wd-drop-menu-item
          v-bind="findOption(item).defaultProps"
          v-if="!item.slot"
          v-model="menusModel[item.prop]"
          :options="item.options"
          :title="item.options.find((i) => i.value === menusModel[item.prop])?.label || item.title"
          @change="onSearchChange"
        ></wd-drop-menu-item>
        <!--这里接收传递进入的指定插槽name-->
        <wd-drop-menu-item
          :ref="(el) => (slotRefs[item.prop] = el)"
          v-bind="findOption(item).AllProps"
          v-if="item.slot && !item.slotType"
        >
          <slot name="dropMenu" :slotRef="slotRefs[item.prop]" :item="item"></slot>
        </wd-drop-menu-item>
        <!--自定义插槽-->
        <wd-drop-menu-item
          v-bind="{
            ...findOption(item).AllProps,
          }"
          custom-popup-class="text-center"
          v-if="item.slot && item.slotType === 'date'"
          ref="dateRef"
          v-model="menusModel[item.prop]"
          :title="menusModel[item.prop] || item.title"
          @close="handleDateClose"
        >
          <uni-calendar
            v-bind="{
              insert: true,
              showMonth: false,
              'end-date': '2099-5-20',
              'start-date': '2001-1-1',
              ...findOption(item).customProps.slotProps,
            }"
            :date="menusModel[item.prop] || ''"
            @change="calendarChange($event, item)"
          />
          <wd-button
            custom-class="w-[90%] !rounded-2 mb-1 m-auto"
            type="primary"
            @click="handleTime(item.prop, menusModel[item.prop])"
          >
            确认
          </wd-button>
        </wd-drop-menu-item>
      </template>
    </wd-drop-menu>
  </view>
</template>
<!--wot组件库的样式穿透-->
<script lang="ts">
export default {
  options: {
    styleIsolation: 'shared',
  },
}
</script>
<script lang="ts" setup>
export interface SearchFormOption {
  // 新增form的功能
  searchForm?: {
    // 是否需要显示搜索
    show?: boolean
    // 是否需要显示新增
    showAdd?: boolean
    // 跳转路径
    path?: string
  }
  placeholder?: string
  modelValue?: { [key: string]: string }
  options?: {
    // 下拉搜索项字段
    prop: string
    // 是否使用插槽
    slot?: boolean
    // 插槽类型名称
    slotType?: string
    // 是否在不需要前面添加一个全部的选项 默认false 表示添加
    noNeedAllOption?: boolean
    // 标题
    title?: string
    // 下拉请求
    requestApi?: () => Promise<any>
    // 请求返回的数据结构
    replaceOptions?: { label: string; value: string }
    // 自定义下拉选项
    options?: { label: string; value: string }[]
    // 默认值
    initialValue?: any
    // 自定义插槽
    slotProps?: any
    [key: string]: any
  }[]
  // 自定义搜索字段名
  searchKey?: string
}
const props = withDefaults(defineProps<SearchFormOption>(), {
  searchForm: () => ({
    show: true,
    showAdd: false,
  }),
  options: () => [],
  // 自定义字段名
  searchKey: 'search',
})
// 传入item找到props.options
const findOption = (item: any) => {
  const { prop, requestApi, replaceOptions, slot, slotType, slotProps, noNeedAllOption, ...rest } =
    props.options.find((i) => i.prop === item.prop)
  return {
    customProps: { prop, requestApi, replaceOptions, slot, slotType, noNeedAllOption, slotProps },
    defaultProps: {
      ...rest,
    },
    AllProps: props.options.find((i) => i.prop === item.prop),
  }
}
// 时间选择器变化时触发,如果再次点击相同则取消选中
const calendarChange = (e: any, item: any) => {
  if (menusModel.value[item.prop] === e.fulldate) {
    menusModel.value[item.prop] = ''
  } else {
    menusModel.value[item.prop] = e.fulldate
  }
}
// 关闭刷新
const handleDateClose = () => {
  onSearchChange()
}

const slotRefs = ref<Record<string, any>>({})
// 点击清空搜索时
const handleSearchClear = () => {
  searchValue.value = '' // 清空 searchValue
  const updatedModelValue = { ...props.modelValue }
  delete updatedModelValue[props.searchKey] // 删除搜索键
  emit('update:modelValue', updatedModelValue) // 更新父组件的 modelValue
}
const emit = defineEmits(['update:modelValue', 'search'])

const searchValue = ref('')
const dateRef = ref()
const menusModel = ref<{ [key: string]: string }>({ ...props.modelValue })
// 默认的菜单值，用于在搜索时重置菜单值
const defaultMenusModel = {}
// 如果是列表编辑，则需要刷新列表
const reloadPageList = inject<() => Promise<any>>('reloadPageList', () => Promise.resolve())
// drop menu 最终显示的数据
const finalOptions = ref([])
// 筛选时间确认后更新搜索表单
const handleTime = (label: string, time: string) => {
  emit('update:modelValue', { ...props.modelValue, [label]: time })
  dateRef.value[0].close()
}
// 跳转表单页
const toForm = () => {
  uni.navigateTo({
    url: props.searchForm?.path,
    success: (opt) => {
      // 刷新
      opt.eventChannel.on('refresh', () => {
        reloadPageList()
      })
    },
  })
}
// 外部传递了 requestApi 的选项，需要请求数据
const requestOptions = (options: SearchFormOption['options']) => {
  return Promise.all(
    options.map((item) => {
      // 如果有设置replaceOptions自定义的接口字段
      if (item.requestApi) {
        return item.requestApi().then((res) => {
          // 设置了replaceOptions自定义的接口字段
          if (item.replaceOptions) {
            // 判断数据来源：res.data 是数组则直接使用，否则使用 res.data.records
            const dataSource = Array.isArray(res.data) ? res.data : res.data?.records || []
            let options = dataSource.map((listData: any) => {
              return {
                label: listData[item.replaceOptions.label],
                value: listData[item.replaceOptions.value],
              }
            })
            // 把全部选项加入数据
            if (!item.noNeedAllOption && options) {
              options = [{ label: item.title, value: '-1' }, ...options]
            }
            // 处理好的数据加入配置项
            return { ...item, options }
          } else {
            return { ...item, options: res.data.records }
          }
        })
      }
      // 处理不是接口的数据 并且有选项时
      if (!item.noNeedAllOption && item.options) {
        return {
          ...item,
          options: [{ label: item.title, value: '-1' }, ...item.options],
        }
      }
      // 处理插槽里的组件值 如时间选择器
      return item
    }),
  )
}
// 生成最终的选项
const generateOptions = () => {
  requestOptions(props.options).then((res) => {
    res.forEach((item) => {
      // 如果有设置传递默认值则优先使用默认值
      if (item.initialValue) {
        menusModel.value[item.prop] = item.initialValue
      } else if (item.options) {
        // 如果是select有options设置下拉默认值
        menusModel.value[item.prop] = item.options[0].value
      }
    })
    Object.assign(defaultMenusModel, menusModel.value)
    finalOptions.value = res
  })
}
watch(
  () => [props.options, props.placeholder],
  () => generateOptions(),
  {
    deep: true,
    immediate: true,
  },
)

// 搜索项更新
watch(
  () => props.modelValue,
  () => {
    // 如果只有一个基础search搜索项且搜索内容为空，则重置搜索项
    if (Object.keys(props.modelValue).length === 0) {
      menusModel.value = defaultMenusModel
      searchValue.value = ''
    }
  },
  { deep: true, immediate: true },
)
const onSearchChange = () => {
  // 移除'全部'(声明值为'-1')的选项，只保留有值的选项
  // 保留有值的选项
  const menuValue = {}
  Object.keys(menusModel.value).forEach((key) => {
    if (
      menusModel.value[key] !== '-1' &&
      menusModel.value[key] !== undefined &&
      menusModel.value[key] !== null &&
      menusModel.value[key] !== ''
    ) {
      menuValue[key] = menusModel.value[key]
    }
  })
  const searchParams = {
    ...(searchValue.value && { [props.searchKey]: searchValue.value }),
    ...menuValue,
  }

  emit('update:modelValue', searchParams)
  emit('search', searchParams)
}
</script>
<style lang="scss" scoped>
:deep(.is-medium) {
  width: 95%;
  margin: 0 auto;
}

:deep(.wd-search) {
  @apply flex-1;
}

:deep(.wd-drop-menu__list) {
  overflow-x: auto;
  overflow-y: hidden;
}

:deep(.wd-drop-menu__item) {
  min-width: initial;
}

:deep(.wd-drop-menu__arrow) {
  right: 0;
}
</style>
