<template>
  <view>
    <wd-input
      v-model="internalValue"
      placeholder="请输入短信验证码"
      use-suffix-slot
      v-bind="$attrs"
    >
      <template #suffix>
        <view style="color: rgba(77, 128, 240)" @click="handleCaptchaAction">
          {{ countdown > 0 ? `${countdown}s` : '获取验证码' }}
        </view>
      </template>
    </wd-input>
  </view>
</template>

<script lang="ts" setup>
// 定义 props 类型
interface ComponentProps {
  modelValue: string
}

// 接收 props
const props = defineProps<ComponentProps>()

// 定义 emit 类型
const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
}>()

// 内部状态
const internalValue = ref(props.modelValue)

// 倒计时状态
const countdown = ref(0)
let timer: any = null

// 当内部值变化时，更新 modelValue 并触发事件
watch(internalValue, (newValue) => {
  emit('update:modelValue', newValue)
})

// 监听 props 的变化
watch(
  () => props.modelValue,
  (newVal) => {
    internalValue.value = newVal
  },
)

// 获取验证码方法
function handleCaptchaAction() {
  if (countdown.value > 0) return // 如果还有倒计时，则不再点击

  startCountdown(60) // 开始60秒倒计时
  console.log('获取验证码的逻辑')
}

// 开始倒计时的功能
function startCountdown(seconds: number) {
  countdown.value = seconds

  // 每秒减少计时
  timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer) // 倒计时结束，清除定时器
    }
  }, 1000)
}

// 清理定时器
onBeforeUnmount(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>

<style scoped></style>
