<template>
  <wd-form
    ref="formRef"
    :model="model"
    :rules="formOption.rules"
    custom-class="form-custom"
    error-type="toast"
  >
    <view class="flex flex-col gap-2">
      <view
        v-for="option in formOption.options.filter((item) => !item.bottom)"
        :key="option.props.prop"
      >
        <!--常规输入框-->
        <wd-input
          v-if="option.type === 'input' || option.type === 'input-password'"
          v-model="model[option.props.prop]"
          :placeholder="getPlaceholder(option.type, option.props)"
          :show-password="option.type === 'input-password'"
          custom-class=" rounded-1"
          label=""
          no-border
          v-bind="option.props"
        />
        <!--邀请码-->
        <InviteInput
          v-if="option.type === 'input-otp'"
          v-model="model[option.props.prop]"
          v-bind="option.props"
        />
        <!--验证码组件-->
        <InputCode
          v-if="option.type === 'input-code'"
          v-model="model[option.props.prop]"
          v-bind="option.props"
        />
        <slot v-if="option.type === 'slot'" :model="model" :name="option.props.prop" />
      </view>
    </view>
  </wd-form>
  <!--提交按钮-->
  <wd-button
    v-if="option.submitLabel"
    :round="false"
    block
    size="large"
    type="primary"
    @click="handleSubmit"
  >
    {{ option.submitLabel }}
  </wd-button>

  <slot :model="model" name="footer" />
</template>

<script lang="ts" setup>
import { useToast } from 'wot-design-uni'
import { getPlaceholder, schemasToOptions } from './utils'
import type { Schema } from './utils'
import InviteInput from './InviteInput.vue'
import InputCode from './components/InputCode.vue'
export type FormOption = {
  schemas: Schema[]
  submitRequest?: (data: any) => Promise<any>
  submitLabel?: string
  afterSubmit?: (data: any) => void
}

const props = withDefaults(defineProps<{ option: FormOption }>(), {
  option: () => ({
    schemas: [],
    submitRequest: () => Promise.resolve(),
    submitLabel: '提交',
  }),
})
type A = { [K in ExtractPublicPropTypes<typeof props>]: unknown }
const setBeforeSubmit = inject<(promise: () => void) => void>('beforeSubmit')
const { success: showSuccess } = useToast()
const model = ref<Record<string, any>>({})

const formRef = ref()
const getForm = () => ({ ...model.value })
// 将schemas转换为options, 即表单项
const formOption = computed(() => schemasToOptions<Schema>(props.option.schemas, getForm))
function handleSubmit() {
  return formRef.value
    .validate()
    .then(({ valid, errors }) => {
      if (valid) {
        if (props.option.submitRequest) {
          return props.option.submitRequest(model.value).then((res) => {
            showSuccess({ msg: '提交成功' })
            props.option.afterSubmit?.(res)
          })
        } else {
          showSuccess({ msg: '提交成功' })
        }
      }
      return Promise.reject(errors)
    })
    .catch((error) => {
      console.error('error:', error)
      return Promise.reject(error)
    })
}
setBeforeSubmit?.(handleSubmit)
// 把formRef方法暴露出去
defineExpose({
  setForm: (data: any) => {
    model.value = data
  },
  getForm,
})
</script>
<script lang="ts">
export default {
  options: {
    styleIsolation: 'shared',
  },
}
</script>
<style lang="scss" scoped>
.form-custom {
  :deep(.wd-input__label) {
    display: none !important;
  }
  //:deep(.wd-input__value) {
  //  height: 2.5rem;
  //}
}
:deep(form) {
  @apply grid gap-2;
}
:deep(button) {
  width: 100%;
}
</style>
