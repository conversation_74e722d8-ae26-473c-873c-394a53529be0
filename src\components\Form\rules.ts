import { getPlaceholder } from './utils'
import { FormItemRule, FormItemRuleWithoutValidator } from 'wot-design-uni/components/wd-form/types'

const required = (field: Field, type: string, message: string) => {
  return { required: true, message: message || getPlaceholder(type, field) }
}

const phone = (field: Field, message: string) => {
  return {
    required: false,
    pattern: /^1[3-9]\d{9}$/,
    message: message || `请输入正确的${field.label}`,
  }
}

const password = (field: Field, message: string) => {
  // 大小写，最少8位
  return {
    required: false,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d]{8,}$/,
    message: message || `请输入至少8位，包含大小写字母和数字的${field.label}`,
  }
}

const minmax = (field: Field, min: number, max: number, message: string) => {
  return {
    required: false,
    pattern: new RegExp(`^.{${min},${max}}$`),
    message:
      message ||
      (!max ? `请输入至少${min}位${field.label}` : `请输入${min}到${max}位${field.label}`),
  }
}

const equal = (field: Field, equalFn: () => boolean, message: string) => {
  return {
    required: false,
    validator: (value: any) => equalFn() === value,
    message: message || `请输入与${field.label}相同的值`,
  }
}
// 必须以T开头，且可以有多个|和任意字符
type StartsWith<T extends string> = T | `${T}|${string}`

export type Rule =
  | StartsWith<'required'>
  | StartsWith<'phone'>
  | StartsWith<'password'>
  | StartsWith<'minmax'>
  | StartsWith<'equal'>
  | FormItemRule

export interface Field {
  [key: string]: any
}

export default { required, phone, password, minmax, equal }
