import rules, { Rule } from './rules'
import { FormRules, FormItemRule } from 'wot-design-uni/components/wd-form/types'
export const getPlaceholder = (type: string, props: { [key: string]: any }) => {
  switch (type) {
    case 'input':
    case 'input-number':
    case 'input-password':
    case 'input-otp':
    case 'input-code':
      return `请输入${props.label}`
    default:
      return `请选择${props.label}`
  }
}
export type Schema = {
  type: 'input' | 'input-number' | 'input-password' | 'input-otp' | 'input-code' | 'select' | 'slot'
  label: string
  prop: string
  // 是否显示在底部 提交按钮的下面
  bottom?: boolean
  rules?: Rule[]
  [key: string]: any
}

export const schemasToOptions = <T extends Schema>(
  schemas: T[],
  getForm: () => Record<string, any>,
) => {
  const formRules: FormRules = {}
  const options: { type: string; bottom?: boolean; props: any }[] = []
  for (const item of schemas) {
    const { type, bottom, prop, rules: itemRules, ...rest } = item
    formRules[prop] =
      item.rules?.map((rule: Rule | FormItemRule): FormItemRule => {
        // 表单可接受的rule 规则
        if (typeof rule === 'object') {
          return rule
        }
        const ruleList = rule.split('|')
        const message = ruleList.length > 1 ? ruleList.at(-1) : undefined
        switch (ruleList[0]) {
          case 'required':
            return rules.required(item, type, message)
          case 'phone':
            return rules.phone(item, message)
          case 'password':
            return rules.password(item, message)
          case 'minmax':
            if (ruleList.length > 2) {
              const [min, max] = ruleList.at(1).split(',').map(Number)
              return rules.minmax(item, min, max, message)
            } else throw new Error(`minmax rule error: ${rule}`)
          case 'equal':
            return rules.equal(
              item,
              () => getForm()[ruleList.at(1)],
              ruleList.length > 2 && message,
            )
        }
      }) || []
    options.push({ type, bottom, props: { prop, ...rest } })
  }
  return { options, rules: formRules }
}
// 分类下拉组件类型枚举
export enum SelectSlotType {
  DATE = 'date',
}
