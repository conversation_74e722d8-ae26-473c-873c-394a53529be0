<template>
  <wd-img
    :src="src || IconDefaultGoods"
    :enable-preview="enablePreview"
    radius="10"
    :width="width"
    :height="height"
  >
    <template #error>
      <wd-img :src="IconDefaultGoods" radius="10" :width="width" :height="height" />
    </template>
  </wd-img>
</template>

<script lang="ts" setup>
import IconDefaultGoods from '@/assets/IconDefaultGoods.png'

const props = withDefaults(
  defineProps<{ src: string; width?: string; height?: string; enablePreview: boolean }>(),
  {
    enablePreview: false,
    width: '88',
    height: '88',
  },
)
</script>
