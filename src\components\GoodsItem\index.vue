<template>
  <view class="border-box goods-item">
    <view v-if="layoutType === 1" class="layout layout1">
      <view class="tag">
        <view class="text">{{ info.boxName }}</view>
        <image class="tagbg" src="@/static/images/<EMAIL>" />
      </view>
      <view class="cover">
        <view class="sell-out" v-if="isSellOut">已售空</view>
        <image
          style="width: 100%; height: 100%"
          mode="scaleToFill"
          src="@/static/images/food.png"
        ></image>
      </view>

      <view class="info-wrap">
        <view class="desc">
          <wd-text
            :text="info.goodsName"
            size="28rpx"
            color="#1a1a1a"
            :lines="2"
            lineHeight="36rpx"
            bold
          ></wd-text>
        </view>
        <view class="wrap-group">
          <view class="wrap1">
            <view class="price">￥{{ info.price }}</view>
            <view class="stock">库存：{{ info.stock }}</view>
          </view>
          <view class="wrap2">
            <view class="add-icon" @click="onAdd">
              <wd-icon
                name="close-circle-filled"
                :color="isDisable ? '#fdd5c9' : '#f9744b'"
                size="48rpx"
              ></wd-icon>
            </view>
            <view @click="toCheckout" class="buy-btn" :class="{ disable: isDisable }">
              立即购买
            </view>
          </view>
        </view>
      </view>
    </view>

    <view v-if="[2, 3].includes(layoutType)" class="layout layout2">
      <view class="tag">
        <view class="text">{{ info.boxName }}</view>
        <image class="tagbg" src="@/static/images/<EMAIL>" />
      </view>
      <view class="cover size-24 rounded-xl overflow-hidden">
        <image
          style="width: 100%; height: 100%"
          mode="scaleToFill"
          src="@/static/images/food.png"
        ></image>
      </view>
      <view class="info-wrap">
        <wd-text
          :text="info.goodsName"
          size="28rpx"
          color="#1a1a1a"
          :lines="2"
          lineHeight="44rpx"
          bold
        ></wd-text>
        <!-- <view class="desc">杂酱炒面+油条+熟鸡蛋+纯牛奶</view> -->
        <view>
          <view v-if="layoutType === 2" class="item-wrap stock-wrap">
            <view class="stock">库存：{{ info.stock }}</view>
            <view class="tips" v-if="isSellOut">超出库存数量</view>
          </view>
          <view class="item-wrap price-control">
            <view class="price">￥{{ info.price }}</view>
            <view class="total" v-if="layoutType === 3">共{{ info.quantity }}件</view>
            <view class="control-wrap" v-if="layoutType === 2">
              <view class="sub" @click="onMinus">
                <wd-icon name="minus-circle" color="#f9744b" size="48rpx"></wd-icon>
              </view>
              <view class="num">{{ selected }}</view>
              <view class="add-icon" @click="onAdd">
                <wd-icon
                  name="close-circle-filled"
                  :color="isDisable ? '#fdd5c9' : '#f9744b'"
                  size="48rpx"
                ></wd-icon>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { useToast } from 'wot-design-uni'
const toast = useToast()

const props = defineProps({
  info: {
    type: Object,
    default: () => ({}),
  },
  layoutType: {
    type: Number,
    default: 1,
  },
})

const emit = defineEmits(['add', 'minus'])

const selected = ref(props.info.quantity || 0)
// const isSellOut = ref(false)
// const isDisable = ref(false)

const isSellOut = computed(() => {
  return selected.value >= props.info.stock
})
const isDisable = computed(() => {
  return isSellOut.value
})

watch(selected, (newVal, oldVal) => {
  if (newVal >= props.info.stock) {
    isSellOut.value = true
    isDisable.value = true
    toast.warning('超出库存数量')
  } else {
    isSellOut.value = false
    isDisable.value = false
  }
})
const onAdd = () => {
  if (isSellOut.value) return
  selected.value++
  emit('add')
}

const onMinus = () => {
  selected.value--
  emit('minus')
}

const toCheckout = () => {
  const data = JSON.parse(JSON.stringify(props.info))
  data.quantity = 1
  uni.setStorage({
    key: 'cart_goods',
    data: [data],
    success: function () {
      uni.navigateTo({
        url: `/pages/checkout/index?total=${1}&price=${props.info.price}`,
      })
    },
  })
}
</script>

<style lang="scss" scoped>
.layout {
  position: relative;
  display: flex;
  padding: 24rpx;
  border-radius: 24rpx;
  overflow: hidden;
  background: #ffffff;

  .tag {
    z-index: 9;
    position: absolute;
    top: 0;
    left: 0;
    width: 144rpx;
    height: 72rpx;

    .text {
      z-index: 9;
      position: absolute;
      top: 0;
      left: 0;
      width: 120rpx;
      height: 50rpx;
      line-height: 50rpx;
      font-size: 28rpx;
      color: #1a1a1a;
      text-align: center;
    }

    .tagbg {
      width: 144rpx;
      height: 72rpx;
    }
  }
}

.layout1 {
  flex-direction: column;
  width: 352rpx;
  height: 600rpx;

  .cover {
    position: relative;
    width: 304rpx;
    height: 304rpx;
    border-radius: 16rpx;
    overflow: hidden;

    .sell-out {
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      font-size: 36rpx;
      color: #ffffff;
      border-radius: 16rpx;
      background-color: rgba(0, 0, 0, 0.5);
    }
  }

  .info-wrap {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .desc {
    // font-weight: bold;
    // font-size: 28rpx;
    // color: #1a1a1a;
    // line-height: 44rpx;
    margin: 20rpx 0 28rpx;
  }

  .wrap1,
  .wrap2 {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .wrap1 {
    .price {
      font-weight: bold;
      font-size: 32rpx;
      color: #f9744b;
    }

    .stock {
      font-weight: 400;
      font-size: 28rpx;
      color: #656666;
    }
  }

  .wrap2 {
    margin-top: 24rpx;

    .buy-btn {
      width: 152rpx;
      height: 48rpx;
      line-height: 48rpx;
      text-align: center;
      font-size: 28rpx;
      color: #ffffff;
      background: #f9744b;
      border-radius: 24rpx;
    }
  }
}

.disable {
  opacity: 0.3;
}

.add-icon {
  transform: rotate(45deg);
}

.layout2 {
  height: 240rpx;

  .cover {
    // width: 192rpx;
    // height: 192rpx;
    // background: #f1f2f2;
    // border-radius: 24rpx 24rpx 24rpx 24rpx;
    background-color: #fffaf2;
  }

  .info-wrap {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin-left: 24rpx;

    .item-wrap,
    .control-wrap {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .stock-wrap {
      margin-bottom: 20rpx;

      .stock {
        font-size: 28rpx;
        color: #656666;
      }

      .tips {
        font-size: 28rpx;
        color: #f25555;
      }
    }

    .price-control {
      .price {
        font-weight: bold;
        font-size: 32rpx;
        color: #f9744b;
      }

      .total {
        font-size: 28rpx;
        color: #656666;
      }
    }

    .control-wrap {
      .num {
        margin: 0 20rpx;
      }
    }
  }
}
</style>
