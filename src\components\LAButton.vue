<template>
  <view class="la-btn-wrap" :class="[block ? 'is-block' : '']">
    <wd-button
      v-bind="$attrs"
      :block="block"
      :custom-class="[
        'la-button',
        isMaintenance ? 'is-maintenance' : 'is-user',
        `la-button--${size}`,
        isUser ? 'is-user' : '',
        isOps ? 'is-maintenance' : '',
        pale ? 'is-pale' : '',
        customClass,
      ]"
      @click.stop="handleClick"
      @getuserinfo="handleGetuserinfo"
      @contact="handleConcat"
      @getphonenumber="handleGetphonenumber"
      @error="handleError"
      @launchapp="handleLaunchapp"
      @opensetting="handleOpensetting"
      @chooseavatar="handleChooseavatar"
      @agreeprivacyauthorization="handleAgreePrivacyAuthorization"
    >
      <slot />
    </wd-button>
  </view>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store'
const isMaintenance = useUserStore().isMaintenance
console.log('isMaintenance', isMaintenance)

const props = withDefaults(
  defineProps<{
    size?: 'sm' | 'base' | 'lg' | 'xl'
    pale: boolean
    block: boolean
    isUser: boolean
    isOps: boolean
    customClass?: string
  }>(),
  {
    size: 'base',
    pale: false,
    block: false,
    isUser: false,
  },
)

const emit = defineEmits([
  'click',
  'getuserinfo',
  'contact',
  'getphonenumber',
  'error',
  'launchapp',
  'opensetting',
  'chooseavatar',
  'agreeprivacyauthorization',
])

function handleClick(event: any) {
  emit('click', event)
}
function handleGetuserinfo(event: any) {
  emit('getuserinfo', event.detail)
}
function handleConcat(event: any) {
  emit('contact', event.detail)
}
function handleGetphonenumber(event: any) {
  console.log('evt', event)

  emit('getphonenumber', event)
}
function handleError(event: any) {
  emit('error', event.detail)
}

function handleLaunchapp(event: any) {
  emit('launchapp', event.detail)
}
function handleOpensetting(event: any) {
  emit('opensetting', event.detail)
}
function handleChooseavatar(event: any) {
  emit('chooseavatar', event.detail)
}
function handleAgreePrivacyAuthorization(event: any) {
  emit('agreeprivacyauthorization', event.detail)
}
</script>

<script lang="ts">
export default {
  name: 'la-button',
  options: {
    addGlobalClass: true,
    virtualHost: true,
    styleIsolation: 'shared',
  },
}
</script>

<style scoped lang="scss">
$theme-color-user: #6d3202;
$theme-color-ops: #356afd;
$user-light-bg-color: rgba(109, 50, 2, 0.1);
$ops-light-bg-color: rgba(53, 106, 253, 0.1);

page {
  --theme-color-user: $theme-color-user;
  --theme-color-ops: $theme-color-user;
  --wot-color-theme: $theme-color-user;
  --wot-button-normal-bg: $theme-color-user;
  --wot-button-primary-bg-color: $theme-color-user;
}

// 用户端button圆角为高度的1/2，运维端圆角为高度的1/4
@mixin button-size($size, $radius-type: 'normal') {
  $heights: (
    'sm': 24px,
    'base': 32px,
    'lg': 40px,
    'xl': 48px,
  );

  @if map-has-key($heights, $size) {
    $height: map-get($heights, $size);
    height: $height !important;

    // 根据 $radius-type 参数计算圆角
    @if $radius-type == 'normal' {
      border-radius: $height / 4 !important;
    } @else if $radius-type == 'round' {
      border-radius: $height / 2 !important;
    } @else {
      @error "未知的圆角类型: #{$radius-type}";
    }
  } @else {
    @error "未知的按钮尺寸: #{$size}";
  }
}

.la-btn-wrap {
  display: flex;
  &:deep() {
    .la-button {
      &--base {
        @include button-size('base');
      }
      &--sm {
        @include button-size('sm');
      }
      &--lg {
        @include button-size('lg');
      }
      &--xl {
        @include button-size('xl');
      }
    }
    .la-button.is-primary {
      background: var(--theme-color-user, #6d3202);
    }
    .la-button.is-user {
      background: var(--theme-color-user, #6d3202);
    }
    .la-button.is-info {
      color: #1a1a1a;
      background: #f8f9fc !important;
    }
    .la-button.is-primary.is-plain {
      color: #6d3202;
      border-color: #6d3202;
      background: #fff;
    }
    .la-button.is-primary.is-maintenance.is-plain {
      color: var(--theme-color-ops, #356afd);
      border-color: var(--theme-color-ops, #356afd);
      background: #fff;
    }

    .la-button.is-plain {
      color: #fff;
    }
    .is-round.la-button {
      &--base {
        // @include button-size('base', 'round');
        border-radius: 16px !important;
      }
      &--sm {
        border-radius: 12px !important;
        // @include button-size('sm', 'round');
      }
      &--lg {
        border-radius: 20px !important;
        // @include button-size('lg', 'round');
      }
      &--xl {
        border-radius: 24px !important;
        // @include button-size('xl', 'round');
      }
    }
    .la-button.is-primary.is-maintenance {
      background: var(--theme-color-ops, #356afd);
    }
    .la-button.is-maintenance {
      background: var(--theme-color-ops, #356afd);
    }
    .la-button.is-primary.is-pale {
      color: #356afd;
      // background: #eaf0ff;
      background: rgba(53, 106, 253, 0.05);
    }
    .la-button.is-error.is-pale {
      color: #f25555;
      // background: #feeded;
      background: rgba(242, 85, 85, 0.05);
    }
  }
}
.is-block {
  display: block;
}
:deep(.wd-button) {
  &.is-medium {
    min-width: auto;
  }
}
</style>
