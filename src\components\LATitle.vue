<template>
  <view :style="{ marginBottom: `${bottom}rpx` }" class="word">
    <view class="title decoration">
      {{ title }}
      <!--默认插槽-->
    </view>
    <slot></slot>
  </view>
</template>

<script lang="ts" setup>
defineProps({
  title: {
    type: String,
    default: '标题',
  },
  bottom: {
    type: Number || String,
    default: 0,
  },
})
</script>

<style lang="scss" scoped>
.word {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.title {
  line-height: 100%;
  @apply font-bold gap-1 items-center flex text-[#1a1a1a] text-base;
  &.decoration::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 16px;
    background: rgba(53, 106, 253, 1);
    border-radius: 2px;
  }
}
</style>
