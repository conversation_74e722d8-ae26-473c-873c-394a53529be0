<template>
  <z-paging
    ref="paging"
    v-model="dataList"
    v-bind="{
      'show-loading-more-no-more-view': false,
      'inside-more': true,
      'auto-show-system-loading': autoShowSystemLoading,
      'hide-no-more-inside': true,
      'use-page-scroll': usePagescroll,
      'empty-view-img': '/static/images/noData.png',
      'empty-view-img-style': {
        width: '140rpx',
        height: '140rpx',
      },
      'empty-view-text': '暂无数据',
      ...$attrs,
    }"
    @query="queryList"
  >
    <template #top>
      <SearchForm
        v-if="search && typeof search === 'object'"
        v-model="searchParams"
        v-bind="search"
        @search="onSearchChange"
      >
        <template #dropMenu="{ item, slotRef }">
          <slot
            v-if="item.slot"
            :item="item"
            :value="searchParams[item.prop]"
            :onSearchChange="onSearchChange"
            :slotRef="slotRef"
            :name="item.prop"
            :actions="actions(slotRef, item.prop)"
          />
        </template>
      </SearchForm>
      <template v-if="tabs.length">
        <wd-tabs v-model="tabIndex" animated swipeable @change="onTabChange">
          <template v-for="item in tabs" :key="item.id">
            <wd-tab :title="item.name"></wd-tab>
          </template>
        </wd-tabs>
      </template>
      <slot name="top" />
    </template>
    <view v-for="(item, index) in dataList" :key="item.id" class="item">
      <slot
        :item="item"
        :list="dataList"
        :tab="tab"
        :reload="reloadPageList"
        :refresh="refresh"
      ></slot>
      <template v-if="index === dataList.length - 1 && bottomSpace">
        <view :style="{ height: bottomSpace }"></view>
      </template>
    </view>
    <template #bottom>
      <slot name="bottom" />
    </template>
  </z-paging>
</template>
<script lang="ts" setup>
import SearchForm from '@/components/Form/SearchForm.vue'
import { computed, nextTick, provide, ref, watch } from 'vue'
import type { SearchFormOption } from '@/components/Form/SearchForm.vue'

interface ListResponse {
  data?: any[]
  records?: any[]
}

// show-loading-more-no-more-view 是否显示没有更多数据的view
// to-bottom-loading-more-enabled 是否启用滑动到底部加载更多数据
// loading-more-enabled 是否开启加载更多功能(含滑动到底部加载更多数据和点击加载更多数据)
// inside-more 当分页未满一屏时，是否自动加载更多
/* auto-show-system-loading 是否自动显示系统Loading若开启则将在刷新列表时(调用reload、refresh时)显示，下拉刷新和滚动到底部加载更多不会显示 **/
// hide-no-more-inside '没有更多了'底部文字显示
/* fixed 若不需要z-paging铺满全屏，例如希望在弹窗内使用z-paging，请设置:fixed="false"，同时必须指定z-paging的高度，否则列表无法显示。若希望z-paging随着内容自动撑高，需设置:use-page-scroll="true"，无需修改fixed的配置，也无需指定z-paging高度) **/
const props = withDefaults(
  defineProps<{
    usePagescroll?: boolean
    // tab 列表
    tabs?: {
      id: number | string
      name: string
    }[]
    // 底部间距
    bottomSpace?: string
    // 搜索表单
    search?: SearchFormOption
    // 数据是否有分页
    isDataPage?: boolean
    // 加载中icon显示
    autoShowSystemLoading?: boolean
    // 请求接口
    requestApi: (params?: {
      current?: number
      size?: number
      tab?: { id: number | string; name: string }
    }) => Promise<ListResponse>
  }>(),
  {
    tabs: () => [],
    autoShowSystemLoading: true,
    isDataPage: true,
    usePagescroll: false,
  },
)

const emit = defineEmits(['tabChange'])
// 搜索值
const searchParams = ref({})
// 更新值的方法
const updateValue = (prop, newValue) => {
  searchParams.value[prop] = newValue
}
// searchParams = {reportPersonId: 'xxx'}
// value =  searchParams.reportPersonId  ref
onMounted(() => {
  // 取initialValue为默认搜索值
  if (props.search?.options) {
    props.search.options.forEach((item) => {
      if (item.initialValue) {
        searchParams.value[item.prop] = item.initialValue
      }
    })
  }
})
// 获取条件筛选的表单
const getFieldsValue = () => {
  return searchParams.value
}
// 设置条件筛选的表单
const setFieldsValue = (params, reload = true) => {
  searchParams.value = {
    ...searchParams.value,
    ...params,
  }
  if (reload) {
    onSearchChange()
  }
  // console.log(11111, searchParams.value, params)
}

const actions = (slotRef, prop) => {
  return {
    setFieldsValue,
    updateValue(value) {
      setFieldsValue({
        [prop]: value,
      })
      slotRef.close()
    },
  }
}
// z-paging 实例
const paging = ref(null)
// 数据列表
const dataList = ref([])
// 当前选中的 tab 索引
const tabIndex = ref(0)
// 当前选中的 tab
const tab = computed(() => props.tabs[tabIndex.value])
// 是否开始请求
const loading = ref(false)
const reloadPageList = () => {
  return new Promise((resolve) => {
    const stop = watch(loading, (val) => {
      if (!val) {
        resolve(true)
        stop()
      }
    })
    paging.value?.reload()
  })
}
// 页面刷新
const refresh = {
  success: (opt) => {
    // 刷新
    opt.eventChannel.on('refresh', () => {
      reloadPageList()
    })
  },
}
// 暴露一个刷新数据的方法
provide('reloadPageList', reloadPageList)
// tab 改变时刷新列表数据
const onTabChange = () => {
  searchParams.value = {}
  paging.value?.reload()
  nextTick(() => {
    emit('tabChange', tabIndex.value)
  })
}
// 搜索值改变时刷新列表数据
const onSearchChange = () => {
  paging.value?.reload()
}
const queryList = (current?: number, size?: number) => {
  // 这里的pageNo和pageSize会自动计算好，直接传给服务器即可
  // 这里的请求只是演示，请替换成自己的项目的网络请求，并在网络请求回调中通过paging.value.complete(请求回来的数组)将请求结果传给z-paging
  loading.value = true
  // const res = props.requestApi()
  // paging.value?.complete(res.data)
  // 不分页时请求不带分页参数,分页时请求带分页参数

  props
    .requestApi({
      ...(props.isDataPage ? { current, size } : {}),
      ...(tab?.value ? { tab: tab.value } : {}),
      ...searchParams.value,
    })
    .then((data: any) => {
      // 请勿在网络请求回调中给dataList赋值！！只需要调用complete就可以了
      paging.value?.complete(data.data?.records || data?.data || [])
    })
    .catch((res) => {
      console.error('数据请求失败：', res)
      // 如果请求失败写paging.value.complete(false)，会自动展示错误页面
      // 注意，每次都需要在catch中写这句话很麻烦，z-paging提供了方案可以全局统一处理
      // 在底层的网络请求抛出异常时，写uni.$emit('z-paging-error-emit');即可
      paging.value?.complete(false)
    })
    .finally(() => {
      loading.value = false
    })
}

// 暴露实例
defineExpose({
  paging,
  getFieldsValue,
  setFieldsValue,
})
</script>
<style lang="scss" scoped></style>
