<template>
  <view class="z-9999 custom-tabbar">
    <!--运维-->
    <wd-tabbar
      v-if="isMaintenance"
      @change="handleChange"
      fixed
      v-bind="$attrs"
      active-color="#356AFD"
      inactive-color="#ACB2C4"
      bordered
      safeAreaInsetBottom
      placeholder
    >
      <wd-tabbar-item name="maintenanceHome" title="工作台">
        <template #icon>
          <wd-img
            height="56rpx"
            width="56rpx"
            :src="attrs.modelValue === 'maintenanceHome' ? TabbarHome2Active : TabbarHome2"
          ></wd-img>
        </template>
      </wd-tabbar-item>
      <wd-tabbar-item name="maintenanceMap" title="车辆定位">
        <template #icon>
          <wd-img
            height="56rpx"
            width="56rpx"
            :src="attrs.modelValue === 'maintenanceMap' ? TabbarMapActive : TabbarMap"
          ></wd-img>
        </template>
      </wd-tabbar-item>
      <wd-tabbar-item name="maintenanceMy" title="我的">
        <template #icon>
          <wd-img
            height="56rpx"
            width="56rpx"
            :src="attrs.modelValue === 'maintenanceMy' ? TabbarMy2Active : TabbarMy2"
          ></wd-img>
        </template>
      </wd-tabbar-item>
    </wd-tabbar>
    <!--默认为用户-->
    <wd-tabbar
      v-else
      @change="handleChange"
      fixed
      v-bind="$attrs"
      active-color="#6D3202"
      inactive-color="#979998"
      bordered
      safeAreaInsetBottom
      placeholder
    >
      <wd-tabbar-item name="home" title="首页">
        <template #icon>
          <wd-img
            height="56rpx"
            width="56rpx"
            :src="attrs.modelValue === 'home' ? TabbarHomeActive : TabbarHome"
          ></wd-img>
        </template>
      </wd-tabbar-item>
      <wd-tabbar-item name="order" title="订单">
        <template #icon>
          <wd-img
            height="56rpx"
            width="56rpx"
            :src="attrs.modelValue === 'order' ? TabbarOrderActive : TabbarOrder"
          ></wd-img>
        </template>
      </wd-tabbar-item>
      <wd-tabbar-item name="my" title="我的">
        <template #icon>
          <wd-img
            height="56rpx"
            width="56rpx"
            :src="attrs.modelValue === 'my' ? TabbarMyActive : TabbarMy"
          ></wd-img>
        </template>
      </wd-tabbar-item>
    </wd-tabbar>
  </view>
</template>

<script setup lang="ts">
import { useUserStore, useGlobalStore } from '@/store'
import TabbarMy from '../assets/TabbarMy.svg?url'
import TabbarOrder from '../assets/TabbarOrder.svg?url'
import TabbarHome from '../assets/TabbarHome.svg?url'
import TabbarMyActive from '../assets/TabbarMyActive.svg?url'
import TabbarOrderActive from '../assets/TabbarOrderActive.svg?url'
import TabbarHomeActive from '../assets/TabbarHomeActive.svg?url'
import TabbarMy2 from '../assets/TabbarMy2.svg?url'
import TabbarMy2Active from '../assets/TabbarMy2Active.svg?url'
import TabbarHome2 from '../assets/TabbarHome2.svg?url'
import TabbarHome2Active from '../assets/TabbarHome2Active.svg?url'
import TabbarMap from '../assets/TabbarMap.svg?url'
import TabbarMapActive from '../assets/TabbarMapActive.svg?url'
import { useAttrs, getCurrentInstance } from 'vue'
import { getRect } from 'wot-design-uni/components/common/util'
import { storeToRefs } from 'pinia'

const { isMaintenance } = storeToRefs(useUserStore())
const globalStore = useGlobalStore()
const { setTabbarHeight } = globalStore

const instance = getCurrentInstance()
const attrs = useAttrs()

const urlMap = {
  // 消费者-首页
  home: '/pages/index/index',
  // 消费者-订单
  order: '/pages/order/index',
  // 消费者-我的
  my: '/pages/my/index',
  // 运维-我的
  maintenanceMy: '/pages-maintenance/my/index',
  // 运维-首页
  maintenanceHome: '/pages-maintenance/index/index',
  // 运维-车辆定位
  maintenanceMap: '/pages-maintenance/map/index',
}

// 将储存的激活的tabbar赋值给使得active并根据仓库的值保持响应式
function handleChange({ value }: { value: string }) {
  // 区分普通用户跳转
  if (isMaintenance.value) {
    uni.redirectTo({
      url: urlMap[value],
      // animationDuration: 0,
    })
  } else {
    uni.switchTab({
      url: urlMap[value],
    })
  }
}

function storeTabbarHeight() {
  getRect('.custom-tabbar', false, instance).then((rect) => {
    if (rect?.height) {
      setTabbarHeight(rect.height)
    }
  })
}

onMounted(() => {
  if (globalStore.tabbarHeight) return
  const timer = setInterval(() => {
    if (globalStore.tabbarHeight) {
      clearInterval(timer)
    }
    storeTabbarHeight()
  }, 100)
})
</script>
