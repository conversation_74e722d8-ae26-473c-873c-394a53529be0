<template>
  <view
    style="padding-bottom: env(safe-area-inset-bottom)"
    class="camera-sheet-class"
    :class="customClass"
  >
    <view @click="toggle">
      <slot></slot>
    </view>
    <wd-action-sheet v-model="show" title="照片拍摄" @close="close">
      <view class="px-4 w-full box-border flex flex-col">
        <view class="flex w-full justify-between">
          <view class="flex items-center gap-2">
            <text class="text-sm text-gray-900 font-bold">照片拍摄</text>

            <text class="text-sm text-gray-500" v-if="min && max">
              {{ `至少${min}张，最多${max}张` }}
            </text>
          </view>
          <tip-container-page />
        </view>
        <scroll-view class="w-full max-h-50" scroll-y>
          <view class="w-full mt-4">
            <wd-upload
              v-model:file-list="fileList"
              :source-type="allowAlbum ? ['album', 'camera'] : ['camera']"
              :action="FILE_UPLOAD_URL"
              accept="image"
              image-mode="aspectFill"
              prop="imagePath"
              @success="handleFileSuccess"
              :before-choose="beforeChoose"
            >
              <view
                class="flex flex-col items-center justify-center w-20 h-20 rounded-lg gap-2"
                style="border: 1px solid #c9cad9"
              >
                <wd-icon name="camera" size="30" color="#c9cad9"></wd-icon>
                <text class="text-sm text-gray-500">点击拍摄</text>
              </view>
            </wd-upload>
          </view>
        </scroll-view>

        <LAButton
          type="primary"
          :round="false"
          custom-class="mt-4"
          size="xl"
          block
          @click="handleConfirm"
        >
          确认
        </LAButton>
      </view>
    </wd-action-sheet>
  </view>
</template>
<script setup lang="ts">
import type { UploadFile } from 'wot-design-uni/components/wd-upload/types'

import { FILE_UPLOAD_URL } from '@/service/api'
import { useGlobalStore } from '@/store'
import TipContainerPage from '@/components/tip-container-page.vue'
import { useToast } from 'wot-design-uni'
const toast = useToast()

const { setJumpSelecting } = useGlobalStore()

const props = withDefaults(
  defineProps<{
    // 是否允许选择文件
    allowAlbum?: boolean
    min?: number
    max?: number
    modelValue: string[]
    customClass?: string
  }>(),
  {
    // 默认允许相册
    allowAlbum: true,
  },
)

const emit = defineEmits<{
  (e: 'update:modelValue', value: string[]): void
}>()

const fileList = ref<UploadFile[]>([])

watch(
  () => props.modelValue,
  (value) => {
    fileList.value = value?.map((item) => ({ url: item }))
  },
  { immediate: true, deep: true },
)
const beforeChoose = ({ fileList, resolve }) => {
  setJumpSelecting(false)
  resolve(true)
}
// 图片上传成功
const handleFileSuccess = (res: any) => {
  const resData = JSON.parse(res.file.response)
  if (!resData.success) {
    return toast.show(resData.message)
  }
  res.file.url = resData.data.fullPath
}

const show = ref(false)
const close = () => {
  show.value = false
}
const toggle = () => {
  show.value = true
}
const handleConfirm = () => {
  if (props.min && fileList.value.length < props.min) {
    return toast.info(`请至少拍摄${props.min}张照片`)
  } else if (props.max && fileList.value.length > props.max) {
    return toast.info(`最多拍摄${props.max}张照片`)
  }
  // 抛出点击确定的
  emit(
    'update:modelValue',
    fileList.value.map((item) => {
      return item.url
    }),
  )
  setJumpSelecting(true)

  show.value = false
}
</script>
<style lang="scss" scoped>
:deep(.wd-upload__evoke-slot) {
  order: -1;
  margin-right: 10px;
}
</style>
