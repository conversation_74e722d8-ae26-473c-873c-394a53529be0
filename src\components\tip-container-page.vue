<template>
  <view>
    <view @click="handleClick">
      <slot>
        <view class="flex items-center gap-1 text-gray-500">
          <wd-icon name="help-circle" custom-class="-mt-.5" size="16px"></wd-icon>
          <text class="text-sm">拍摄说明</text>
        </view>
      </slot>
    </view>
    <wd-action-sheet
      custom-header-class="border-b border-b-solid border-b-color-[#EFF1F5] !p-0"
      custom-class="h-[90vh]"
      v-model="showPopup"
      :title="title"
      @close="handleClose"
    >
      <slot name="content">
        <view class="p-2">
          <view class="text-lg fw-900 mb-2">拍摄说明</view>
          <view>拍摄角度、拍摄张数</view>
        </view>
      </slot>
    </wd-action-sheet>
  </view>
</template>
<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    title?: string
    showBtn?: boolean
  }>(),
  {
    title: '拍摄说明',
    showBtn: true,
  },
)
onMounted(() => {
  showPopup.value = false
})
const showPopup = ref(false)
const handleClose = () => {
  showPopup.value = false
}
const handleClick = () => {
  showPopup.value = true
}
// 暴露给父组件的方法
defineExpose({
  handleClick,
  handleClose,
})
</script>
<style lang="scss" scoped></style>
