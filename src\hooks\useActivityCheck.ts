// 检查用户是否离线, 离线则重置页面
import { useUserStore, useBaseInfoStore } from '@/store'
import { useCart } from '@/pages/goods-menu/useCart'
import { storeToRefs } from 'pinia'
import { onUnmounted } from 'vue'
import { refreshCarWaitTime } from '@/service/api'

export const useActivityCheck = () => {
  // 检查间隔时间
  const CHECK_DURATION = 1000 * 30
  // 超时认为离线
  const OFFLINE_DURATION = 1000 * 60 * 3

  const lastActivityTime = ref(0)
  const userStore = useUserStore()
  const { clearCart } = useCart()
  const activityStatus = ref(false)
  const { reset } = useBaseInfoStore()
  const { referer, scanParams } = storeToRefs(useBaseInfoStore())
  let timer = null

  const countdown = computed(() => {
    return ~~Math.max(0, OFFLINE_DURATION - (Date.now() - lastActivityTime.value)) / 1000
  })

  const resetStatus = () => {
    reset()
    clearCart()
    console.log('resetStatus relaunch')
    uni.reLaunch({ url: '/pages/index/index' })
    clearInterval(timer)
  }

  const stop = watch(
    () => [userStore.isMaintenance, referer.value],
    ([isMaintenance, referer]) => {
      // 只针对扫码购买的用户进行检测
      if (isMaintenance || referer !== 'scan') {
        clearInterval(timer)
      } else {
        activityStatus.value = true
        lastActivityTime.value = Date.now()

        timer = setInterval(() => {
          if (activityStatus.value) {
            activityStatus.value = false
            lastActivityTime.value = Date.now()
            refreshCarWaitTime({
              carId: scanParams.value.carId,
              timestamp: scanParams.value.timestamp,
              isLocal: 0,
            }).then((res) => {
              if (!res.data) resetStatus()
            })
          } else {
            if (Date.now() - lastActivityTime.value > OFFLINE_DURATION) {
              // resetStatus()
            }
          }
        }, CHECK_DURATION)
      }
    },
    { immediate: true },
  )

  onUnmounted(() => {
    clearInterval(timer)
    stop()
  })

  return {
    countdown,
    addEvent: () => {
      activityStatus.value = true
    },
  }
}
