import { ref, onMounted } from 'vue'

export default function useFetchData<T>(fetchFunction: () => Promise<T>) {
  const data = ref<T>()
  const fetchData = async () => {
    try {
      const response: any = await fetchFunction()
      data.value = response?.data?.records || response?.data || []
    } catch (error) {
      console.error('Error fetching data:', error)
    }
  }

  // 在组件挂载时自动获取数据
  onMounted(fetchData)

  return {
    data,
    fetchData, // 提供手动重新获取数据的方法
  }
}
