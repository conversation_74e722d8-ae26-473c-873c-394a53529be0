import { ref, computed, nextTick, type Ref, type ComputedRef } from 'vue'

// 地图标记点类型
export interface MapMarker {
  id: string | number
  longitude: number
  latitude: number
  iconPath?: string
  width?: number
  height?: number
  callout?: {
    content: string
    color?: string
    fontSize?: number
    borderRadius?: number
    bgColor?: string
    padding?: number
    display?: 'ALWAYS' | 'BYCLICK'
  }
  customCallout?: {
    display?: 'ALWAYS' | 'BYCLICK'
    anchorX?: number
    anchorY?: number
  }
}

// 地图路线类型
export interface MapPolyline {
  points: Array<{
    longitude: number
    latitude: number
  }>
  color?: string
  width?: number
  dottedLine?: boolean
  arrowLine?: boolean
  borderColor?: string
  borderWidth?: number
}

// 地图多边形类型
export interface MapPolygon {
  id: string | number
  points: Array<{
    longitude: number
    latitude: number
  }>
  item: any
  polygonId: string
  strokeWidth?: number
  strokeColor?: string
  fillColor?: string
  zIndex?: number
  level?: string
  dashArray?: number[]
  // 自定义属性，用于支持类似 callout 的功能
  callout?: {
    content: string
    color?: string
    fontSize?: number
    borderRadius?: number
    bgColor?: string
    padding?: number
    display?: 'ALWAYS' | 'BYCLICK'
  }
  // 隐藏的 marker 属性
  markerIconPath?: string
}

// 当前位置类型
export interface CurrentLocation {
  longitude: number
  latitude: number
  accuracy: number
}

// 地图配置选项类型
export interface MapOptions {
  mapId?: string
  longitude?: number
  latitude?: number
  scale?: number
  autoLocation?: boolean
  showLocation?: boolean
}

// 地图事件类型
export interface MapEventDetail {
  longitude?: number
  latitude?: number
  markerId?: string | number
  scale?: number
  centerLocation?: {
    longitude: number
    latitude: number
  }
  type?: string
}

// 事件处理器类型
export interface EventHandlers {
  onMarkerTap?: (event: MapEventDetail, marker?: MapMarker) => void
  onCalloutTap?: (event: MapEventDetail, marker?: MapMarker) => void
  onMapTap?: (event: MapEventDetail) => void
  onRegionChange?: (event: MapEventDetail) => void
  onMapUpdated?: (event: MapEventDetail) => void
}

// useMap 返回类型
export interface UseMapReturn {
  // 响应式数据
  centerLongitude: Ref<number>
  centerLatitude: Ref<number>
  mapScale: Ref<number>
  currentLocation: Ref<CurrentLocation | null>
  loading: Ref<boolean>
  loadingText: Ref<string>
  markers: Ref<MapMarker[]>
  polylines: Ref<MapPolyline[]>
  polygons: Ref<MapPolygon[]>
  allMarkers: ComputedRef<MapMarker[]>
  mapContext: Ref<any>

  // 方法
  initMap: () => void
  getCurrentLocation: () => Promise<CurrentLocation>
  moveToLocation: (longitude: number, latitude: number, scale?: number) => void
  zoomIn: () => void
  zoomOut: () => void
  includePoints: (points: Array<{ longitude: number; latitude: number }>, padding?: number) => void
  addMarker: (marker: MapMarker) => void
  removeMarker: (markerId: string | number) => void
  updateMarker: (markerId: string | number, updates: Partial<MapMarker>) => void
  clearMarkers: () => void
  addPolyline: (polyline: MapPolyline) => void
  removePolyline: (index: number) => void
  clearPolylines: () => void
  addPolygon: (polygon: MapPolygon) => void
  removePolygon: (polygonId: string | number) => void
  updatePolygon: (polygonId: string | number, updates: Partial<MapPolygon>) => void
  clearPolygons: () => void
  getPolygonCenter: (points: Array<{ longitude: number; latitude: number }>) => {
    longitude: number
    latitude: number
  }
  clearAll: () => void
  getCenterLocation: () => { longitude: number; latitude: number }
  getMapBounds: () => Promise<any>
  createEventHandlers: (customHandlers?: EventHandlers) => {
    onMarkerTap: (e: MapEventDetail) => { event: MapEventDetail; marker?: MapMarker }
    onCalloutTap: (e: MapEventDetail) => { event: MapEventDetail; marker?: MapMarker }
    onMapTap: (e: MapEventDetail) => { longitude?: number; latitude?: number }
    onRegionChange: (e: MapEventDetail) => MapEventDetail
    onMapUpdated: (e: MapEventDetail) => MapEventDetail
  }
  init: () => void
}

/**
 * 地图相关的 hooks
 * @param options 配置选项
 * @returns 返回地图相关的响应式数据和方法
 */
export function useMap(options: MapOptions = {}): UseMapReturn {
  const {
    mapId = 'map',
    longitude: initLongitude = 116.397428,
    latitude: initLatitude = 39.90923,
    scale: initScale = 16,
    autoLocation = true,
  } = options

  // 响应式数据
  const centerLongitude = ref<number>(initLongitude)
  const centerLatitude = ref<number>(initLatitude)
  const mapScale = ref<number>(initScale)
  const currentLocation = ref<CurrentLocation | null>(null)
  const loading = ref<boolean>(false)
  const loadingText = ref<string>('定位中...')
  const mapContext = ref<any>(null)
  const markers = ref<MapMarker[]>([])
  const polylines = ref<MapPolyline[]>([])
  const polygons = ref<MapPolygon[]>([])

  // 计算属性 - 合并所有标记点（包括多边形的隐藏标记点）
  const allMarkers = computed<MapMarker[]>(() => {
    const polygonMarkers = polygons.value
      .filter((polygon) => polygon.callout)
      .map((polygon) => {
        const center = getPolygonCenter(polygon.points)
        return {
          id: -polygon.id,
          longitude: +center.longitude,
          latitude: +center.latitude,
          iconPath: polygon.markerIconPath || '',
          width: 0, // 隐藏标记点
          height: 0, // 隐藏标记点
          callout: polygon.callout,
        } as MapMarker
      })

    return [...markers.value, ...polygonMarkers]
  })

  // 初始化地图
  const initMap = (): void => {
    mapContext.value = uni.createMapContext(mapId)
  }

  // 获取当前位置
  const getCurrentLocation = (): Promise<CurrentLocation> => {
    loading.value = true
    loadingText.value = '定位中...'

    return new Promise((resolve, reject) => {
      uni.getLocation({
        type: 'gcj02', // 使用国测局坐标
        success: (res: any) => {
          const location: CurrentLocation = {
            longitude: res.longitude,
            latitude: res.latitude,
            accuracy: res.accuracy,
          }

          currentLocation.value = location

          // 移动地图中心到当前位置
          centerLongitude.value = res.longitude
          centerLatitude.value = res.latitude

          loading.value = false
          resolve(location)
        },
        fail: (err: any) => {
          console.error('获取位置失败:', err)
          uni.showToast({
            title: '定位失败，请检查定位权限',
            icon: 'none',
            duration: 2000,
          })
          loading.value = false
          reject(err)
        },
      })
    })
  }

  // 移动到指定位置
  const moveToLocation = (
    longitude: number,
    latitude: number,
    scale: number = mapScale.value,
  ): void => {
    centerLongitude.value = longitude
    centerLatitude.value = latitude
    mapScale.value = scale

    if (mapContext.value) {
      mapContext.value.moveToLocation({
        longitude,
        latitude,
        scale,
      })
    }
  }

  // 放大地图
  const zoomIn = (): void => {
    if (mapContext.value) {
      mapContext.value.getScale({
        success: (res: any) => {
          const newScale = Math.min(res.scale + 2, 20)
          moveToLocation(centerLongitude.value, centerLatitude.value, newScale)
        },
      })
    }
  }

  // 缩小地图
  const zoomOut = (): void => {
    if (mapContext.value) {
      mapContext.value.getScale({
        success: (res: any) => {
          const newScale = Math.max(res.scale - 2, 5)
          moveToLocation(centerLongitude.value, centerLatitude.value, newScale)
        },
      })
    }
  }

  // 包含所有标记点的视野
  const includePoints = (
    points: Array<{ longitude: number; latitude: number }>,
    padding: number = 20,
  ): void => {
    if (mapContext.value && points.length > 0) {
      mapContext.value.includePoints({
        points,
        padding: [padding, padding, padding, padding],
      })
    }
  }

  // 添加标记点
  const addMarker = (marker: MapMarker): void => {
    markers.value.push(marker)
  }

  // 删除标记点
  const removeMarker = (markerId: string | number): void => {
    const index = markers.value.findIndex((marker) => marker.id === markerId)
    if (index > -1) {
      markers.value.splice(index, 1)
    }
  }

  // 更新标记点
  const updateMarker = (markerId: string | number, updates: Partial<MapMarker>): void => {
    const marker = markers.value.find((m) => m.id === markerId)
    if (marker) {
      Object.assign(marker, updates)
    }
  }

  // 清空所有标记点
  const clearMarkers = (): void => {
    markers.value = []
  }

  // 添加路线
  const addPolyline = (polyline: MapPolyline): void => {
    polylines.value.push(polyline)
  }

  // 删除路线
  const removePolyline = (index: number): void => {
    if (index >= 0 && index < polylines.value.length) {
      polylines.value.splice(index, 1)
    }
  }

  // 清空所有路线
  const clearPolylines = (): void => {
    polylines.value = []
  }

  // 计算多边形中心点
  const getPolygonCenter = (
    points: Array<{ longitude: number; latitude: number }>,
  ): { longitude: number; latitude: number } => {
    if (points.length === 0) {
      return { longitude: 0, latitude: 0 }
    }

    let totalLongitude = 0
    let totalLatitude = 0

    points.forEach((point) => {
      totalLongitude += point.longitude
      totalLatitude += point.latitude
    })

    return {
      longitude: totalLongitude / points.length,
      latitude: totalLatitude / points.length,
    }
  }

  // 添加多边形
  const addPolygon = (polygon: MapPolygon): void => {
    polygons.value.push(polygon)
  }

  // 删除多边形
  const removePolygon = (polygonId: string | number): void => {
    const index = polygons.value.findIndex((polygon) => polygon.id === polygonId)
    if (index > -1) {
      polygons.value.splice(index, 1)
    }
  }

  // 更新多边形
  const updatePolygon = (polygonId: string | number, updates: Partial<MapPolygon>): void => {
    const polygon = polygons.value.find((p) => p.id === polygonId)
    if (polygon) {
      Object.assign(polygon, updates)
    }
  }

  // 清空所有多边形
  const clearPolygons = (): void => {
    polygons.value = []
  }

  // 清空所有数据
  const clearAll = (): void => {
    clearMarkers()
    clearPolylines()
    clearPolygons()
  }

  // 获取地图中心点
  const getCenterLocation = (): { longitude: number; latitude: number } => {
    return {
      longitude: centerLongitude.value,
      latitude: centerLatitude.value,
    }
  }

  // 获取地图边界
  const getMapBounds = (): Promise<any> => {
    return new Promise((resolve, reject) => {
      if (mapContext.value) {
        mapContext.value.getRegion({
          success: resolve,
          fail: reject,
        })
      } else {
        reject(new Error('地图未初始化'))
      }
    })
  }

  // 地图事件处理器
  const createEventHandlers = (customHandlers: EventHandlers = {}) => {
    return {
      // 标记点点击事件
      onMarkerTap: (e: { detail: MapEventDetail }) => {
        const { markerId } = e.detail
        const marker = allMarkers.value.find((m) => m.id === markerId)

        if (customHandlers.onMarkerTap) {
          customHandlers.onMarkerTap(e.detail, marker)
        }

        return { event: e.detail, marker }
      },

      // 气泡点击事件
      onCalloutTap: (e: { detail: MapEventDetail }) => {
        const { markerId } = e.detail
        const marker = allMarkers.value.find((m) => m.id === markerId)
        if (customHandlers.onCalloutTap) {
          customHandlers.onCalloutTap(e.detail, marker)
        }
        return { event: e.detail, marker }
      },

      // 地图点击事件
      onMapTap: (e: { detail: MapEventDetail }) => {
        const { longitude, latitude } = e.detail

        if (customHandlers.onMapTap) {
          customHandlers.onMapTap(e.detail)
        }

        return { longitude, latitude }
      },

      // 地图区域改变事件
      onRegionChange: (e: { detail: MapEventDetail }) => {
        if (e.detail.type === 'end') {
          centerLongitude.value = e.detail.centerLocation?.longitude || centerLongitude.value
          centerLatitude.value = e.detail.centerLocation?.latitude || centerLatitude.value
          mapScale.value = e.detail.scale || mapScale.value
        }

        if (customHandlers.onRegionChange) {
          customHandlers.onRegionChange(e.detail)
        }

        return e.detail
      },

      // 地图更新事件
      onMapUpdated: (e: { detail: MapEventDetail }) => {
        if (customHandlers.onMapUpdated) {
          customHandlers.onMapUpdated(e.detail)
        }
        return e.detail
      },
    }
  }

  // 生命周期初始化
  const init = (): void => {
    nextTick(() => {
      initMap()
      if (autoLocation) {
        getCurrentLocation().catch((err) => {
          console.warn('自动定位失败:', err)
        })
      }
    })
  }

  return {
    // 响应式数据
    centerLongitude,
    centerLatitude,
    mapScale,
    currentLocation,
    loading,
    loadingText,
    markers,
    polylines,
    polygons,
    allMarkers,
    mapContext,

    // 方法
    initMap,
    getCurrentLocation,
    moveToLocation,
    zoomIn,
    zoomOut,
    includePoints,
    addMarker,
    removeMarker,
    updateMarker,
    clearMarkers,
    addPolyline,
    removePolyline,
    clearPolylines,
    addPolygon,
    removePolygon,
    updatePolygon,
    clearPolygons,
    getPolygonCenter,
    clearAll,
    getCenterLocation,
    getMapBounds,
    createEventHandlers: createEventHandlers as any,
    init,
  }
}
