import { ref, type Ref } from 'vue'
import type { UseMapReturn, MapMarker, MapPolyline } from './useMap'

// 预设位置类型
export interface PresetLocation {
  id: number
  name: string
  longitude: number
  latitude: number
  scale: number
}

// 地图控件配置类型
export interface MapControl {
  id: string
  position: {
    left?: number
    right?: number
    top?: number
    bottom?: number
  }
  iconPath: string
  clickable: boolean
}

// 控件事件类型
export interface ControlEvent {
  detail: {
    controlId: string
  }
}

// useMapControls 返回类型
export interface UseMapControlsReturn {
  // 响应式数据
  showControls: Ref<boolean>
  showLocationBtn: Ref<boolean>
  showZoomBtns: Ref<boolean>
  presetLocations: Ref<PresetLocation[]>

  // 方法
  addRandomMarker: () => MapMarker
  drawRoute: () => MapPolyline | false
  includeAllPoints: () => boolean
  moveToPresetLocation: (location: PresetLocation) => void
  handleClearMarkers: () => void
  handleClearRoutes: () => void
  handleClearAll: () => void
  handleGetCurrentLocation: () => Promise<any>
  createMapControls: () => MapControl[]
  handleControlTap: (e: ControlEvent) => void
}

/**
 * 地图控件相关的 hooks
 * @param mapHooks useMap 返回的对象
 * @returns 返回控件相关的数据和方法
 */
export function useMapControls(mapHooks: UseMapReturn): UseMapControlsReturn {
  const {
    getCurrentLocation,
    zoomIn,
    zoomOut,
    includePoints,
    addMarker,
    clearMarkers,
    addPolyline,
    clearPolylines,
    moveToLocation,
    markers,
    centerLongitude,
    centerLatitude,
  } = mapHooks

  // 控件显示状态
  const showControls = ref<boolean>(true)
  const showLocationBtn = ref<boolean>(true)
  const showZoomBtns = ref<boolean>(true)

  // 预设位置
  const presetLocations = ref<PresetLocation[]>([
    { id: 1, name: '北京', longitude: 116.397428, latitude: 39.90923, scale: 12 },
    { id: 2, name: '上海', longitude: 121.473701, latitude: 31.230416, scale: 12 },
    { id: 3, name: '广州', longitude: 113.264385, latitude: 23.129163, scale: 12 },
    { id: 4, name: '深圳', longitude: 114.085947, latitude: 22.547, scale: 12 },
  ])

  // 添加随机标记点
  const addRandomMarker = (): MapMarker => {
    const randomId = Date.now()
    const randomLng = centerLongitude.value + (Math.random() - 0.5) * 0.01
    const randomLat = centerLatitude.value + (Math.random() - 0.5) * 0.01

    const newMarker: MapMarker = {
      id: randomId,
      longitude: randomLng,
      latitude: randomLat,
      iconPath: '/static/images/marker-green.png',
      width: 25,
      height: 25,
      callout: {
        content: `标记点 ${randomId}`,
        color: '#ffffff',
        fontSize: 11,
        borderRadius: 4,
        bgColor: '#2ed573',
        padding: 6,
        display: 'BYCLICK',
      },
    }

    addMarker(newMarker)

    uni.showToast({
      title: '已添加标记点',
      icon: 'success',
      duration: 1000,
    })

    return newMarker
  }

  // 绘制连接所有标记点的路线
  const drawRoute = (): MapPolyline | false => {
    if (markers.value.length < 2) {
      uni.showToast({
        title: '至少需要2个标记点',
        icon: 'none',
        duration: 2000,
      })
      return false
    }

    const points = markers.value.map((marker) => ({
      longitude: marker.longitude,
      latitude: marker.latitude,
    }))

    const newPolyline: MapPolyline = {
      points,
      color: '#007aff',
      width: 4,
      dottedLine: false,
      arrowLine: true,
      borderColor: '#ffffff',
      borderWidth: 1,
    }

    // 清空之前的路线，只保留一条
    clearPolylines()
    addPolyline(newPolyline)

    uni.showToast({
      title: '路线已绘制',
      icon: 'success',
      duration: 1000,
    })

    return newPolyline
  }

  // 包含所有标记点的视野
  const includeAllPoints = (): boolean => {
    if (markers.value.length === 0) {
      uni.showToast({
        title: '没有标记点',
        icon: 'none',
        duration: 1500,
      })
      return false
    }

    const points = markers.value.map((marker) => ({
      longitude: marker.longitude,
      latitude: marker.latitude,
    }))

    includePoints(points, 50)

    uni.showToast({
      title: '视野已调整',
      icon: 'success',
      duration: 1000,
    })

    return true
  }

  // 移动到预设位置
  const moveToPresetLocation = (location: PresetLocation): void => {
    moveToLocation(location.longitude, location.latitude, location.scale)

    uni.showToast({
      title: `已移动到${location.name}`,
      icon: 'success',
      duration: 1000,
    })
  }

  // 清空标记点
  const handleClearMarkers = (): void => {
    clearMarkers()
    uni.showToast({
      title: '已清空标记点',
      icon: 'success',
      duration: 1000,
    })
  }

  // 清空路线
  const handleClearRoutes = (): void => {
    clearPolylines()
    uni.showToast({
      title: '已清空路线',
      icon: 'success',
      duration: 1000,
    })
  }

  // 清空所有
  const handleClearAll = (): void => {
    clearMarkers()
    clearPolylines()
    uni.showToast({
      title: '已清空所有数据',
      icon: 'success',
      duration: 1000,
    })
  }

  // 获取当前位置（带提示）
  const handleGetCurrentLocation = async (): Promise<any> => {
    try {
      const location = await getCurrentLocation()
      uni.showToast({
        title: '定位成功',
        icon: 'success',
        duration: 1500,
      })
      return location
    } catch (error) {
      uni.showToast({
        title: '定位失败',
        icon: 'error',
        duration: 2000,
      })
      throw error
    }
  }

  // 创建地图控件配置
  const createMapControls = (): MapControl[] => {
    return [
      {
        id: 'location',
        position: { left: 20, bottom: 100 },
        iconPath: '/static/images/location.png',
        clickable: true,
      },
      {
        id: 'zoomIn',
        position: { right: 20, top: 100 },
        iconPath: '/static/images/zoom-in.png',
        clickable: true,
      },
      {
        id: 'zoomOut',
        position: { right: 20, top: 150 },
        iconPath: '/static/images/zoom-out.png',
        clickable: true,
      },
    ]
  }

  // 控件点击处理
  const handleControlTap = (e: ControlEvent): void => {
    const { controlId } = e.detail

    switch (controlId) {
      case 'location':
        handleGetCurrentLocation()
        break
      case 'zoomIn':
        zoomIn()
        break
      case 'zoomOut':
        zoomOut()
        break
      default:
        console.log('未知控件:', controlId)
    }
  }

  return {
    // 响应式数据
    showControls,
    showLocationBtn,
    showZoomBtns,
    presetLocations,

    // 方法
    addRandomMarker,
    drawRoute,
    includeAllPoints,
    moveToPresetLocation,
    handleClearMarkers,
    handleClearRoutes,
    handleClearAll,
    handleGetCurrentLocation,
    createMapControls,
    handleControlTap,
  }
}
