import { useMessage as useWotMessage, useToast } from 'wot-design-uni'
import { useStyleTag } from '@vueuse/core'

type MessageProps = {
  title?: string
  msg?: string
  fetch?: () => Promise<any>
  onSubmitSuccess?: () => void
  theme?: 'dark' | 'light'
}
export function useMessage(out?: MessageProps) {
  // 提示弹窗
  const message = useWotMessage()
  const toast = useToast()
  const click = (inner?: MessageProps) => {
    const { title, msg, fetch, onSubmitSuccess, theme = 'dark' } = { ...out, ...inner }
    if (theme === 'dark') {
      const darkStyle = `.wd-popup { --wot-dark-background2: #3b3ca7; --wot-dark-background4: #3B3CA7; --wot-card-bg: #191A47 }`
      useStyleTag(darkStyle, { id: 'theme-style' })
    } else {
      const lightStyle = ``
      useStyleTag(lightStyle, { id: 'theme-style' })
    }
    message
      .confirm({
        title,
        msg,
        cancelButtonProps: {
          round: false,
          plain: true,
          hairline: true,
          customStyle: 'border: 1px solid #20218E;',
        },
        confirmButtonProps: {
          round: false,
          customStyle:
            'background: linear-gradient(180deg, #3b3ca7 0%, #356afd 100%); border: 1px solid #20218E;',
        },
        beforeConfirm: async ({ resolve }) => {
          if (fetch) {
            await fetch?.()
              .then((res) => {
                if (res.statusCode === 101) {
                  resolve(true)
                  toast.success('操作成功')
                  onSubmitSuccess?.()
                } else {
                  resolve(false)
                  toast.error('操作失败')
                }
              })
              .catch((error) => {
                console.log('error', error)
              })
          }
          resolve(true)
        },
      })
      .then(() => {
        // refreshData()
        // props.onSubmitSuccess?.()
      })
      .catch((error) => {
        console.log(error)
      })
  }
  return click
}
