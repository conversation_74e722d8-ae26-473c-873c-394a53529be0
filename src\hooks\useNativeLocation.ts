// src/hooks/useNativeLocation.ts
import { shallowRef, type ShallowRef, toRaw } from 'vue'
import { useGlobalStore } from '@/store'

type Location = UniApp.GetLocationSuccess

interface UseNativeLocationOptions {
  showModalWhenDenied?: boolean
  autoInitial?: boolean
}
export default function useNativeLocation(options?: UseNativeLocationOptions) {
  options = { showModalWhenDenied: true, autoInitial: true, ...options }
  const { setJumpSelecting, setPermission } = useGlobalStore()

  // 初始化设置
  const location: ShallowRef<Location | null> = shallowRef(null)
  const error = shallowRef('')
  const isLoading = shallowRef(false)
  const readyPromise: Promise<void> | null = null

  const openSetting = async () => {
    setJumpSelecting(false)
    const settingRes = await uni.openSetting()
    setTimeout(() => {
      setJumpSelecting(true)
    }, 200)
    if (settingRes.authSetting?.['scope.userLocation']) {
      setPermission('granted')
      return true
    } else {
      setPermission('denied')
      return false
    }
  }

  const handleLocation = async () => {
    try {
      isLoading.value = true
      error.value = ''

      // 检查授权状态
      const { authSetting } = await uni.getSetting({})

      // 处理不同授权状态
      if (authSetting?.['scope.userLocation'] === undefined) {
        setPermission('unperssion')
        await uni.authorize({
          scope: 'scope.userLocation',
          modalContent: '需要获取您的位置信息用于车辆，请允许位置权限',
        })
      } else if (authSetting['scope.userLocation'] === false) {
        setPermission('denied')
        await new Promise((resolve, reject) => {
          if (options.showModalWhenDenied && false) {
            uni.showModal({
              title: '位置权限提示',
              content: '需要您的位置信息提供周边服务，请点击下方按钮开启权限',
              confirmText: '立即授权',
              showCancel: false,
              success: async (res) => {
                if (res.confirm) {
                  const isGranted = await openSetting()
                  if (isGranted) {
                    resolve(true)
                  } else {
                    reject(new Error('用户未授权'))
                  }
                } else {
                  reject(new Error('用户取消授权'))
                }
              },
              fail: reject,
            })
          } else {
            resolve(true)
          }
        })
      } else {
        setPermission('granted')
      }

      // 获取定位信息
      const res = await uni.getLocation({
        type: 'wgs84',
        altitude: true,
      })

      // 存储原始数据
      location.value = { ...res }
      error.value = ''
    } catch (err: any) {
      // 确保清空旧数据
      location.value = null
      handleLocationError(err)
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const handleLocationError = (err: any) => {
    const errorMap: Record<number, string> = {
      1: '位置获取失败：缺少定位权限',
      2: '位置信息不可用',
      3: '定位请求超时',
    }

    if (err.errCode === 1) {
      uni.showModal({
        title: '需要位置权限',
        content: '请在设置中开启定位权限',
        success: (res) => {
          if (res.confirm) uni.openSetting()
        },
      })
    }

    error.value = errorMap[err.errCode] || '定位失败'
  }

  // 修改后的 getRawLocation（始终返回可用值）
  const getRawLocation = async () => {
    await handleLocation().catch((e) => {
      console.warn('获取定位失败:', e)
    })
    return toRaw(location.value) // 可能为 null 或有效位置
  }

  // 立即开始初始化
  if (options.autoInitial) getRawLocation()

  return {
    location,
    getRawLocation,
    openSetting,
    isLoading,
    error,
    ready: readyPromise,
  }
}
