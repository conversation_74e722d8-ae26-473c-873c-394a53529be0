// Message types for business logic
interface PickupPoint {
  id: string
  latitude: number
  longitude: number
  address: string
}

interface VehiclePoint {
  id: string
  latitude: number
  longitude: number
  status: string
}

interface TrajectoryPoint {
  latitude: number
  longitude: number
  timestamp: number
}

interface VehicleTrajectory {
  vehicleId: string
  points: TrajectoryPoint[]
}

// WebSocket message types
type WebSocketMessage = {
  type:
    | 'SET_PICKUP_POINTS'
    | 'SET_VEHICLE_POINTS'
    | 'SET_VEHICLE_TRAJECTORIES'
    | 'SET_SELF_LOCATION'
  data: {
    visible: boolean
    pickupPoints?: PickupPoint[]
    vehiclePoints?: VehiclePoint[]
    trajectories?: VehicleTrajectory[]
    latitude?: number
    longitude?: number
  }
}

interface WebSocketOptions {
  code: string
  url: string
  protocols?: string[]
  header?: Record<string, string>
  onmessage?: (data: any) => void
  success?: () => void
  fail?: (error: any) => void
  complete?: () => void
}

interface WebSocketState {
  socketOpen: boolean
  socketTask: UniApp.SocketTask | null
  messageQueue: any[]
  reconnectAttempts: number
  reconnectInterval: number
}

export const useTianDiMapWebsocket = (options: WebSocketOptions) => {
  const state = reactive<WebSocketState>({
    socketOpen: false,
    socketTask: null,
    messageQueue: [],
    reconnectAttempts: 0,
    reconnectInterval: 3000,
  })

  const connect = () => {
    try {
      const socketTask = uni.connectSocket({
        url: options.url,
        protocols: options.protocols,
        header: options.header,
        success: () => {
          options.success?.()
        },
        fail: (error) => {
          options.fail?.(error)
          handleReconnect()
        },
        complete: options.complete,
      })

      if (socketTask) {
        state.socketTask = socketTask

        socketTask.onOpen(() => {
          state.socketOpen = true
          state.reconnectAttempts = 0
          // Send any queued messages
          flushMessageQueue()
        })

        socketTask.onClose(() => {
          state.socketOpen = false
          handleReconnect()
        })

        socketTask.onError((error) => {
          console.error('WebSocket error:', error)
          handleReconnect()
        })

        socketTask.onMessage((result) => {
          handleMessage(result.data)
        })
      }
    } catch (error) {
      console.error('Failed to connect WebSocket:', error)
      handleReconnect()
    }
  }

  const disconnect = () => {
    if (state.socketTask && state.socketOpen) {
      state.socketTask.close({
        code: 1000,
        reason: 'Normal closure',
        success: () => {
          state.socketOpen = false
          state.socketTask = null
        },
      })
    }
  }

  const send = (message: object | ArrayBuffer) => {
    if (state.socketOpen && state.socketTask) {
      state.socketTask.send({
        data: JSON.stringify({ ...message, code: options.code }),
        fail: (error) => {
          console.error('Failed to send message:', error)
          // Queue the failed message for retry
          state.messageQueue.push(message)
        },
      })
    } else {
      // Queue the message if socket is not open
      state.messageQueue.push(message)
    }
  }

  const flushMessageQueue = () => {
    while (state.messageQueue.length > 0 && state.socketOpen) {
      const message = state.messageQueue.shift()
      send(message)
    }
  }

  const handleMessage = (data: string | ArrayBuffer) => {
    try {
      const message =
        typeof data === 'string' ? JSON.parse(data) : JSON.parse(new TextDecoder().decode(data))
      console.log('【小程序接收消息】:', message)
      options.onmessage?.(message)
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error)
    }
  }

  const handleReconnect = () => {
    if (state.reconnectAttempts < 5) {
      // Maximum 5 reconnection attempts
      setTimeout(() => {
        state.reconnectAttempts++
        connect()
      }, state.reconnectInterval)
    }
  }

  // Auto-connect when the hook is initialized
  onMounted(() => {
    connect()
  })

  // Clean up on component unmount
  onUnmounted(() => {
    disconnect()
  })

  // 显示/隐藏取货点
  const setPickupPointsVisibility = (points: PickupPoint[], visible: boolean) => {
    const message: WebSocketMessage = {
      type: 'SET_PICKUP_POINTS',
      data: {
        visible,
        pickupPoints: points,
      },
    }
    send(message)
  }

  // 显示/隐藏车辆点
  const setVehiclePointsVisibility = (points: VehiclePoint[], visible: boolean) => {
    const message: WebSocketMessage = {
      type: 'SET_VEHICLE_POINTS',
      data: {
        visible,
        vehiclePoints: points,
      },
    }
    send(message)
  }

  // 显示/隐藏多个车辆轨迹
  const setVehicleTrajectories = (trajectories: VehicleTrajectory[], visible: boolean) => {
    const message: WebSocketMessage = {
      type: 'SET_VEHICLE_TRAJECTORIES',
      data: {
        visible,
        trajectories,
      },
    }
    send(message)
  }
  // 显示/隐藏自身定位
  const setSelfLocation = (
    location: { latitude: number; longitude: number },
    visible: boolean = true,
  ) => {
    const message: WebSocketMessage = {
      type: 'SET_SELF_LOCATION',
      data: {
        visible,
        latitude: location.latitude,
        longitude: location.longitude,
      },
    }
    send(message)
  }

  // 显示/隐藏单个车辆轨迹（为了向后兼容和便利性）
  const setVehicleTrajectoryVisibility = (trajectory: VehicleTrajectory, visible: boolean) => {
    setVehicleTrajectories([trajectory], visible)
  }

  // 清除所有显示的点和轨迹
  const clearAll = () => {
    setPickupPointsVisibility([], false)
    setVehiclePointsVisibility([], false)
    setVehicleTrajectories([], false)
  }

  return {
    isConnected: computed(() => state.socketOpen),
    send,
    connect,
    disconnect,
    reconnectAttempts: computed(() => state.reconnectAttempts),
    // 控制显示/隐藏的方法
    setPickupPointsVisibility,
    setVehiclePointsVisibility,
    setVehicleTrajectoryVisibility,
    setVehicleTrajectories,
    setSelfLocation,
    clearAll,
  }
}
