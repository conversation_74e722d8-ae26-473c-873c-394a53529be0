<template>
  <wd-config-provider :theme="theme">
    <!-- <text class="fixed left-4 top-4">{{ countdown }}</text> -->
    <view
      class="default-layout"
      :class="isMaintenance ? 'maintenance-theme' : ''"
      @touchstart="addEvent"
      @scroll="addEvent"
    >
      <slot />
      <wd-toast />
      <wd-message-box />
    </view>
  </wd-config-provider>
</template>
<script lang="ts" setup>
import { useMessage, useToast } from 'wot-design-uni'
import { useUserStore } from '@/store'
import { useActivityCheck } from '@/hooks/useActivityCheck'

const isMaintenance = useUserStore().isMaintenance
// 检测用户是否活跃，判断当前用户是否正在购买
const { addEvent } = useActivityCheck()

// 提示弹窗必须引入
const message = useMessage()
const toast = useToast()
const theme = ref<'dark' | 'light'>('light')
</script>
<!--wot组件库的样式穿透-->
<script lang="ts">
export default { options: { styleIsolation: 'shared' } }
</script>
<style lang="scss">
// page {
//   --wot-button-primary-bg-color: #356afd;
//   .wd-button.is-primary {
//     // background: var(--wot-button-primary-bg-color);
//   }
// }
.default-layout {
  height: 100%;
  overflow: auto;
}

.wot-theme-light {
  height: 100%;
}

:deep(.is-required) {
  --wot-cell-required-color: #356afdff !important;
}

// 取消原生导航栏之后，沉浸式窗体的状态栏占位
.status_bar {
  height: var(--status-bar-height);
  width: 100%;
}

image {
  will-change: transform;
}

// 全屏高
.height-full {
  height: 100%;
}

uni-page-body {
  height: 100%;
}

.uni-progress-bar,
.uni-progress-inner-bar {
  border-radius: 9999px;
}

// uni-forms

/*表单label*/
//label在上方时的样式
:deep(.uni-forms-item.is-direction-top .uni-forms-item__label) {
  font-weight: bold;
  font-size: 16px;
  position: relative;
  padding-left: 10px !important;
  padding-bottom: 0 !important;
  color: black;
}

/*必填星号*/
:deep(.uni-forms-item .is-required) {
  color: rgba(53, 106, 253, 1) !important;
  position: absolute;
  left: 0;
}

/*uni-forms-item带border且label在上面 时的样式*/
:deep(.uni-forms-item.is-direction-top.uni-forms-item--border) {
  padding: 8px 0;
}

/*uni-table 中设置min-width*/
:deep(.uni-table-scroll .uni-table) {
  min-width: unset !important;
}
</style>
