import { createSSRApp } from 'vue'
import App from './App.vue'
import store from './store'
import { routeInterceptor, requestInterceptor } from './interceptors'
// import PageSpy from '@huolala-tech/page-spy-wechat'
import PageSpy from '@huolala-tech/page-spy-uniapp'
import LAButton from './components/LAButton.vue'

import 'virtual:svg-icons-register'
import 'virtual:uno.css'
import '@/style/index.scss'

if (!window) {
  const options = {
    api: '47.109.45.248:6752',
    enableSSL: false,
  }
  const $pageSpy = new PageSpy(options)
  setTimeout(() => {
    $pageSpy.showPanel()
  }, 2000)
}

// production mock server
// 线上报错，TODO: fix me
// if (import.meta.env.PROD) {
//   import('../mock/_mockProdServer').then(({ setupProdMockServer }) => {
//     console.log('mockProdServer...')
//     setupProdMockServer()
//   })
// }

export function createApp() {
  const app = createSSRApp(App)
  app.use(store)
  app.use(routeInterceptor)
  app.use(requestInterceptor)
  app.component('LAButton', LAButton)
  return {
    app,
  }
}
