<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '关闭工单' },
}
</route>

<template>
  <view class="textarea pb-2 bg-white">
    <wd-textarea label="清理备注" required v-model="remark" placeholder="请输入" :maxlength="200" auto-focus show-word-limit />
    <view class="px-4">
      <LAButton block size="xl" :round="false" @click="handleSubmit">
        确定
      </LAButton>
    </view>
  </view>
</template>

<script setup lang="ts">
import { closeClean } from '@/service/maintenanceApi'
import { useToast } from 'wot-design-uni'
const toast = useToast()

const orderId = ref('')
onLoad((params) => {
  orderId.value = params.id
})
const remark = ref('')
const handleSubmit = async () => {
  const params = { workOrderId: orderId.value, remark: remark.value }
  try {
    const res = await closeClean(params)
    if (res.success) {
      toast.success('操作成功')
      uni.redirectTo({ url: '/pages-maintenance/clean-order/index' })
    } else {
      toast.error(res.message)
    }
  } catch (e) { }
}
</script>

<style lang="scss" scoped>
:deep(.wd-textarea) {
  display: block !important;
}
</style>
