<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '清理工单详情' },
}
</route>

<template>
  <view class="pb-2">
    <view class="py-2.5 px-2 bg-white">
      <view class="flex justify-between items-center p-3.5">
        <view class="font-bold text-black text-base">{{ data?.workOrderNum }}</view>
        <view class="text-sm" :style="{ color: statusMap.get(data?.status)?.color }">
          {{ statusMap.get(data?.status)?.name }}
        </view>
      </view>
      <view class="text-sm leading-7 p-4 mb-1.5 rounded-xl bg-[#F8F9FC]">
        <view class="flex justify-between">
          <view class="text-[#979998]">车辆名称</view>
          <view class="text-[#1A1A1A]">{{ data?.deviceMineTrainName }}</view>
        </view>
        <view class="flex justify-between">
          <view class="text-[#979998]">车牌号</view>
          <view class="text-[#1A1A1A]">{{ data?.plateNo }}</view>
        </view>
        <view class="flex justify-between">
          <view class="text-[#979998]">车辆型号</view>
          <view class="text-[#1A1A1A]">{{ data?.modelNumber }}</view>
        </view>
        <view class="flex justify-between">
          <view class="text-[#979998]">车辆编码</view>
          <view class="text-[#1A1A1A]">{{ data?.carCode }}</view>
        </view>
        <view class="flex justify-between">
          <view class="text-[#979998]">投放区域</view>
          <view class="text-[#1A1A1A]">{{ data?.runAreaName }}</view>
        </view>
        <view class="flex justify-between">
          <view class="text-[#979998]">工单生成时间</view>
          <view class="text-[#1A1A1A]">{{ data?.createDate }}</view>
        </view>
        <view class="flex justify-between">
          <view class="text-[#979998]">清理负责人</view>
          <view class="text-[#1A1A1A]">{{ data?.picName || '-' }}</view>
        </view>
        <view class="flex justify-between">
          <view class="text-[#979998]">清理完成时间</view>
          <view class="text-[#1A1A1A]">{{ data?.completionTime || '-' }}</view>
        </view>
      </view>
      <wd-select-picker
        v-if="data?.status === '0'"
        title="派工-选中清理负责人"
        type="radio"
        filterable
        use-default-slot
        v-model="user1"
        :columns="workerList"
        @confirm="handleDispatchClean"
      >
        <LAButton :round="false" block size="lg">派工</LAButton>
      </wd-select-picker>
      <view v-if="data?.status === '1'" class="flex justify-between items-center">
        <view
          @click="toClean(data?.carId)"
          class="flex justify-center items-center flex-1 h-11 text-[#356AFD] text-sm rounded-xl bg-[#f4f7ff]"
        >
          清理
        </view>
        <view
          @click="toClosed(data?.id)"
          class="flex justify-center items-center flex-1 h-11 mx-1 text-[#356AFD] text-sm rounded-xl bg-[#f4f7ff]"
        >
          关闭工单
        </view>
        <view class="flex-1">
          <wd-select-picker
            title="选中清理负责人"
            type="radio"
            filterable
            use-default-slot
            v-model="user2"
            :columns="workerList"
            @confirm="handleTransferClean"
          >
            <view
              class="flex justify-center items-center flex-1 h-11 text-[#356AFD] text-sm rounded-xl bg-[#f4f7ff]"
            >
              转让工单
            </view>
          </wd-select-picker>
        </view>
      </view>
    </view>
    <view class="bg-white mt-2 p-4 pb-0 text-sm text-[#656666]">
      <view class="flex items-center">
        <view class="w-1 h-4 rounded-sm bg-[#356AFD] mr-1.5 mt-0.5"></view>
        <view class="font-bold text-base text-[#1a1a1a]">清理项目</view>
      </view>
      <view class="flex items-center font-bold text-[#1a1a1a] py-4.5">
        <view class="flex-1">废料桶名称</view>
        <view class="w-20">已经占容量</view>
      </view>
      <view
        class="flex items-center py-4.5 border-t border-t-solid border-slate-200"
        v-for="item in data?.clearProjects"
        :key="item.deviceId"
      >
        <view class="flex-1">{{ item.deviceName }}-{{ item.bucketName }}</view>
        <view class="w-20 text-[#F25555]">{{ item.occupied?.replace('&', '') }}</view>
      </view>
    </view>
    <view class="bg-white mt-2 p-4" v-if="data?.deviceClearRecords">
      <view class="flex items-center">
        <view class="w-1 h-4 rounded-sm bg-[#356AFD] mr-1.5 mt-0.5"></view>
        <view class="font-bold text-base text-[#1a1a1a]">清理记录</view>
      </view>
      <view
        class="border-b border-b-solid border-slate-200 p-2"
        v-for="item in data?.deviceClearRecords"
        :key="item.id"
      >
        <view class="flex items-center text-sm text-[#656666] mb-2">
          <view class="font-bold text-[#1a1a1a] mr-2.5">{{ item.currentPicName }}</view>
          <view>{{ item.operationDes }}</view>
          <view class="ml-auto">{{ item.createDate }}</view>
        </view>
        <view class="flex items-center text-sm text-[#656666]">
          <view class="font-bold text-[#1a1a1a]">{{ item.remarkTitle }}：</view>
          <view>{{ item.remark }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import {
  dispatchClean,
  transferClean,
  getCleanWorkerList,
  getCleanLeaderList,
} from '@/service/maintenanceApi'
import { useToast } from 'wot-design-uni'
const toast = useToast()

const data = ref()

uni.getStorage({
  key: 'cleanDetails',
  success: function (res) {
    data.value = res.data
    getCleanWorkerList({ carId: data.value.carId }).then((res) => {
      workerList.value = res.data?.map((item) => ({ value: item.id, label: item.userName }))
    })
  },
})

const statusMap = new Map([
  [
    '0',
    {
      name: '未派工',
      color: '#356AFD',
    },
  ],
  [
    '1',
    {
      name: '待清理',
      color: '#00C290',
    },
  ],
  [
    '2',
    {
      name: '已关闭',
      color: '#656666',
    },
  ],
])

const user1 = ref('')
const user2 = ref('')
const workerList = ref([])
const leaderList = ref([])

getCleanLeaderList({}).then((res) => {
  leaderList.value = res.data?.map((item) => ({ value: item.id, label: item.userName }))
})
const handleDispatchClean = async (payload: any) => {
  if (!payload.value) return
  try {
    const { label, value } = payload.selectedItems
    const params = {
      picId: value,
      picName: label,
      id: data.value.id,
    }
    const res = await dispatchClean(params)
    if (res.success) {
      toast.success('操作成功')
      uni.redirectTo({ url: '/pages-maintenance/clean-order/index' })
    } else {
      toast.error(res.message)
    }
  } catch (e) {
    console.log(e)
    toast.error('操作失败')
  }
}
const handleTransferClean = async (payload: any) => {
  if (!payload.value) return
  try {
    const { label, value } = payload.selectedItems
    const params = {
      picId: value,
      picName: label,
      workOrderId: data.value.id,
    }
    const res = await transferClean(params)
    if (res.success) {
      toast.success('操作成功')
      uni.redirectTo({ url: '/pages-maintenance/clean-order/index' })
    } else {
      toast.error(res.message)
    }
  } catch (e) {
    console.log(e)
  }
}
const toClean = (carId: string) => {
  uni.navigateTo({
    url: `/pages-maintenance/clean/details?carId=${carId}`,
  })
}
const toClosed = (id) => {
  uni.navigateTo({ url: `/pages-maintenance/clean-order/closed?id=${id}` })
}
</script>

<style lang="scss" scoped>
.wd-select-picker {
  :deep(.wd-action-sheet__header) {
    height: 50px;
    line-height: 50px;
    .wd-action-sheet__close {
      top: 18px;
    }
  }
}
:deep(.wd-select-picker__footer.wd-select-picker__footer .wd-button) {
  background: var(--theme-color-ops, #356afd);
  border-radius: 11px;
}
</style>
