<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '清理工单' },
}
</route>

<template>
  <view class="pb-2">
    <PageList :requestApi="fetchList" :search="search">
      <template #default="{ item }">
        <view class="bg-white rounded-xl py-3.5 px-4 mt-2 mx-2" @click="toDetails(item)">
          <view
            class="flex justify-between items-center pb-3.5 border-b border-b-solid border-slate-200"
          >
            <view class="font-bold text-base text-black">{{ item.workOrderNum }}</view>
            <view class="flex items-center" :style="{ color: statusMap.get(item.status)?.color }">
              <view class="text-sm">{{ statusMap.get(item.status)?.name }}</view>
              <wd-icon name="arrow-right" size="16px"></wd-icon>
            </view>
          </view>
          <view class="text-sm pt-3.5">
            <view class="flex justify-between items-center">
              <view class="text-gray-400">车辆名称</view>
              <view class="text-[#1a1a1a]">{{ item.deviceMineTrainName }}</view>
            </view>
            <view class="flex justify-between items-center">
              <view class="text-gray-400">清理负责人</view>
              <view class="text-[#1a1a1a]">{{ item.picName || '-' }}</view>
            </view>
            <view class="flex justify-between items-center">
              <view class="text-gray-400">工单生成时间</view>
              <view class="text-[#1a1a1a]">{{ item.createDate }}</view>
            </view>
          </view>
        </view>
      </template>
    </PageList>
  </view>
</template>

<script setup lang="ts">
import { getCleanList, getCleanCarList } from '@/service/maintenanceApi'

const search = {
  placeholder: '清理工单号/清理人',
  searchKey: 'search',
  options: [
    {
      prop: 'carId',
      title: '车辆名称',
      requestApi: getCleanCarList,
      replaceOptions: {
        label: 'carName',
        value: 'id',
      },
    },
    {
      prop: 'status',
      title: '工单状态',
      options: [
        { label: '未派工', value: '0' },
        { label: '待清理', value: '1' },
        { label: '已关闭', value: '2' },
      ],
    },
    {
      prop: 'createDate',
      slot: true,
      slotType: 'date',
      title: '生成时间',
    },
  ],
}

const toDetails = (item) => {
  uni.setStorage({
    key: 'cleanDetails',
    data: item,
    success: function () {
      uni.navigateTo({ url: `/pages-maintenance/clean-order/details` })
    },
  })
}

const statusMap = new Map([
  [
    '0',
    {
      name: '未派工',
      color: '#356AFD',
    },
  ],
  [
    '1',
    {
      name: '待清理',
      color: '#00C290',
    },
  ],
  [
    '2',
    {
      name: '已关闭',
      color: '#656666',
    },
  ],
])
const fetchList = async (params) => {
  return getCleanList(params)
}
</script>

<style scoped></style>
