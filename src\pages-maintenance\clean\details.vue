<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '清理' },
}
</route>
<template>
  <view class="pb-16">
    <view class="flex justify-end items-center py-3.5 px-4 text-sm text-[#1a1a1a] bg-white">
      <view class="flex justify-center mr-auto">
        <wd-icon name="location" size="16px"></wd-icon>
        <view>{{ carInfo.runAreaName }}</view>
      </view>
      <view>{{ carInfo.carName }}</view>
      <view class="mx-1.5" :style="{ color: carInfo.status === offlineCode ? '#656666' : '#00C290' }">
        {{ carInfo.status === offlineCode ? '离线' : '在线' }}
      </view>
      <view>{{ formatDecimal(carInfo?.distance) }}</view>
    </view>

    <view class="flex items-center bg-white my-2 pl-4">
      <view class="font-bold text-sm text-black label before:content['*']">照片拍摄</view>
      <view class="flex-1">
        <CameraSheet :allowAlbum="false" v-model="photoList" :min="3" style="padding-bottom: 0">
          <wd-cell :title="photoList?.length ? `已拍摄${photoList?.length}张` : '点击拍摄'" is-link />
        </CameraSheet>
      </view>
    </view>
    <view class="bg-white mb-2 py-3 px-6" v-for="item in bucketList" :key="item.bucketId">
      <view class="flex justify-between mb-3">
        <view class="font-bold text-base text-[#1a1a1a]">{{ item.bucket }}</view>
        <view class="flex text-sm text-[#656666]">
          <view>已占容量：</view>
          <view :style="{ color: item.isExceed ? '#F25555' : '#656666' }">
            {{ item.occupy }}{{ item.unit }}
          </view>
        </view>
      </view>
      <view class="flex">
        <view @click="handleCleanStatus(item, '0')"
          class="w-[74px] h-[32px] text-sm text-center text-[#1A1A1A] rounded-md border border-solid border-slate-200 mr-2"
          :class="{ active: item.tag === '0' }" style="line-height: 32px">
          已清理
        </view>
        <view @click="handleCleanStatus(item, '1')"
          class="w-[74px] h-[32px] text-sm text-center text-[#1A1A1A] rounded-md border border-solid border-slate-200"
          :class="{ active: item.tag === '1' }" style="line-height: 32px">
          未清理
        </view>
      </view>
    </view>
    <view class="p-2 fixed bottom-0 left-0 w-full bg-white">
      <LAButton size="xl" :round="false" :disabled="isDisabled" custom-style="width:95vw" @click="handleSubmit">
        确定
      </LAButton>
      <wd-gap safe-area-bottom height="0"></wd-gap>
    </view>
  </view>
</template>

<script setup lang="ts">
import { getCarInfoById } from '@/service/api'
import { getCleanBucketList, submitClean } from '@/service/maintenanceApi'
import useNativeLocation from '@/hooks/useNativeLocation'
import CameraSheet from '@/components/camera-sheet.vue'
import { formatDecimal } from '@/utils'
import { useToast } from 'wot-design-uni'
const toast = useToast()

// status 状态说明，1006为离线，其他都认定为在线
const offlineCode = ref(1006)
interface CarInfo {
  id: string
  carName: string
  runAreaName: string
  runAreaId: string
  status: number
  distance: number
}
const carInfo = ref<CarInfo>({
  id: '',
  carName: '',
  runAreaName: '',
  runAreaId: '',
  status: 0,
  distance: 0,
})

onLoad(({ carId }) => {
  getCarInfo(carId)
  getBucketList(carId)
})

const getCarInfo = async (carId) => {
  const { getRawLocation } = useNativeLocation()
  const currentLocation = await getRawLocation()

  const params = {
    carId,
    ...(currentLocation?.latitude && currentLocation?.longitude
      ? {
        lat: Number(currentLocation.latitude.toFixed(8)),
        lon: Number(currentLocation.longitude.toFixed(8)),
      }
      : {}),
  }
  const res = await getCarInfoById(params)
  if (res.success) {
    carInfo.value = res.data as CarInfo
    const status = carInfo.value.status
    if (!status) {
      carInfo.value.status = offlineCode
    }
  }
}

const photoList = ref([])

// tag 标签说明，'0' 为已清理，'1' 为未清理
interface BucketItem {
  isExceed: boolean
  bucketId: string
  bucket: string
  occupy: string
  unit: string
  tag: string
}
const bucketList = ref<BucketItem[]>([])
const getBucketList = async (carId: string) => {
  const res = await getCleanBucketList({ carId })
  if (res.success) {
    bucketList.value = (res.data as BucketItem[])?.map((x) => {
      x.tag = ''
      return x
    })
  }
}

const handleCleanStatus = (item: BucketItem, tag: string) => {
  if (item.tag === tag) item.tag = ''
  else item.tag = tag
}
const isDisabled = computed(() => {
  return bucketList.value.some((item) => item.tag === '')
})
const handleSubmit = async () => {
  if (!photoList.value.length) {
    return toast.warning('请拍摄照片')
  }
  const clearBuckets = bucketList.value.map(({ bucketId, bucket, occupy, unit, tag }) => ({
    bucketId,
    bucket,
    occupy,
    unit,
    tag,
  }))
  try {
    const { id: carId, carName, runAreaId, runAreaName: runArea } = carInfo.value
    const params = {
      carId,
      carName,
      runArea,
      runAreaId,
      clearBuckets,
      imageUrls: photoList.value,
    }

    const res = await submitClean(params)
    if (res.success) {
      toast.success('操作成功')
      setTimeout(() => {
        uni.navigateBack()
      }, 500)
    } else {
      toast.error(res.message)
    }
  } catch (e) {
    console.log(e)
    toast.error('操作失败')
  }
}
</script>

<style lang="scss" scoped>
:deep(.camera-sheet-class) {
  padding-bottom: 0 !important;
}

.active {
  color: #fff;
  border-color: transparent;
  background: #356afd;
}

.label {
  &::before {
    content: '*';
    color: #356afd;
    margin-right: 2px;
  }
}
</style>
