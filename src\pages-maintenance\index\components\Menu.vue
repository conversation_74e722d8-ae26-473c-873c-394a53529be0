<template>
  <view class="flex flex-row flex-wrap">
    <view
      v-for="(item, index) in menuItems"
      :key="index"
      class="w-1/2 flex items-center gap-1 p-4 border-box"
      @click="navigateTo(item.url)"
    >
      <image class="w-7 h-7" :src="item.icon" mode="aspectFit" />
      <text class="text-gray-700 whitespace-nowrap">{{ item.title }}</text>
      <!-- 条件渲染角标 -->
      <view
        v-if="item.showBadge && item.badgeCount > 0"
        class="bg-red rounded-md w-fit min-w-4 h-4 px-1 text-center text-white text-xs"
      >
        {{ item.badgeCount }}
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
// 类型定义
export interface ListItem {
  title: string
  name: string
  icon: string
  url: string
  showBadge?: boolean
  badgeCount?: number
}
const props = defineProps({
  menuItems: {
    type: Array as PropType<ListItem[]>,
    default: () => [],
  },
})

// 统一的导航函数
function navigateTo(url: string) {
  uni.navigateTo({ url })
}
</script>

<style scoped></style>
