<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '蓝奥无人奶茶车运维端' },
}
</route>

<template>
  <view class="p-4">
    <view class="bg-white rounded-lg p-2">
      <Menu :menu-items="menuItems" />
    </view>
    <view class="bg-white rounded-lg p-2 mt-4">
      <Menu :menu-items="menuItems2" />
    </view>

    <TabBar v-model="activeTab"></TabBar>
  </view>
</template>

<script lang="ts" setup>
/**
 * @file tab-运维-首页
 * <AUTHOR>
 * @date 2025/2/26
 */
// 奶茶车
import IconCar from './imgs/IconCar.svg?url'
// 目标点管理
import IconTargetPoint from './imgs/IconTargetPoint.svg?url'
// 补料
import IconReplenish from './imgs/IconReplenish.svg?url'
// 清理
import IconClean from './imgs/IconClean.svg?url'
// 补料工单
import IconReplenishOrder from './imgs/IconReplenishOrder.svg?url'
// 清理工单
import IconCleanOrder from './imgs/IconCleanOrder.svg?url'
// 维修工单
import IconRepairOrder from './imgs/IconRepairOrder.svg?url'
// 保养工单
import IconMaintenanceOrder from './imgs/IconMaintenanceOrder.svg?url'
// 维修经验库
import IconMaintenanceExperienceLibrary from './imgs/IconMaintenanceExperienceLibrary.svg?url'
// 保养知识库
import IconMaintenanceKnowledgeLibrary from './imgs/IconMaintenanceKnowledgeLibrary.svg?url'

import TabBar from '@/components/TabBar.vue'
import Menu from '@/pages-maintenance/index/components/Menu.vue'
import { getPending } from '@/service/maintenanceApi'

interface ListItem {
  title: string
  name: string
  icon: string
  url: string
  showBadge?: boolean
  badgeCount?: number
}

const activeTab = ref('maintenanceHome')
// 菜单数据
const menuItems = ref<ListItem[]>([
  {
    title: '奶茶车',
    name: 'sellCar',
    icon: IconCar,
    url: '/pages-maintenance/sell-car/index?url=/pages-maintenance/sell-car/detail/index',
    showBadge: false,
  },
  {
    title: '目标点管理',
    name: 'markerManager',
    icon: IconTargetPoint,
    url: '/pages-maintenance/marker-manager/index',
    showBadge: false,
  },
  {
    title: '补料',
    name: 'replenish',
    icon: IconReplenish,
    url: '/pages-maintenance/sell-car/index?url=/pages-maintenance/replenish/replenishEditForm/index',
    showBadge: true,
    badgeCount: 0,
  },
  {
    title: '清理',
    name: 'clean',
    icon: IconClean,
    url: '/pages-maintenance/sell-car/index?url=/pages-maintenance/clean/details',
    badgeCount: 0,
    showBadge: true,
  },
  {
    title: '补料工单',
    name: 'replenishOrder',
    icon: IconReplenishOrder,
    url: '/pages-maintenance/replenish-order/index',
    showBadge: true,
    badgeCount: 0,
  },
  {
    title: '清理工单',
    name: 'cleanOrder',
    icon: IconCleanOrder,
    url: '/pages-maintenance/clean-order/index',
    showBadge: true,
    badgeCount: 0,
  },
])
const menuItems2 = ref<ListItem[]>([
  {
    title: '维修工单',
    name: 'repairOrder',
    icon: IconRepairOrder,
    url: '/pages-maintenance/repair-order/index',
    showBadge: true,
    badgeCount: 0,
  },
  {
    title: '保养工单',
    name: 'maintenanceOrder',
    icon: IconMaintenanceOrder,
    url: '/pages-maintenance/maintenance-order/index',
    showBadge: true,
    badgeCount: 0,
  },
  {
    title: '维修经验库',
    name: 'repairExperienceLibrary',
    icon: IconMaintenanceExperienceLibrary,
    url: '/pages-maintenance/repair-experience-library/index',
    showBadge: false,
    badgeCount: 0,
  },
  {
    title: '保养知识库',
    name: 'maintenanceExperienceLibrary',
    icon: IconMaintenanceKnowledgeLibrary,
    url: '/pages-maintenance/maintenance-experience-library/index',
    showBadge: false,
    badgeCount: 0,
  },
])
interface PendingType {
  deviceId: string
  deviceCode: string
  deviceName: string
  restockCount: number
  clearCount: number
  repairCount: number
  maintenanceCount: number
}
// 新增：获取未读消息数
const fetchBadgeCount = async () => {
  try {
    // 这里替换为实际接口调用，示例用假数据
    const count: Record<string, number> = {
      maintenanceOrder: 0,
      cleanOrder: 0,
      repairOrder: 0,
      replenishOrder: 0,
    }

    await getPending().then((res: IResData<PendingType[]>) => {
      //  获取补料总和(每一项相加)
      count.replenishOrder = res.data.reduce((sum, item) => sum + item.restockCount, 0)
      //  获取清理总和(每一项相加)
      count.cleanOrder = res.data.reduce((sum, item) => sum + item.clearCount, 0)
      //  获取维修总和(每一项相加)
      count.repairOrder = res.data.reduce((sum, item) => sum + item.repairCount, 0)
      //  获取保养总和(每一项相加)
      count.maintenanceOrder = res.data.reduce((sum, item) => sum + item.maintenanceCount, 0)
    })

    // 更新菜单项的角标数量
    menuItems.value.forEach((item) => {
      if (count[item.name]) {
        item.badgeCount = count[item.name]
      }
    })
    menuItems2.value.forEach((item) => {
      if (count[item.name]) {
        item.badgeCount = count[item.name]
      }
    })
  } catch (error) {
    console.error('获取角标数据失败:', error)
  }
}
onLoad(() => {
  uni.hideHomeButton()
})
onShow(() => {
  fetchBadgeCount()
})

</script>
