<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '保养知识详情',
  },
}
</route>
<template>
  <view>
    <view class="p-4 bg-white">
      <view class="pb-1">
        <view
          class="fw-bold text-[#1A1A1A] text-xl pb-4 mb-4 bg-white border-b border-b-solid border-b-color-[#EFF1F5]"
        >
          <view>{{ repairExperienceDetail?.position }}</view>
        </view>
        <view class="rounded-2 flex flex-col gap-4">
          <view v-for="item in detailLabel" :key="item.field" class="flex">
            <view class="text-sm mr-2 fw-bold text-[#1A1A1A]">{{ item.label }}:</view>
            <view
              v-if="!['method'].includes(item.field)"
              class="max-w-[350rpx] text-[#656666] text-sm"
            >
              {{
                item.render
                  ? item.render(repairExperienceDetail[item.field])
                  : repairExperienceDetail[item.field]
              }}
            </view>
            <view
              v-else-if="item.field === 'method'"
              class="flex-1 text-[#656666] text-sm"
              v-html="handleImg(repairExperienceDetail[item.field])"
            ></view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
/**
 * @file 保养知识库-详情
 * <AUTHOR>
 * @date 2025/4/21
 */

import { getRepairExperienceRecordList } from '@/service/maintenanceApi'

interface DetailItem {
  label: string
  field: string
  render?: (value: any) => VNode | string
}

// 工单详情数据
const repairExperienceDetail = ref<Record<string, any>>({})
// 用户维修经验记录
const repairExperienceRecordData = ref()

// 刷新
const refresh = async () => {
  // 获取维修经验记录
  await getRepairExperienceRecordList({
    experienceId: repairExperienceDetail.value?.id,
  }).then((res) => {
    repairExperienceRecordData.value = res.data
  })
}
// 事件通道，在onLoad中进行获取
let eventChannel: {
  on: (eventName: string, cb: (...args: any) => void) => void
  emit: (eventName: string, ...args: any) => void
}
onUnload(() => {
  eventChannel.emit('refresh')
})
onLoad(async (options) => {
  const instance = getCurrentInstance().proxy
  // @ts-expect-error - eventChannel
  eventChannel = instance.getOpenerEventChannel()
  eventChannel.on('params', (params) => {
    repairExperienceDetail.value = params
    console.log(78, repairExperienceDetail.value)
  })

  await refresh()
})
const handleImg = (html) => {
  return html
    ?.replace(/<p/g, '<p style="line-height: 1.2;"')
    .replace(/<img([^>]+)>/g, (match, attrs) => {
      // 添加自定义类名或样式
      return `<img class="custom-img" width="100%" style="max-width:100%;height:auto;" ${attrs}>`
    })
}
// 详情项配置
const detailLabel = reactive<DetailItem[]>([
  { label: '奶茶车型号', field: 'modelNumberName' },
  {
    label: '保养间隔',
    field: 'cycle',
    render: (data) => {
      return `${data}天`
    },
  },
  {
    label: '材料准备',
    field: 'material',
  },
  {
    label: '保养方式',
    field: 'method',
  },
])
</script>

<style scoped>
view {
  line-height: 100%;
}

:deep(.wd-select-picker__field.wd-select-picker__field) {
  height: 100%;
}
</style>
