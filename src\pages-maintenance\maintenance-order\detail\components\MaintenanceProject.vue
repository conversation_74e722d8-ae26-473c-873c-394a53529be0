<template>
  <view class="bg-white p-4 mt-2 flex-col flex gap-2">
    <LATitle title="保养项目" />
    <view
      v-for="(data, i) in projectList"
      :key="data.id"
      class="flex-col flex gap-3 text-sm pb-1"
      :class="i < projectList?.length - 1 ? 'border-b border-b-solid border-b-color-[#EFF1F5]' : ''"
    >
      <TipContainerPage title="保养知识详情">
        <wd-cell :title="data.position" is-link custom-class="!pl-2" />

        <template #content>
          <view class="p-4 w-full bg-white">
            <view
              class="text-lg fw-900 pb-4 w-full border-b border-b-solid border-b-color-[#EFF1F5]"
            >
              {{ data?.position }}
            </view>
            <view class="flex flex-col gap-4 pt-4">
              <view v-for="item in detailLabel" class="flex" :key="item.field">
                <view class="font-900 mr-2 whitespace-nowrap">{{ item.label }}:</view>
                <view class="text-[#656666]" v-if="item.field !== 'method'">
                  {{
                    item.render ? item.render(data?.[item.field]) : showValue(data?.[item.field])
                  }}
                </view>
                <view class="text-[#656666]" v-else v-html="handleImg(data?.[item.field])"></view>
              </view>
            </view>
          </view>
        </template>
      </TipContainerPage>
    </view>
  </view>
</template>

<script setup lang="ts">
/**
 * @file 保养工单-详情-保养项目
 * <AUTHOR>
 * @date 2025/4/22
 */

import LATitle from '@/components/LATitle.vue'
import { showValue } from '@/utils'
import TipContainerPage from '@/components/tip-container-page.vue'
interface MaintenancePosition {
  id: number
  position: string
}
interface DetailItem {
  label: string
  field: string
  render?: (value: any) => VNode | string
}
const props = defineProps({
  // 接收数据
  projectList: {
    type: Array as PropType<MaintenancePosition[]>,
    default: () => [{}],
  },
})
const handleImg = (html) => {
  return html?.replace(/<img([^>]+)>/g, (match, attrs) => {
    // 添加自定义类名或样式
    return `<img class="custom-img" width="100%" style="max-width:100%;height:auto;" ${attrs}>`
  })
}
// 弹出详情数据
const positionDetail = ref()

// 详情项配置
const detailLabel = reactive<DetailItem[]>([
  {
    label: '奶茶车型号',
    field: 'modelNumber',
    render: (val) => {
      return val === 'common' ? '通用' : val
    },
  },
  {
    label: '保养间隔',
    field: 'cycle',
    render: (val) => {
      return `${val}天`
    },
  },
  {
    label: '材料准备',
    field: 'material',
  },
  { label: '保养方式', field: 'method' },
])
</script>

<style scoped>
view {
  line-height: 100%;
  box-sizing: border-box;
}
</style>
