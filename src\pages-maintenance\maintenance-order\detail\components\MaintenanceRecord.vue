<template>
  <view class="bg-white p-4 mt-2 flex-col flex gap-3">
    <LATitle title="保养记录" />
    <view
      v-for="(data, i) in maintenanceRecord"
      :key="data.id"
      class="flex-col flex gap-3 text-sm pb-2 pl-1"
      :class="
        i < maintenanceRecord?.length - 1 ? 'border-b border-b-solid border-b-color-[#EFF1F5]' : ''
      "
    >
      <view class="flex justify-between items-center">
        <view class="flex">
          <view class="fw-bold mr-2">{{ data.maintenanceResponsiblePersonName }}</view>
          <view class="text-[#656666]">
            {{ maintenanceOperateResults?.[data.maintenanceResults] }}
          </view>
        </view>
        <view class="text-[#656666]">
          {{ dayjs(data.createDate).format('YYYY-MM-DD HH:mm:ss') }}
        </view>
      </view>
      <view v-if="['transfer'].includes(data.maintenanceResults)">
        <text class="fw-bold mr-2">{{ maintenanceContent[data.maintenanceResults] }}:</text>
        <text class="text-[#656666]">
          {{ data.newResponsiblePersonName }}
        </text>
      </view>
      <view v-if="['finish', 'evaluation'].includes(data.maintenanceResults)">
        <text class="fw-bold mr-2">{{ maintenanceContent[data.maintenanceResults] }}:</text>
        <text class="text-[#656666]">
          {{ data.maintenanceContent }}
        </text>
        <view class="flex text-sm mt-3" v-if="data?.deviceRepairMaintainFileList?.length > 0">
          <view class="font-bold mr-2">照片拍摄:</view>
          <view class="flex gap-2 flex-1 flex-wrap">
            <GoodsImage
              v-for="imgDataItem in imgData.deviceRepairMaintainFileList"
              :src="imgDataItem.filePath"
              :key="imgDataItem.id"
            />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
/**
 * @file 保养工单-详情-保养记录
 * <AUTHOR>
 * @date 2025/4/22
 */
import dayjs from 'dayjs'

import LATitle from '@/components/LATitle.vue'
import GoodsImage from '@/components/GoodsImage.vue'
interface MaintenanceRecord {
  id: number
  createDate: string
  deviceRepairMaintainFileList?: any[]
  maintenanceContent: string
  maintenanceResponsiblePersonId: string
  maintenanceResponsiblePersonName: string
  maintenanceResults: string
  newResponsiblePersonName?: string
  workOrderId: string
}
const props = defineProps({
  // 接收数据
  maintenanceRecord: {
    type: Array as PropType<MaintenanceRecord[]>,
    default: () => [{}],
  },
})
const imgData = computed(() => {
  // console.log(
  //   123,
  //   props.maintenanceRecord.find((data) => data?.deviceRepairMaintainFileList?.length > 0),
  // )
  return props.maintenanceRecord.find((data) => data?.deviceRepairMaintainFileList?.length > 0)
})
// 保养结果/保养备注的字段
const maintenanceContent = {
  transfer: '保养负责人',
  finish: '保养备注',
  evaluation: '保养备注',
}
// 工单操作描述（repairResults的三种操作描述："transfer"：'转让了工单'，"unRepaired"\"repaired":'填写了维修结果'，"closed":'关闭了工单'）
const maintenanceOperateResults = {
  transfer: '转让了工单',
  finish: '填写了维修结果',
  evaluation: '填写了保养结果',
}
</script>

<style scoped>
view {
  line-height: 100%;
}
</style>
