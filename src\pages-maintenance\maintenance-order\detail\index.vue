<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '保养工单详情',
  },
}
</route>
<template>
  <view>
    <view class="bg-white pb-2">
      <view class="p-4 flex justify-between bg-white">
        <view class="fw-bold text-base">{{ workOrderDetail?.number }}</view>
        <view class="flex gap-2 text-sm">
          <view
            v-if="workOrderDetail.overdueDays > 0"
            :class="
              (workOrderDetail.status !== 'finish' || workOrderDetail.status !== 'completed') &&
              workOrderDetail.overdueDays > 0
                ? 'text-red text-sm'
                : 'text-sm'
            "
          >
            逾期{{ workOrderDetail?.overdueDays }}天
          </view>
          <view
            class="text-sm"
            :style="{ color: maintenanceOrderStatusEnum[workOrderDetail?.status]?.color }"
          >
            {{ maintenanceOrderStatusEnum[workOrderDetail?.status]?.text }}
          </view>
        </view>
      </view>
      <view class="mx-2 bg-[#F8F9FC] gap-4 flex flex-col p-4 rounded-3 text-sm">
        <view v-for="item in detailLabel" :key="item.field" class="flex justify-between">
          <view class="text-sm text-gray">{{ item.label }}</view>
          <view class="max-w-[350rpx]">
            {{
              item.render ? item.render(workOrderDetail) : showValue(workOrderDetail[item.field])
            }}
          </view>
        </view>
      </view>
      <view
        class="px-2 pt-2 flex gap-1 justify-center bg-white h-[90rpx]"
        v-if="['waitingForMaintenance', 'notAssigned'].includes(workOrderDetail?.status)"
      >
        <view
          @click="maintenanceResults"
          v-if="workOrderDetail?.status === 'waitingForMaintenance'"
          class="rounded-3! flex-1 bg-[#356AFD] text-white text-center flex-col flex justify-center h-full text-sm"
        >
          填写保养结果
        </view>
        <wd-select-picker
          v-if="workOrderDetail?.status === 'waitingForMaintenance' && isResponsiblePerson"
          @confirm="transferRepairSubmit"
          @change="transferRepairChange"
          custom-class="flex-1 "
          required
          filterable
          filter-placeholder="搜索"
          type="radio"
          v-model="userId"
          :columns="transferPersonList"
          use-default-slot
          title="选择保养负责人"
        >
          <view
            class="rounded-3! text-sm flex-1 bg-[#F8F9FC] text-center flex-col flex justify-center h-full"
          >
            转让工单
          </view>
        </wd-select-picker>
        <wd-select-picker
          v-if="workOrderDetail?.status === 'notAssigned'"
          @confirm="distributeWorkOrderSubmit"
          @change="distributeWorkOrderChange"
          custom-class="flex-1"
          required
          filterable
          filter-placeholder="搜索"
          type="radio"
          v-model="workOrderDetail.userId"
          :columns="personList"
          use-default-slot
          title="选择保养负责人"
        >
          <view
            class="rounded-3! flex-1 text-sm bg-[#356AFD] text-white text-center flex-col flex justify-center h-full"
          >
            派工
          </view>
        </wd-select-picker>
      </view>
    </view>
    <!--保养项目-->
    <MaintenanceProject
      v-if="maintenanceProjectList?.length > 0"
      :project-list="maintenanceProjectList"
    />
    <!--保养记录-->
    <MaintenanceRecord
      v-if="maintenanceRecordData?.length > 0"
      :maintenance-record="maintenanceRecordData"
    />
    <!--保养评价-->
    <AssessmentRecord v-if="evaluateRecordData?.id" :evaluate-record-data="evaluateRecordData" />
  </view>
</template>

<script setup lang="tsx">
/**
 * @file 保养工单-详情
 * <AUTHOR>
 * @date 2025/4/19
 */
// 请求列表
import {
  getMaintenancePosition,
  assignPerson,
  getEvaluateInfo,
  getUserInfoList,
  transferMaintainWorkOrder,
  getMaintainRecordList,
  getMaintainWorkOrderDetail,
} from '@/service/maintenanceApi'

import AssessmentRecord from '@/pages-maintenance/repair-order/detail/components/AssessmentRecord.vue'
import { maintenanceOrderStatusEnum } from '@/pages-maintenance/maintenance-order/types'
import { showValue } from '@/utils'
import MaintenanceProject from '@/pages-maintenance/maintenance-order/detail/components/MaintenanceProject.vue'
import MaintenanceRecord from '@/pages-maintenance/maintenance-order/detail/components/MaintenanceRecord.vue'
import { useToast } from 'wot-design-uni'
import { useUserStore } from '@/store'

const toast = useToast()

interface DetailItem {
  label: string
  field: string
  render?: (value: any) => VNode | string
}

const userId = ref()
// 工单id
const workOrderId = ref()
// 工单详情数据
const workOrderDetail = ref<Record<string, any>>({})
// 保养记录数据
const maintenanceRecordData = ref()
// 保养项目列表
const maintenanceProjectList = ref()
// 保养负责人列表
const personList = ref([])
// 转让负责人列表
const transferPersonList = ref([])
// 工单评价记录数据
const evaluateRecordData = ref()
// 刷新
const refresh = async () => {
  // 获取保养工单详情
  await getMaintainWorkOrderDetail({ id: workOrderId.value }).then((res: any) => {
    workOrderDetail.value = {
      ...res.data,
      overdueDays: workOrderDetail.value?.overdueDays,
    }
  })

  // 获取保养记录
  await getMaintainRecordList({
    workOrderId: workOrderDetail.value?.id,
  }).then((res) => {
    maintenanceRecordData.value = res.data
  })
  // 获取评价信息
  await getEvaluateInfo({
    transactionId: workOrderId.value,
    type: 'maintenance',
  }).then((res) => {
    evaluateRecordData.value = res?.data || {}
  })
}
// 事件通道，在onLoad中进行获取
let eventChannel: {
  on: (eventName: string, cb: (...args: any) => void) => void
  emit: (eventName: string, ...args: any) => void
}
onUnload(() => {
  eventChannel.emit('refresh')
})
onLoad(async (options) => {
  const instance = getCurrentInstance().proxy
  // @ts-expect-error - eventChannel
  eventChannel = instance.getOpenerEventChannel()
  workOrderDetail.value = JSON.parse(options.data)
  console.log(workOrderDetail)
  workOrderId.value = workOrderDetail.value.id

  await refresh()

  // 请求保养负责人列表
  await getUserInfoList({
    loginType: 'maintenance',
  }).then((res: any) => {
    const resData = JSON.parse(JSON.stringify(res.data))
    // 派工
    personList.value = resData.map((item) => ({
      label: item.employeeName,
      value: item.id,
    }))
    // 转让
    transferPersonList.value = resData
      .map((item) => ({
        label: item.employeeName,
        value: item.id,
      }))
      .filter((item) => item.value !== workOrderDetail.value.userId)
  })
  await getMaintenancePosition({
    workOrderId: workOrderDetail.value?.id,
  }).then((res) => {
    maintenanceProjectList.value = res.data
  })
})
// 判断当前登录用户是否是保养负责人
const isResponsiblePerson = computed(() => {
  return workOrderDetail.value.userId === useUserStore().userInfo.id
})
// 详情项配置
const detailLabel = reactive<DetailItem[]>([
  { label: '奶茶车名称', field: 'deviceName' },
  {
    label: '奶茶车型号',
    field: 'modelNumber',
  },
  {
    label: '保养间隔',
    field: 'cycle',
    render: (data) => {
      return `${data?.cycle || 0}天`
    },
  },
  { label: '工单生成时间', field: 'maintenanceTime' },
  { label: '保养负责人', field: 'chargePerson' },
  { label: '保养完成时间', field: 'completionTime' },
])
// 填写保养结果
const maintenanceResults = () => {
  uni.navigateTo({
    url: `/pages-maintenance/maintenance-order/detail/writeMaintenanceResult?id=${workOrderId.value}`,
    // 刷新列表
    success: (opt) => {
      opt.eventChannel.on('writeResultSuccess', () => {
        refresh()
      })
    },
  })
}
// 转让工单
const transferRepairForm = ref()
const transferRepairChange = (e) => {
  const val = e.value
  const data = personList.value.find((item) => item.value === val)
  transferRepairForm.value = {
    id: workOrderId.value,
    newResponsiblePersonId: data.value,
    newResponsiblePersonName: data.label,
  }
}
// 转让工单
const transferRepairSubmit = () => {
  transferMaintainWorkOrder(transferRepairForm.value)
    .then((res) => {
      if (res.success) {
        toast.success('转让成功')
        // refresh()
        eventChannel.emit('refresh')
        // 返回上一页
        setTimeout(uni.navigateBack, 500)
      } else {
        toast.error(res.message)
      }
    })
    .catch((err) => {
      console.log(err)
    })
}
// 派发表单
const distributeWorkOrderForm = ref()
const distributeWorkOrderChange = (e) => {
  const val = e.value
  const data = personList.value.find((item) => item.value === val)
  distributeWorkOrderForm.value = {
    workOrderId: workOrderId.value,
    personName: data.label,
    userId: data.value,
  }
}
// 派发工单
const distributeWorkOrderSubmit = () => {
  assignPerson(distributeWorkOrderForm.value)
    .then((res) => {
      if (res.success) {
        toast.success('派工成功')
        refresh()
      } else {
        toast.error(res.message)
      }
    })
    .catch((err) => {
      console.log(err)
    })
}
</script>

<style scoped>
view {
  line-height: 100%;
}

:deep(.wd-select-picker__field.wd-select-picker__field) {
  height: 100%;
}
:deep(.wd-select-picker__footer.wd-select-picker__footer .wd-button) {
  background: var(--theme-color-ops, #356afd);
  border-radius: 11px;
}
</style>
