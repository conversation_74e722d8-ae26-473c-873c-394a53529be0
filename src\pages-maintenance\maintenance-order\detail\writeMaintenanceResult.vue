<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '填写保养结果',
  },
}
</route>

<template>
  <view>
    <uni-forms
      ref="formRef"
      :modelValue="formData"
      :rules="rules"
      border
      err-show-type="toast"
      label-position="top"
      label-width="100"
    >
      <uni-forms-item required label="保养备注" name="maintenanceContent">
        <uni-easyinput
          class="pl-2"
          v-model="formData.maintenanceContent"
          :inputBorder="false"
          :maxlength="200"
          placeholder="请输入"
          placeholderStyle="font-size:16px"
          type="textarea"
        />
      </uni-forms-item>
      <uni-forms-item required label="照片拍摄" name="photoList">
        <CameraSheet :min="1" v-model="formData.photoList">
          <wd-cell
            :title="
              formData.photoList?.length ? `已拍摄${formData.photoList?.length}张` : '点击拍摄'
            "
            is-link
          />
        </CameraSheet>
      </uni-forms-item>
      <view>
        <LAButton :round="false" size="lg" block @click="handleSubmit">确定</LAButton>
      </view>
    </uni-forms>
  </view>
</template>

<script setup lang="ts">
import { modifyMaintainWorkOrder } from '@/service/maintenanceApi'
import CameraSheet from '@/components/camera-sheet.vue'
import { useToast } from 'wot-design-uni'
const toast = useToast()

/**
 * @file 保养工单-详情-填写保养结果
 * <AUTHOR>
 * @date 2025/4/17
 */
const formRef = ref()
const formData = reactive({
  maintenanceContent: '',
  photoList: [],
})
const rules = {
  maintenanceContent: { rules: [{ required: true, errorMessage: '请输入保养备注' }] },
  photoList: {
    rules: [{ required: true, errorMessage: '请至少拍摄一张照片' }],
  },
}

const workOrderId = ref()
// 事件通道，在onLoad中进行获取
let eventChannel: {
  on: (eventName: string, cb: (...args: any) => void) => void
  emit: (eventName: string, ...args: any) => void
}
// 获取事件通道和维修工单ID
onLoad((option) => {
  const instance = getCurrentInstance().proxy
  // @ts-expect-error - eventChannel
  eventChannel = instance.getOpenerEventChannel()

  workOrderId.value = option.id
})

/**
 * 点击提交之后执行的函数
 */
const handleSubmit = () => {
  formRef.value
    .validate()
    .then((data) => {
      const params = {
        id: workOrderId.value,
        maintenanceContent: formData?.maintenanceContent,
        // 只有 photoList 有元素时才添加字段
        filePaths: formData.photoList.join(','),
        maintenanceResults: 'completed',
      }
      modifyMaintainWorkOrder(params).then((res) => {
        if (res.success) {
          eventChannel.emit('writeResultSuccess')
          toast.success('提交成功')
          setTimeout(uni.navigateBack, 500)
        }
      })
    })
    .catch((err) => {
      console.log('表单错误信息：', err)
    })
}
</script>

<style scoped>
:deep(.uni-forms) {
  @apply bg-white px-4 pb-2;
}
:deep(.wd-cell.wd-cell) {
  @apply pl-2;
}
</style>
