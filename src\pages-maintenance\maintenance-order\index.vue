<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '保养工单' },
}
</route>

<template>
  <view>
    <PageList :requestApi="getMaintainWorkOrderList" :search="search">
      <template #default="{ item, refresh }">
        <wd-card custom-class="m-2! mb-0 rounded-xl! p-4!">
          <template #title>
            <view class="flex items-center justify-between" @click="toDetail(item, refresh)">
              <view class="font-bold">{{ item.number }}</view>
              <view class="flex items-center gap-1">
                <view
                  class="text-sm"
                  v-if="item.overdueDays > 0"
                  :class="
                    (item.status !== 'completed' || item.status !== 'finish') &&
                    item.overdueDays > 0
                      ? 'text-red'
                      : ''
                  "
                >
                  逾期{{ item?.overdueDays }}天
                </view>
                <view
                  class="text-sm"
                  :style="{ color: maintenanceOrderStatusEnum[item.status]?.color }"
                >
                  {{ maintenanceOrderStatusEnum[item.status]?.text }}
                </view>
                <wd-icon
                  name="chevron-right"
                  size="16px"
                  :color="maintenanceOrderStatusEnum[item.status]?.color"
                ></wd-icon>
              </view>
            </view>
          </template>
          <wd-divider custom-class="mt-0! p-0! " color="#EBEDF1" />
          <view class="flex flex-col gap-2" @click="toDetail(item, refresh)">
            <view class="flex justify-between" v-for="val in detailLabel" :key="val.field">
              <text class="text-[#979998]">{{ val.label }}</text>
              <text class="text-[#1A1A1A]">{{ showValue(item?.[val.field]) }}</text>
            </view>
          </view>
        </wd-card>
      </template>
    </PageList>
  </view>
</template>
<script setup lang="ts">
/**
 * @file 维修工单-列表
 * <AUTHOR>
 * @date 2025/4/16
 */
import {
  getSellCarListAll,
  getMaintainWorkOrderList,
  getAllCycleList,
} from '@/service/maintenanceApi'
import { maintenanceOrderStatusEnum } from '@/pages-maintenance/maintenance-order/types'
import { showValue } from '@/utils'

// 搜索插槽
const search = {
  placeholder: '保养工单号/保养人',
  // 自定义请求中search字段名
  searchKey: 'search',
  options: [
    {
      prop: 'status',
      title: '全部状态',
      options: [
        { label: '未派工', value: 'notAssigned' },
        { label: '待保养', value: 'waitingForMaintenance' },
        { label: '待评价', value: 'evaluation' },
        { label: '已关闭', value: 'completed' },
      ],
    },
    {
      prop: 'deviceId',
      title: '奶茶车名称',
      requestApi: getSellCarListAll,
      replaceOptions: {
        label: 'carName',
        value: 'id',
      },
    },
    {
      prop: 'cycle',
      title: '保养间隔',
      requestApi: () => getAllCycleList({ deviceType: 'deviceSellCar' }),
      replaceOptions: {
        label: 'cycle',
        value: 'cycle',
      },
    },
    {
      prop: 'createDateStr',
      slot: true, // 开启自定义插槽
      slotType: 'date', // 日期
      title: '生成时间',
    },
  ],
}

const detailLabel = [
  { label: '奶茶车名称', field: 'deviceName' },
  { label: '保养间隔(天)', field: 'cycle' },
  { label: '工单生成时间', field: 'maintenanceTime' },
  { label: '保养负责人', field: 'chargePerson' },
]
const toDetail = (item: any, refresh: any) => {
  uni.navigateTo({
    url: `/pages-maintenance/maintenance-order/detail/index?data=${JSON.stringify(item)}`,
    ...refresh,
  })
}
</script>
<style scoped lang="scss">
view {
  line-height: 100%;
}
:deep(.wd-card__title-content.wd-card__title-content) {
  @apply pt-0;
}
</style>
