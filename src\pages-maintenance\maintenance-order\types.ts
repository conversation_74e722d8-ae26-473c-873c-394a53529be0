// 状态枚举
export enum StatusEnum {
  /** 未派工 */
  NOT_ASSIGNED = 'notAssigned',
  /** 待保养 */
  TO_MAINTAIN = 'waitingForMaintenance',
  /** 已关闭 */
  CLOSED = 'completed',
  /** 待评价 */
  EVALUATION = 'evaluation',
  /** 已完成 */
  FINISH = 'finish',
}
// 工单状态信息
export const maintenanceOrderStatusEnum = {
  [StatusEnum.TO_MAINTAIN]: {
    bg: 'rgba(0, 194, 144, 0.1)',
    color: 'rgba(0, 194, 144, 1)',
    text: '待保养',
  },
  [StatusEnum.CLOSED]: {
    bg: 'rgba(101, 102, 102, 0.1)',
    color: 'rgba(101, 102, 102, 1)',
    text: '已关闭',
  },
  [StatusEnum.FINISH]: {
    bg: 'rgba(101, 102, 102, 0.1)',
    color: 'rgba(101, 102, 102, 1)',
    text: '已完成',
  },
  [StatusEnum.EVALUATION]: {
    bg: 'rgba(119, 102, 249, 0.1)',
    color: 'rgba(119, 102, 249, 1)',
    text: '待评价',
  },
  [StatusEnum.NOT_ASSIGNED]: {
    bg: 'rgba(53, 106, 253, 0.1)',
    color: 'rgba(53, 106, 253, 1)',
    text: '未派工',
  },
}
