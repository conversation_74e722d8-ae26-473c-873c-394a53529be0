<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '车辆定位' },
}
</route>
<template>
  <view>
    <view class="w-screen p-2 flex gap-2 box-border fixed top-0 left-0 z-10">
      <wd-select-picker
        placeholder="运营区域名称"
        custom-class="w-1/2 maintance-map__select shadow-md"
        custom-content-class="pb-15!"
        v-model="params.areaId"
        :columns="areaList"
        :show-confirm="false"
        size="large"
        type="radio"
        :ellipsis="true"
        filterable
        title="选择运营区域"
        filter-placeholder="搜索运营区域"
      />
      <wd-select-picker
        ref="carSelectRef"
        placeholder="奶茶车名称"
        custom-class="w-1/2 maintance-map__select shadow-md"
        custom-content-class="pb-15!"
        v-model="params.carId"
        :columns="carList"
        @change="handleCarChange"
        :show-confirm="false"
        size="large"
        type="radio"
        :ellipsis="true"
        @open="handleCarOpen"
        filterable
        title="选择奶茶车"
        filter-placeholder="搜索奶茶车"
      />
    </view>

    <!-- 全屏地图 -->
    <map
      id="map"
      class="map"
      :longitude="centerLongitude"
      :latitude="centerLatitude"
      :scale="mapScale"
      :markers="allMarkers"
      :polyline="polylines"
      :show-location="true"
      :enable-3D="false"
      :show-compass="false"
      :enable-zoom="true"
      :enable-scroll="true"
      :enable-rotate="false"
      :enable-overlooking="false"
      :enable-satellite="false"
      :enable-traffic="false"
      @markertap="eventHandlers.onMarkerTap"
      @callouttap="eventHandlers.onCalloutTap"
      @controltap="handleControlTap"
      @regionchange="eventHandlers.onRegionChange"
      @tap="eventHandlers.onMapTap"
      @updated="eventHandlers.onMapUpdated"
    />

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-overlay">
      <view class="loading-spinner"></view>
      <text class="loading-text">{{ loadingText }}</text>
    </view>

    <view
      class="w-11 h-11 rounded-lg bg-white fixed bottom-24 right-4 z-9 flex items-center justify-center"
      @tap="onLocation"
    >
      <image :src="IconLocation" class="w-7 h-7" />
    </view>
    <TabBar v-model="activeTab" />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, watch } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useMap } from '@/hooks/useMap'
import { useMapControls } from '@/hooks/useMapControls'
import MarkerCar from './imgs/MarkerCar.png?url'
import IconLocation from './imgs/IconLocation.svg?url'
import TabBar from '@/components/TabBar.vue'
import { getSellAreaByLocation, getSellCarList } from '@/service/api'
import useNativeLocation from '@/hooks/useNativeLocation'
const carSelectRef = ref()
const { getRawLocation } = useNativeLocation()

// 初始化地图 hooks
const mapHooks = useMap({
  mapId: 'map',
  longitude: 116.397428,
  latitude: 39.90923,
  scale: 16,
  autoLocation: true,
})

// 解构地图相关数据和方法
const {
  centerLongitude,
  centerLatitude,
  mapScale,
  loading,
  loadingText,
  markers,
  polylines,
  allMarkers,
  createEventHandlers,
  init,
  moveToLocation,
  includePoints,
} = mapHooks

// 初始化控件 hooks
const controlsHooks = useMapControls(mapHooks)

// 解构控件相关数据和方法
const { showControls, handleGetCurrentLocation, handleControlTap } = controlsHooks

// 添加一些初始标记点
onMounted(() => {
  // 初始化地图
  init()
})

// 创建事件处理器
const eventHandlers = createEventHandlers({
  onMapTap: (event) => {},
  onRegionChange: (event) => {},
  onMapUpdated: (event) => {},
})

const onLocation = async () => {
  const location = await getRawLocation()
  if (location) {
    moveToLocation(location.longitude, location.latitude)
  }
}

// 类型定义
export interface AreaListItem {
  id: string
  name: string
  distance: number
  boxCoordinate: string
}

export interface CarListItem {
  id: string
  name: string
  longitude?: string
  latitude?: string
  label?: string
  value?: string
}
const activeTab = ref('maintenanceMap')
const areaList = ref<AreaListItem[]>([])
const carList = ref<CarListItem[]>([])
const params = reactive({ carId: '', areaId: '' })
// 获取奶茶车列表
function fetchCarList() {
  if (!params.areaId) return
  getSellCarList({ runAreaId: params.areaId, carAuth: 1 }).then((res: any) => {
    if (!Array.isArray(res.data?.records)) return
    // 筛选具有经纬度数据的点
    const hasLocationMarkers = res.data.records
      .map((item, index) => {
        const { location, carName, id } = item
        if (!location) return null
        const [longitude, latitude] = location.split(' ')
        return {
          label: carName,
          value: id,
          longitude,
          latitude,
          id: index,
          iconPath: MarkerCar,
          width: 40,
          height: 40,
          callout: {
            content: carName,
            color: '#1A1A1A',
            fontSize: 12,
            borderRadius: 4,
            bgColor: '#ffffff',
            padding: 8,
            display: 'ALWAYS',
          },
        }
      })
      .filter(Boolean)
    // 奶茶车名称列表
    carList.value = hasLocationMarkers
    // markers 数据如果包含其他的属性会导致手机端无法正常显示
    markers.value = hasLocationMarkers.map((item) => {
      const { label, value, ...rest } = item
      return rest
    })
  })
}

// 奶茶车列表选择
function handleCarChange({ value }: { value: string }) {
  const car = carList.value.find((item: any) => item.value === value)
  if (car) {
    // 奶茶车 change 时，调用方法把点居中显示
    if (car.longitude && car.latitude) {
      moveToLocation(Number(car.longitude), Number(car.latitude))
    }
  }
}

// 奶茶车列表打开
function handleCarOpen() {
  if (!params.areaId) {
    uni.showToast({ title: '请先选择运营区域', icon: 'none' })
    carSelectRef.value.close()
  }
}

// 解析区域坐标并移动视角到 bbox
function moveToAreaBbox(boxCoordinate: string) {
  try {
    // 解析坐标字符串 "[["104.066783","30.554332"],["104.068639","30.554332"],["104.068639","30.5515"],["104.066783","30.5515"]]"
    const coordinates = JSON.parse(boxCoordinate)
    if (Array.isArray(coordinates) && coordinates.length > 0) {
      // 转换为地图需要的点格式
      const points = coordinates.map((coord) => ({
        longitude: Number(coord[0]),
        latitude: Number(coord[1]),
      }))

      // 使用 includePoints 方法调整视角包含所有坐标点
      includePoints(points, 50) // 50px padding
    }
  } catch (error) {
    console.error('解析区域坐标失败:', error)
  }
}

let interval: NodeJS.Timeout | null

watch(
  () => params.areaId,
  () => {
    // 清空当前标记点
    markers.value = []

    // 获取奶茶车列表
    fetchCarList()

    // 定位地图区域到已选择区域
    const area = areaList.value.find((item: any) => item.value === params.areaId)
    // 选中区域后调用方法移动视角到 bbox
    if (area?.boxCoordinate) {
      moveToAreaBbox(area.boxCoordinate)
    }
  },
)

// 获取实时定位
const onLocationChange = (res: any) => {
  console.log('纬度：' + res.latitude)
  console.log('经度：' + res.longitude)
}

onShow(() => {
  interval = setInterval(fetchCarList, 5000)
  uni.onLocationChange(onLocationChange)
})

onHide(() => {
  clearInterval(interval)
  uni.offLocationChange(onLocationChange)
})

onUnload(() => {
  clearInterval(interval)
  uni.offLocationChange(onLocationChange)
})

onLoad(async () => {
  uni.hideHomeButton()
  const location = await getRawLocation()
  const params: any = {}
  if (location?.longitude && location?.latitude) {
    params.location = [location.longitude, location.latitude].join(' ')
  }
  const res = await getSellAreaByLocation(params)
  if (Array.isArray(res.data)) {
    areaList.value = res.data.map((item) => {
      return {
        label: item.name,
        value: item.id,
        boxCoordinate: item.boxCoordinate,
        distance: item.distance,
      }
    }) as unknown as AreaListItem[]
  }
})
</script>
<style lang="scss">
.maintance-map__select {
  .wd-select-picker__body {
    width: 100%;
  }
  .wd-select-picker__cell {
    border-radius: 16rpx !important;
    .wd-select-picker__value {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
</style>

<style scoped>
.map {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1;
}

.demo-header {
  position: fixed;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 8px 16px;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.demo-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.map-controls {
  position: fixed;
  right: 20px;
  top: 60px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  z-index: 1000;
}

.control-btn {
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: background-color 0.2s;
}

.control-btn:hover {
  background-color: rgba(255, 255, 255, 1);
}

.control-btn:active {
  background-color: rgba(240, 240, 240, 1);
}

.control-icon {
  width: 20px;
  height: 20px;
}

.control-text {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 2000;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.control-panel {
  position: fixed;
  top: 60px;
  left: 10px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 16px;
  max-width: 280px;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.control-section {
  margin-bottom: 16px;
}

.control-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

.control-row {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.setting-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.setting-row:last-child {
  border-bottom: none;
}

.setting-label {
  font-size: 13px;
  color: #666;
}

.preset-locations {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.preset-btn {
  margin: 0;
  font-size: 12px;
}

.info-panel {
  position: fixed;
  bottom: 10px;
  right: 10px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 16px;
  max-width: 280px;
  max-height: 300px;
  overflow-y: auto;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.info-section {
  margin-bottom: 16px;
}

.info-section:last-child {
  margin-bottom: 0;
}

.info-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
  display: block;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-text {
  font-size: 12px;
  color: #666;
  padding: 4px 0;
  border-bottom: 1px solid #f8f8f8;
  line-height: 1.4;
}

.info-text:last-child {
  border-bottom: none;
}

/* 响应式调整 */
@media (max-width: 480px) {
  .control-panel {
    left: 5px;
    max-width: calc(100vw - 80px);
    padding: 12px;
  }

  .info-panel {
    right: 5px;
    bottom: 5px;
    max-width: calc(100vw - 80px);
    padding: 12px;
  }

  .map-controls {
    right: 10px;
    top: 50px;
  }

  .demo-header {
    top: 5px;
    left: 50%;
    transform: translateX(-50%);
    padding: 6px 12px;
  }

  .demo-title {
    font-size: 14px;
  }
}
</style>
