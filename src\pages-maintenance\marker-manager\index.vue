<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '目标点管理' },
}
</route>
<template>
  <PageList :requestApi="getMapAreaList" :search="search">
    <template #default="{ item }">
      <view class="m-2">
        <wd-cell
          :title="item.name"
          :label="item.address || '-'"
          is-link
          :to="`/pages-maintenance/marker-manager/list?mapId=${item.id}&name=${item.name}`"
          custom-class="rounded-lg"
          custom-title-class="font-bold text-lg -mt-1 whitespace-nowrap"
          custom-label-class="text-[#1A1A1A]! text-sm!"
        ></wd-cell>
      </view>
    </template>
  </PageList>
</template>
<script lang="ts" setup>
import { getMapAreaList } from '@/service/api'
const search = { placeholder: '区域名称', searchKey: 'name' }
</script>
