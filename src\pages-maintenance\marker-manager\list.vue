<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '目标点管理' },
}
</route>
<template>
  <PageList :requestApi="fetchList" :search="search">
    <template #default="{ item, reload }">
      <view class="m-2">
        <wd-cell
          :title="item.name"
          :label="item.remark || '-'"
          custom-class="rounded-lg"
          custom-title-class="font-bold text-lg -mt-1"
          custom-label-class="text-[#1A1A1A]! text-sm!"
        >
          <view class="flex justify-end gap-2">
            <LAButton
              custom-class="w-17"
              :round="false"
              size="lg"
              pale
              @click="openEditPopup(item, reload)"
            >
              修改
            </LAButton>
            <LAButton
              custom-class="w-17"
              :round="false"
              size="lg"
              pale
              type="error"
              @click="onRemove(item, reload)"
            >
              删除
            </LAButton>
          </view>
        </wd-cell>
      </view>
    </template>
  </PageList>
  <MarkPointPopup ref="markPointPopupRef" :fetch="fetch" :data="selectedItem" />
  <wd-message-box selector="delete-target-point-message-box" custom-class="message-box-confirm" />
</template>
<script lang="ts" setup>
import MarkPointPopup from '../sell-car/detail/MarkPointPopup.vue'
import { getTargetPointList, editTargetPoint, deleteTargetPoint } from '@/service/api'
import { onLoad } from '@dcloudio/uni-app'
import { useMessage, useToast } from 'wot-design-uni'
const toast = useToast()
const message = useMessage('delete-target-point-message-box')
const markPointPopupRef = ref()
const search = { placeholder: '目标点名称', searchKey: 'name' }
const selectedItem = ref()
// 刷新列表数据方法
let reloadList = () => {}
// 编辑目标点请求
const fetch = (params: any) => {
  return editTargetPoint({ ...params, id: params.id }).then((res) => {
    reloadList()
    return res
  })
}
// 地图id
const mapId = ref()

const fetchList = (params: any) => {
  return getTargetPointList({ ...params, mapId: mapId.value })
}

const openEditPopup = (item: any, reload: any) => {
  reloadList = reload
  // todo 修改相关显示的字段
  selectedItem.value = item
  markPointPopupRef.value.show({ title: '修改目标点-' + item.name })
}

const onRemove = (item: any, reload: any) => {
  message
    .confirm({
      msg: '删除后不可恢复',
      title: '确定删除？',
      cancelButtonProps: {
        round: false,
        size: 'large',
        block: true,
        customStyle: 'background: rgba(248, 249, 252, 1); border: none; width: 100%',
      },
      confirmButtonProps: {
        round: false,
        size: 'large',
        customStyle: 'background: rgba(53, 106, 253, 1); border: none; width: 100%',
      },
    })
    .then(() => {
      deleteTargetPoint({ id: item.id }).then((res) => {
        if (res.statusCode !== 101) {
          toast.error(res.message)
        } else {
          toast.success('操作成功')
          reload()
        }
      })
    })
}

onLoad((options) => {
  mapId.value = options.mapId

  uni.setNavigationBarTitle({
    title: options.name,
  })
})
</script>
