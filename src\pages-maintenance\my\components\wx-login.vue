<template>
  <view
    class="w-full h-16 bg-white flex justify-between items-center p-4 box-border"
    @click="login"
  >
    <view class="flex items-center">
      <wd-img :height="40" :src="myLogin" :width="40" round />
      <text class="font-bold text-lg ml-1">{{ name }}你好,请登录</text>
    </view>
    <view><wd-icon name="arrow-right" size="22px"></wd-icon></view>
  </view>
</template>

<script lang="ts" name="WxLogin" setup>
import myLogin from '../../../static/svg/myLogin.svg'
import { useUserStore } from '@/store'

const show = ref(false)
const userStore = useUserStore()
const name = computed(() => userStore.userInfo?.userName)
const logout = () => {
  uni.showModal({
    title: '确认退出当前账号？',
    success: (res) => {
      if (res.confirm) {
        userStore.clearUserInfo()
      }
    },
  })
}
// 登录跳转
const login = () => {
  userStore.clearUserInfo()
  uni.navigateTo({ url: '/pages/login/index' })
}
</script>
