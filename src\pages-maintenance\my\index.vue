<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '我的运维' },
}
</route>
<template>
  <view class="flex flex-col h-screen w-full overflow-hidden">
    <view class="flex-1 flex flex-col overflow-x-hidden justify-between">
      <wd-cell custom-class="user-maintenance" center is-link to="/pages/my/accountInformation">
        <template #title>
          <view class="flex items-center">
            <img alt="" class="w-12 h-12 rounded-full mr-2" src="./imgs/profilePicture.png" />
            <view class="flex flex-col">
              <view class="text-black text-lg font-bold">{{ name || '-' }}</view>
              <view class="text-gray text-sm">
                {{ useUserStore().userInfo?.mobile || '-' }}
              </view>
            </view>
          </view>
        </template>
      </wd-cell>
      <view class="w-full mt-auto p-2 box-border">
        <LAButton custom-class="custom-btn" @click="logOut">
          <jumpTo />
          退出登录
        </LAButton>
      </view>
      <!--      <wd-cell-->
      <!--        v-for="item in functionList"-->
      <!--        :key="item.title"-->
      <!--        :title="item.title"-->
      <!--        :to="item.path"-->
      <!--        is-link-->
      <!--        custom-class="mt-2 mx-2  rounded-lg bg-white"-->
      <!--      >-->
      <!--        <template #icon>-->
      <!--          <img :src="item.img" alt="" class="w-[50rpx] h-[50rpx] mr-1" />-->
      <!--        </template>-->
      <!--      </wd-cell>-->
    </view>
    <TabBar v-model="activeTab"></TabBar>
  </view>
</template>

<script lang="ts" setup>
/**
 * @file tab-运维-我的
 * <AUTHOR>
 * @date 2025/2/26
 */
import { useUserStore } from '@/store'
import TabBar from '@/components/TabBar.vue'
import profilePicture from './imgs/profilePicture.png'
const activeTab = ref('maintenanceMy')
const userStore = useUserStore()
const name = computed(() => useUserStore().userInfo?.employeeName)
onLoad(() => {
  uni.hideHomeButton()
})
// 功能字段数据
const functionList = [
  {
    title: '联系客服',
    path: '/pages/my/MyPageButton/Contact',
    img: profilePicture,
  },
  {
    title: '隐私政策',
    path: '/pages/my/MyPageButton/Privacy',
    img: profilePicture,
  },
  {
    title: '关于我们',
    path: '/pages/my/MyPageButton/About',
    img: profilePicture,
  },
  {
    title: '操作说明',
    path: '/pages/my/MyPageButton/Guide',
    img: profilePicture,
  },
]
// 登录跳转
const logOut = () => {
  userStore.clearUserInfo()

  uni.switchTab({ url: '/pages/my/index' })
}
</script>
<script lang="ts">
export default {
  options: {
    styleIsolation: 'shared',
  },
}
</script>
<style lang="scss" scoped>
:deep(.user-maintenance) {
  padding-left: 0 !important;
  .wd-cell__wrapper {
    @apply p-4;
  }
  .wd-cell__right {
    display: none;
  }
}
:deep(.custom-btn.custom-btn) {
  color: #f25555ff !important;
  border: none !important;
  border-radius: 12px !important;
  padding: 10px 0 !important;
  box-sizing: content-box;
  width: 100% !important;
  background: white !important;
}
</style>
