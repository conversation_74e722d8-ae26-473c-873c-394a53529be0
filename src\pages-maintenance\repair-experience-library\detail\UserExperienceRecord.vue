<template>
  <view class="bg-white mt-2 p-4">
    <LATitle title="用户维修经验记录">
      <view class="flex text-[#356AFD]">
        <wd-icon name="add" size="16px"></wd-icon>
        <text class="ml-1" @click="jumpForm()">新增</text>
      </view>
    </LATitle>

    <view
      class="flex mt-4 text-sm"
      v-for="(recordData, i) in repairExperienceRecordData"
      :key="recordData.id"
    >
      <view class="font-bold w-[200rpx]">{{ recordData.personName }}</view>
      <view
        class="flex gap-4 flex-col flex-1 px-2 pb-2"
        :class="
          i < repairExperienceRecordData.length - 1
            ? 'border-b border-b-solid border-b-color-[#EFF1F5]'
            : ''
        "
      >
        <view>{{ dayjs(recordData.createDate).format('YYYY-MM-DD HH:mm:ss') }}</view>
        <view class="whitespace-pre-line">{{ recordData.record }}</view>
        <view class="flex gap-1 items-center text-sm">
          <view class="text-[#356AFD]" @click="jumpForm(recordData)">修改</view>
          <wd-divider custom-class="!h-[0.875rem]" vertical />
          <view class="text-[#F25555]" @click="onRemove(recordData)">删除</view>
        </view>
      </view>
    </view>
  </view>
  <wd-message-box selector="delete-experience-message-box" custom-class="message-box-confirm" />
</template>

<script setup lang="ts">
import LATitle from '@/components/LATitle.vue'
import dayjs from 'dayjs'
import { useMessage, useToast } from 'wot-design-uni'
import { deleteRepairExperienceRecord } from '@/service/maintenanceApi'
const message = useMessage('delete-experience-message-box')
const toast = useToast()

const props = defineProps({
  repairExperienceRecordData: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
  experienceId: {
    type: String,
    default: '',
  },
})
const emit = defineEmits(['refresh'])
const jumpForm = (data?) => {
  uni.navigateTo({
    url: `/pages-maintenance/repair-experience-library/detail/experienceForm?data=${JSON.stringify(
      data || {
        experienceId: props.experienceId,
      },
    )}`,
    // 刷新列表
    success: (opt) => {
      opt.eventChannel.on('success', () => {
        console.log(2222)
        emit('refresh')
      })
    },
  })
}

const onRemove = (item: any) => {
  message.confirm({ msg: '删除后不可恢复', title: '确定删除？' }).then(() => {
    deleteRepairExperienceRecord({ id: item.id }).then(() => {
      toast.success('删除成功')
      emit('refresh')
    })
  })
}
</script>

<style scoped>
view {
  line-height: 100%;
}
</style>
