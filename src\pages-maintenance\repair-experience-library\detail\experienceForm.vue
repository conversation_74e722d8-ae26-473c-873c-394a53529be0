<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '新增用户维修经验',
  },
}
</route>

<template>
  <view class="h-full flex-col flex justify-between">
    <uni-forms
      class="bg-white px-4 pb-2"
      ref="formRef"
      :modelValue="formData"
      :rules="rules"
      border
      err-show-type="toast"
      label-position="top"
      label-width="100"
    >
      <uni-forms-item required label="维修经验" name="record">
        <wd-textarea v-model="formData.record" :maxlength="500" clearable show-word-limit />
      </uni-forms-item>
    </uni-forms>
    <view class="bg-white px-2 bottom-0 pt-2">
      <LAButton size="lg" :round="false" block @click="handleSubmit">确定</LAButton>
      <wd-gap safe-area-bottom height="0"></wd-gap>
    </view>
  </view>
</template>

<script setup lang="ts">
import { saveRepairExperienceRecord } from '@/service/maintenanceApi'
import { useToast } from 'wot-design-uni'
const toast = useToast()

/**
 * @file 用户维修经验-详情-新增编辑表单
 * <AUTHOR>
 * @date 2025/4/19
 */
const formRef = ref()
const formData = reactive({
  record: '',
  experienceId: '',
})
const rules = {
  record: { rules: [{ required: true, errorMessage: '请输入维修经验' }] },
}

const experienceDetail = ref()
// 事件通道，在onLoad中进行获取
let eventChannel: {
  on: (eventName: string, cb: (...args: any) => void) => void
  emit: (eventName: string, ...args: any) => void
}
// 获取事件通道和维修工单ID
onLoad((option) => {
  const instance = getCurrentInstance().proxy
  // @ts-expect-error - eventChannel
  eventChannel = instance.getOpenerEventChannel()
  experienceDetail.value = JSON.parse(option.data)
  formData.experienceId = experienceDetail.value.experienceId
  if (experienceDetail.value.id) {
    formData.record = experienceDetail.value.record
  }
  uni.setNavigationBarTitle({
    title: experienceDetail.value.id ? '编辑用户经验记录' : '新增用户经验记录',
  })
})

/**
 * 点击提交之后执行的函数
 */
const handleSubmit = () => {
  console.log('点击提交')

  formRef.value
    .validate()
    .then((data) => {
      const params = {
        ...formData,
        ...(experienceDetail.value.id && {
          id: experienceDetail.value.id,
        }),
      }
      saveRepairExperienceRecord(params).then((res) => {
        if (res) {
          eventChannel.emit('success')
          toast.success('提交成功')
          setTimeout(uni.navigateBack, 500)
        }
      })
    })
    .catch((err) => {
      console.log('表单错误信息：', err)
    })
}
</script>

<style scoped>
:deep(.wd-cell.wd-cell) {
  @apply pl-2;
}
</style>
