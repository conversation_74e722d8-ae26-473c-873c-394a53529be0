<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '维修经验库' },
}
</route>
<template>
  <view>
    <PageList :requestApi="fetch" :search="search">
      <template #default="{ item, refresh }">
        <wd-card custom-class="mt-2! rounded-xl! py-4!">
          <template #title>
            <view class="flex items-center justify-between" @click="toDetail(item, refresh)">
              <view class="font-bold text-base">{{ item.faultCode }}</view>
              <view class="flex items-center">
                <wd-icon name="chevron-right" size="16px" color="#656666"></wd-icon>
              </view>
            </view>
          </template>
          <wd-divider custom-class="mt-0! p-0! bg-[#EBEDF1]" />
          <view class="flex flex-col gap-4" @click="toDetail(item, refresh)">
            <view class="flex justify-between text-sm" v-for="val in detailLabel" :key="val.field">
              <text class="text-[#979998]">{{ val.label }}</text>
              <text class="text-[#1A1A1A]">{{ item?.[val.field] }}</text>
            </view>
          </view>
        </wd-card>
      </template>
    </PageList>
  </view>
</template>
<script setup lang="ts">
/**
 * @file 维修经验库-列表
 * <AUTHOR>
 * @date 2025/4/19
 */
import { getRepairExperienceList } from '@/service/maintenanceApi'

// 搜索插槽
const search = {
  placeholder: '故障码/故障描述/奶茶车型号',
  // 自定义请求中search字段名
  searchKey: 'search',
}
const fetch = (params) => {
  return getRepairExperienceList({
    ...params,
    deviceType: 'deviceSellCar',
  })
}
const detailLabel = [
  { label: '奶茶车型号', field: 'modelNumberName' },
  { label: '故障描述', field: 'faultDescription' },
]
const toDetail = (item: any, refresh: any) => {
  uni.navigateTo({
    url: `/pages-maintenance/repair-experience-library/detail/index`,
    success: (opt) => {
      // 刷新
      opt.eventChannel.emit('params', { ...item })
      refresh.success(opt)
    },
  })
}
</script>
<style scoped lang="scss">
view {
  line-height: 100%;
}
:deep(.wd-card__title-content.wd-card__title-content) {
  @apply pt-0;
}
</style>
