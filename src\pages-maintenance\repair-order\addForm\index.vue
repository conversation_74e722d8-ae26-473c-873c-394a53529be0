<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '新增维修工单',
  },
}
</route>
<template>
  <view class="h-full flex-col flex">
    <uni-forms ref="formRef" :modelValue="formData" :rules="rules" border err-show-type="toast" label-position="top"
      label-width="100">
      <uni-forms-item required label="奶茶车名称" name="deviceId">
        <wd-select-picker custom-class="device-class" filterable filter-placeholder="搜索" type="radio"
          v-model="formData.deviceId" :columns="carList" title="选择奶茶车" />
      </uni-forms-item>
      <uni-forms-item label="故障码" name="faultCode">
        <wd-input no-border custom-class="py-0! pl-2!" type="text" v-model="formData.faultCode" placeholder="请输入" />
      </uni-forms-item>
      <uni-forms-item required label="故障描述" name="faultDescription">
        <wd-textarea custom-textarea-container-class="h-[200rpx]" custom-class="py-0! pl-2!" clear-trigger="focus"
          v-model="formData.faultDescription" :maxlength="200" clearable show-word-limit />
      </uni-forms-item>
      <uni-forms-item required label="故障时间" name="faultTime">
        <wd-calendar :max-date="Date.now()" custom-class="time-class" type="datetime" v-model="formData.faultTime" />
      </uni-forms-item>
      <uni-forms-item required label="报修人" name="reportPersonId">
        <wd-select-picker custom-class="device-class" @change="reportPersonChange" filterable filter-placeholder="搜索"
          type="radio" v-model="formData.reportPersonId" :columns="personList" title="选择报修人" />
      </uni-forms-item>
      <uni-forms-item required label="维修负责人" name="repairResponsiblePersonId">
        <wd-select-picker custom-class="device-class" @change="repairResponsiblePersonChange" filterable
          filter-placeholder="搜索" type="radio" v-model="formData.repairResponsiblePersonId" :columns="personList"
          title="维修负责人" />
      </uni-forms-item>
      <uni-forms-item required label="报修照片" name="photoList">
        <CameraSheet :min="1" v-model="formData.photoList">
          <wd-cell :title="formData.photoList?.length ? `已拍摄${formData.photoList?.length}张` : '点击拍摄'
            " is-link />
        </CameraSheet>
      </uni-forms-item>
    </uni-forms>
    <view class="bg-white p-2 mt-2">
      <LAButton size="xl" :round="false" block @click="handleSubmit">确定</LAButton>
    </view>
    <wd-gap safe-area-bottom height="0"></wd-gap>
  </view>
</template>

<script setup lang="ts">
import { getSellCarListAll, getUserInfoList, saveWorkOrder } from '@/service/maintenanceApi'
import CameraSheet from '@/components/camera-sheet.vue'
import dayjs from 'dayjs'
import { useUserStore } from '@/store'
import { useToast } from 'wot-design-uni'
const toast = useToast()

/**
 * @file 维修工单-详情-填写维修结果
 * <AUTHOR>
 * @date 2025/4/17
 */
const formRef = ref()
const formData = reactive({
  faultCode: '',
  faultDescription: '',
  deviceId: '',
  reportPersonId: '',
  reportPersonName: '',
  repairResponsiblePersonId: '',
  repairResponsiblePersonName: '',
  faultTime: '',
  photoList: [],
})
const rules = {
  deviceId: { rules: [{ required: true, errorMessage: '请选择奶茶车' }] },
  faultDescription: { rules: [{ required: true, errorMessage: '请输入故障描述' }] },
  faultTime: { rules: [{ required: true, errorMessage: '请选择故障时间' }] },
  reportPersonId: { rules: [{ required: true, errorMessage: '请选择报修人' }] },
  repairResponsiblePersonId: { rules: [{ required: true, errorMessage: '请选择维修负责人' }] },
  photoList: {
    rules: [
      { required: true, errorMessage: '请至少拍摄一张照片' },
      // {
      //   validateFunction: function (rule, value, data, cb) {
      //     console.log(value)
      //     if (value.length > 0) {
      //       cb()
      //       return
      //     }
      //     cb('请至少拍摄一张照片')
      //   },
      // },
    ],
  },
}

// 奶茶车列表
const carList = ref([])
const personList = ref([])
// 事件通道，在onLoad中进行获取
let eventChannel: {
  on: (eventName: string, cb: (...args: any) => void) => void
  emit: (eventName: string, ...args: any) => void
}
// 获取事件通道和维修工单ID
onLoad(async (option) => {
  const instance = getCurrentInstance().proxy
  // @ts-expect-error - eventChannel
  eventChannel = instance.getOpenerEventChannel()
  await getSellCarListAll().then((res: any) => {
    carList.value = res.data.map((item) => {
      return {
        label: item.carName,
        value: item.id,
      }
    })
  })
  // 请求维修负责人列表
  await getUserInfoList({ loginType: 'maintenance' }).then((res: any) => {
    personList.value = res.data.map((item) => ({
      label: item.employeeName,
      value: item.id,
    }))
  })
  const userInfo = useUserStore().userInfo
  formData.reportPersonId = userInfo.id
  formData.reportPersonName = userInfo.employeeName
})
const reportPersonChange = (data) => {
  const selData = personList.value.find((item) => {
    return item.value === data.value
  })
  formData.reportPersonName = selData.label
}
const repairResponsiblePersonChange = (data) => {
  const selData = personList.value.find((item) => {
    return item.value === data.value
  })
  console.log(personList.value, selData, data.value)
  formData.repairResponsiblePersonName = selData.label
}

/**
 * 点击提交之后执行的函数
 */
const handleSubmit = () => {
  formRef.value
    .validate()
    .then((data) => {
      if (formData.faultCode === null || formData.faultCode === '') {
        delete formData.faultCode
      }
      const params = {
        ...formData,
        faultTime: dayjs(formData.faultTime).format('YYYY-MM-DD HH:mm:ss'),
        filePaths: formData.photoList.join(','),
      }

      saveWorkOrder(params).then((res) => {
        if (res.success) {
          eventChannel.emit('refresh')
          toast.success('提交成功')
          setTimeout(uni.navigateBack, 500)
        }
      })
    })
    .catch((err) => {
      console.log('表单错误信息：', err)
    })
}
</script>
<style scoped lang="scss">
:deep(.uni-forms) {
  @apply bg-white px-4 pb-2 flex-1;
}

:deep(.camera-sheet-class) {
  padding-bottom: 0 !important;
}

:deep(.device-class .wd-select-picker__cell) {
  @apply py-0 pl-2;
}

:deep(.wd-textarea__inner) {
  height: 100%;
}

:deep(.wd-calendar .wd-calendar__cell) {
  @apply py-0 pl-2;
}

:deep(.wd-select-picker__footer.wd-select-picker__footer .wd-button) {
  background: var(--theme-color-ops, #356afd);
  border-radius: 11px;
}
</style>
