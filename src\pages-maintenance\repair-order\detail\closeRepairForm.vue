<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '关闭工单',
  },
}
</route>

<template>
  <view>
    <uni-forms class="bg-white px-4 pb-2" ref="formRef" :modelValue="formData" :rules="rules" border
      err-show-type="toast" label-position="top" label-width="100">
      <uni-forms-item required label="维修备注" name="repairContent">
        <wd-textarea v-model="formData.repairContent" :maxlength="200" clearable show-word-limit />
      </uni-forms-item>
      <view>
        <LAButton block size="xl" :round="false" @click="handleSubmit">确定</LAButton>
      </view>
    </uni-forms>
  </view>
</template>

<script setup lang="ts">
import { closeWorkOrder } from '@/service/maintenanceApi'
import { useToast } from 'wot-design-uni'
const toast = useToast()

/**
 * @file 维修工单-详情-关闭工单
 * <AUTHOR>
 * @date 2025/4/17
 */
const formRef = ref()
const formData = reactive({
  repairContent: '',
})
const rules = {
  repairContent: { rules: [{ required: true, errorMessage: '请输入维修备注' }] },
}

const workOrderId = ref()
// 事件通道，在onLoad中进行获取
let eventChannel: {
  on: (eventName: string, cb: (...args: any) => void) => void
  emit: (eventName: string, ...args: any) => void
}
// 获取事件通道和维修工单ID
onLoad((option) => {
  const instance = getCurrentInstance().proxy
  // @ts-expect-error - eventChannel
  eventChannel = instance.getOpenerEventChannel()
  workOrderId.value = option.id
})

/**
 * 点击提交之后执行的函数
 */
const handleSubmit = () => {
  formRef.value
    .validate(['workOrderId'])
    .then((data) => {
      const params = {
        id: workOrderId.value,
        repairContent: formData?.repairContent,
      }
      closeWorkOrder(params).then((res) => {
        if (res) {
          eventChannel.emit('closeRepairSuccess')
          toast.success('提交成功')
          setTimeout(uni.navigateBack, 500)
        }
      })
    })
    .catch((err) => {
      console.log('表单错误信息：', err)
    })
}
</script>

<style scoped>
:deep(.uni-forms) {
  @apply bg-white px-4 pb-2;
}

:deep(.wd-cell.wd-cell) {
  @apply pl-2;
}
</style>
