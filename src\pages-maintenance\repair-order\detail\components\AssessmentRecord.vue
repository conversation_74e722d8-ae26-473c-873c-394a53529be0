<template>
  <view class="bg-white p-4 mt-2 flex-col flex gap-3">
    <LATitle title="评价" class="text-sm" />
    <view class="flex-col flex gap-3 text-sm pl-1">
      <view class="flex justify-between">
        <view class="flex">
          <view class="fw-bold mr-2">{{ evaluateRecordData.evaluatorName }}</view>
          <view class="text-[#656666]">
            {{ evaluateRecordData.type === 'repair' ? '评价了维修工单' : '评价了保养工单' }}
          </view>
        </view>
        <view class="text-[#656666]">
          {{ dayjs(evaluateRecordData.createDate).format('YYYY-MM-DD HH:mm:ss') }}
        </view>
      </view>
      <view>
        <text class="fw-bold mr-2">评价分:</text>
        <text class="text-[#656666]">
          {{ evaluateRecordData.score }}
        </text>
      </view>
      <view>
        <text class="font-bold mr-2">评价内容:</text>
        <text class="text-[#656666]">
          {{ evaluateRecordData.evaluatorContent }}
        </text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
/**
 * @file 维修工单-详情-评价记录
 * <AUTHOR>
 * @date 2025/4/17
 */

import LATitle from '@/components/LATitle.vue'
import dayjs from 'dayjs'

const props = defineProps({
  evaluateRecordData: {
    type: Object,
    default: () => ({}),
  },
})
</script>

<style scoped>
view {
  line-height: inherit;
}
</style>
