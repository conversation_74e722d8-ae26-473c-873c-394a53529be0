<template>
  <tip-container-page title="维修经验详情">
    <view class="text-sm text-[#356AFD]">维修经验</view>
    <template #content>
      <view class="px-4 w-full bg-white">
        <view class="text-lg fw-900 py-4 w-full border-b border-b-solid border-b-color-[#EFF1F5]">
          {{ repairExperienceData?.faultCode }}
        </view>
        <view class="flex flex-col gap-4 pt-2">
          <view v-for="data in detailLabel" class="flex text-base" :key="data.field">
            <view class="font-900 mr-2 whitespace-nowrap">{{ data.label }}:</view>
            <view class="text-[#656666]" v-if="data.field !== 'repairExperience'">
              {{ repairExperienceData?.[data.field] }}
            </view>
            <view
              class="text-[#656666]"
              v-else
              v-html="handleImg(repairExperienceData?.[data.field])"
            ></view>
            <view class="text-[#656666]" v-if="!repairExperienceData?.[data.field]">-</view>
          </view>
        </view>
      </view>
      <view class="p-4 w-full bg-white mt-2" v-if="repairExperienceRecordData.length > 0">
        <LATitle title="用户维修经验记录" />
        <view
          class="flex text-base mt-2"
          v-for="recordData in repairExperienceRecordData"
          :key="recordData.id"
        >
          <view class="font-900 w-[200rpx]">{{ recordData.personName }}</view>
          <view class="flex-1 px-2 pb-2 border-b border-b-solid border-b-color-[#EFF1F5]">
            <view>{{ dayjs(recordData.createDate).format('YYYY-MM-DD HH:mm:ss') }}</view>
            <view class="whitespace-pre-line">{{ recordData.record }}</view>
          </view>
        </view>
      </view>
    </template>
  </tip-container-page>
</template>

<script setup lang="ts">
/**
 * @file 维修工单-故障码详情
 * <AUTHOR>
 * @date 2025/4/17
 */
import dayjs from 'dayjs'

import LATitle from '@/components/LATitle.vue'
import TipContainerPage from '@/components/tip-container-page.vue'

const props = defineProps({
  // 维修工单数据
  repairExperienceData: {
    type: Object,
    default: () => ({}),
  },
  // 用户维修经验记录
  repairExperienceRecordData: {
    type: Array as PropType<any[]>,
    default: () => [],
  },
})

// 详情项配置
const detailLabel = reactive([
  { label: '奶茶车型号', field: 'modelNumberName' },
  {
    label: '故障描述',
    field: 'faultDescription',
  },
  { label: '维修经验', field: 'repairExperience' },
])
const handleImg = (html) => {
  return html?.replace(/<img([^>]+)>/g, (match, attrs) => {
    // 添加自定义类名或样式
    return `<img class="custom-img" width="100%" style="max-width:100%;height:auto;" ${attrs}>`
  })
}
</script>

<style scoped lang="scss">
view {
  box-sizing: border-box !important;
}

.title {
  line-height: 100%;
  @apply font-bold gap-1 items-center flex text-[#1a1a1a] text-base;
  &.decoration::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 16px;
    background: rgba(53, 106, 253, 1);
    border-radius: 2px;
  }
}
</style>
