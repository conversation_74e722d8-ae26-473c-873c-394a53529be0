<template>
  <view class="bg-white p-4 pb-2 mt-2 flex-col flex gap-3">
    <LATitle title="维修记录" class="text-sm" />
    <view
      v-for="(data, i) in repairRecord"
      :key="data.id"
      class="flex-col flex gap-3 text-sm pl-1"
      :class="i < repairRecord.length - 1 ? 'border-b border-b-solid border-b-color-[#EFF1F5]' : ''"
    >
      <view class="flex justify-between pb-2">
        <view class="flex">
          <view class="fw-bold mr-2">{{ data.repairResponsiblePersonName }}</view>
          <view class="text-[#656666]">{{ repairResults[data.repairResults] }}</view>
        </view>
        <view class="text-[#656666]">
          {{ dayjs(data.createDate).format('YYYY-MM-DD HH:mm:ss') }}
        </view>
      </view>
      <view v-if="['repaired', 'unRepaired'].includes(data.repairResults)">
        <text class="fw-bold mr-2">{{ repairContent[data.repairResults] }}:</text>
        <text class="text-[#656666]">
          {{ repairedResultInfo.get(data.repairResults as RepairResultStatusEnum) }}
        </text>
      </view>
      <view class="mb-3" v-if="['repaired', 'unRepaired', 'closed'].includes(data.repairResults)">
        <text class="font-bold mr-2">维修备注:</text>
        <text class="text-[#656666]">
          {{ data.repairContent }}
        </text>
      </view>
    </view>
    <view
      v-if="repairRecord.some((item) => item?.deviceRepairMaintainFileList?.length > 0)"
      class="flex text-sm"
    >
      <view class="font-bold mr-2">照片拍摄:</view>
      <view class="flex gap-2 flex-1 flex-wrap">
        <GoodsImage
          v-for="imgDataItem in imgData.deviceRepairMaintainFileList"
          :src="imgDataItem.filePath"
          :key="imgDataItem.id"
          enable-preview
        />
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
/**
 * @file 维修工单-详情-维修记录
 * <AUTHOR>
 * @date 2025/4/17
 */
import dayjs from 'dayjs'

import LATitle from '@/components/LATitle.vue'
import { repairedResultInfo, RepairResultStatusEnum } from '@/pages-maintenance/repair-order/types'
import GoodsImage from '@/components/GoodsImage.vue'
interface RepairRecord {
  id: number
  repairResponsiblePersonName: string
  repairResults: string
  createDate: string
  repairContent: string
  deviceRepairMaintainFileList?: any[]
}
const props = defineProps({
  // 接收数据
  repairRecord: {
    type: Array as PropType<RepairRecord[]>,
    default: () => [{}],
  },
})
const imgData = computed(() => {
  return props.repairRecord.find((data) => data?.deviceRepairMaintainFileList?.length > 0)
})
// 维修结果/维修备注的字段
const repairContent = {
  repaired: '维修结果',
  unRepaired: '维修结果',
  closed: '维修备注',
}
// 工单操作描述（repairResults的三种操作描述："transfer"：'转让了工单'，"unRepaired"\"repaired":'填写了维修结果'，"closed":'关闭了工单'）
const repairResults = {
  transfer: '转让了工单',
  unRepaired: '填写了维修结果',
  repaired: '填写了维修结果',
  closed: '关闭了工单',
}
</script>

<style scoped>
view {
  line-height: inherit;
}
</style>
