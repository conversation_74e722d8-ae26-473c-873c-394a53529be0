<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '维修工单详情',
  },
}
</route>
<template>
  <view>
    <view class="bg-white pb-2">
      <view class="p-4 flex justify-between bg-white">
        <view class="text-base fw-bold">{{ workOrderDetail?.repairWorkOrderNumber }}</view>
        <view :style="{ color: repairOrderStatusEnum[workOrderDetail?.workOrderStatus]?.color }">
          {{ repairOrderStatusEnum[workOrderDetail?.workOrderStatus]?.text }}
        </view>
      </view>
      <view class="mx-2 bg-[#F8F9FC] p-4 rounded-2 flex flex-col gap-2 text-sm">
        <view v-for="item in detailLabel" :key="item.field" class="flex justify-between">
          <view class="text-sm text-gray">{{ item.label }}</view>

          <view
            v-if="!['faultCode', 'deviceRepairMaintainFileList'].includes(item.field)"
            class="max-w-[350rpx]"
          >
            {{ workOrderDetail[item.field] }}
          </view>

          <view
            v-else-if="item.field === 'faultCode'"
            class="max-w-[350rpx] flex items-center gap-1"
          >
            <text class="text-[28rpx]">
              {{ workOrderDetail[item.field] }}
            </text>
            <ExperiencePopup
              :repair-experience-data="repairExperienceData"
              :repair-experience-record-data="repairExperienceRecordData"
            />
          </view>
          <view
            v-else-if="item.field === 'deviceRepairMaintainFileList'"
            class="max-w-[250px] flex flex-wrap gap-1"
          >
            {{ workOrderDetail[item.field]?.length === 0 ? '-' : '' }}
            <GoodsImage
              enable-preview
              v-for="(img, num) in workOrderDetail[item.field]"
              :src="img.filePath"
              :key="num"
            />
          </view>
        </view>
      </view>
      <view
        class="px-2 pt-2 flex gap-1 justify-center bg-white h-[90rpx]"
        v-if="!['evaluation', 'closed'].includes(workOrderDetail?.workOrderStatus)"
      >
        <view
          @click="repairResults"
          class="text-sm rounded-3! flex-1 bg-[#356AFD] text-white text-center flex-col flex justify-center h-full"
        >
          填写维修结果
        </view>
        <wd-select-picker
          @confirm="transferRepairSubmit"
          @change="transferRepairChange"
          custom-class="flex-1"
          required
          filterable
          filter-placeholder="搜索"
          type="radio"
          v-model="repairResponsiblePersonId"
          :columns="personList"
          use-default-slot
          v-if="isRepairResponsiblePerson"
          title="选择维修负责人"
        >
          <view
            class="text-sm rounded-3! flex-1 bg-[#F8F9FC] text-center flex-col flex justify-center h-full"
          >
            转让工单
          </view>
        </wd-select-picker>
        <view
          @click="closeRepair"
          class="text-sm rounded-3! flex-1 text-[#F25555]! bg-[#F8F9FC]! text-center flex-col flex justify-center h-full"
        >
          关闭工单
        </view>
      </view>
    </view>
    <RepairRecord v-if="repairRecordData?.length > 0" :repair-record="repairRecordData" />
    <AssessmentRecord v-if="evaluateRecordData?.id" :evaluate-record-data="evaluateRecordData" />
  </view>
</template>

<script setup lang="ts">
/**
 * @file 维修工单-详情
 * <AUTHOR>
 * @date 2025/4/16
 */
// 请求列表
import {
  getRepairWorkOrderDetail,
  getRepairRecordList,
  getEvaluateInfo,
  getRepairExperienceList,
  getRepairExperienceRecordList,
  getUserInfoList,
  transferWorkOrder,
} from '@/service/maintenanceApi'
import GoodsImage from '@/components/GoodsImage.vue'
import { repairOrderStatusEnum } from '@/pages-maintenance/repair-order/types'
import { useUserStore } from '@/store'

import RepairRecord from '@/pages-maintenance/repair-order/detail/components/RepairRecord.vue'
import AssessmentRecord from '@/pages-maintenance/repair-order/detail/components/AssessmentRecord.vue'
import ExperiencePopup from '@/pages-maintenance/repair-order/detail/components/ExperiencePopup.vue'
import { useToast } from 'wot-design-uni'
const toast = useToast()

interface DetailItem {
  label: string
  field: string
  render?: (value: any) => VNode | string
}

// 工单id
const workOrderId = ref()
// 工单详情数据
const workOrderDetail = ref<Record<string, any>>({})
const repairResponsiblePersonId = ref()
// 维修记录数据
const repairRecordData = ref()
// 故障码-维修经验数据
const repairExperienceData = ref()
// 故障码-用户维修经验记录
const repairExperienceRecordData = ref()
// 维修负责人列表
const personList = ref([])
// 工单评价记录数据
const evaluateRecordData = ref()
// 判断当前登录用户是否是维修负责人
const isRepairResponsiblePerson = computed(() => {
  return workOrderDetail.value.repairResponsiblePersonId === useUserStore().userInfo.id
})
// 刷新
const refresh = async () => {
  // 获取工单详情
  await getRepairWorkOrderDetail({ id: workOrderId.value }).then((res) => {
    workOrderDetail.value = res.data
  })

  // 获取维修记录
  await getRepairRecordList({ workOrderId: workOrderId.value }).then((res) => {
    repairRecordData.value = res.data
  })
  // 获取评价信息
  await getEvaluateInfo({
    transactionId: workOrderId.value,
    type: 'repair',
  }).then((res) => {
    evaluateRecordData.value = res?.data || {}
    console.log('获取评价信息', res)
  })
}
// 事件通道，在onLoad中进行获取
let eventChannel: {
  on: (eventName: string, cb: (...args: any) => void) => void
  emit: (eventName: string, ...args: any) => void
}
onUnload(() => {
  eventChannel.emit('refresh')
})
onLoad(async (options) => {
  const instance = getCurrentInstance().proxy
  // @ts-expect-error - eventChannel
  eventChannel = instance.getOpenerEventChannel()
  workOrderId.value = JSON.parse(options.id)
  await refresh()
  // 请求维修负责人列表
  await getUserInfoList({
    loginType: 'maintenance',
  }).then((res: any) => {
    // 去除自己当前维修责任人
    personList.value = res.data
      .map((item) => ({
        label: item.employeeName,
        value: item.id,
      }))
      .filter((item) => item.value !== workOrderDetail.value.repairResponsiblePersonId)
  })
  await getRepairExperienceList({
    deviceType: 'deviceSellCar',
    faultCodeOrDesc: workOrderDetail.value.faultCode,
    modelNumber: workOrderDetail.value.modelNumber,
    current: 1,
    size: 9999,
  }).then((res) => {
    repairExperienceData.value = (res.data as { records: any[] }).records?.[0] || {}
    repairExperienceData.value.faultCode = workOrderDetail.value.faultCode
  })
  if (repairExperienceData.value && repairExperienceData.value?.id) {
    await getRepairExperienceRecordList({
      experienceId: repairExperienceData.value?.id,
    }).then((res) => {
      repairExperienceRecordData.value = res.data
    })
  }
})
// 优化后的详情项配置
const detailLabel = reactive<DetailItem[]>([
  { label: '奶茶车名称', field: 'deviceName' },
  {
    label: '故障码',
    field: 'faultCode',
  },
  { label: '故障时间', field: 'faultTime' },
  { label: '报修人', field: 'reportPersonName' },
  { label: '维修责任人', field: 'repairResponsiblePersonName' },
  {
    label: '故障描述',
    field: 'faultDescription',
  },
  {
    label: '报修图片',
    field: 'deviceRepairMaintainFileList',
  },
])
// 填写维修结果
const repairResults = () => {
  uni.navigateTo({
    url: `/pages-maintenance/repair-order/detail/writeRepairResult?id=${workOrderId.value}`,
    // 刷新列表
    success: (opt) => {
      opt.eventChannel.on('editRepairResultSuccess', () => {
        refresh()
      })
    },
  })
}
const transferRepairForm = ref()
const transferRepairChange = (e) => {
  const val = e.value
  const data = personList.value.find((item) => item.value === val)
  transferRepairForm.value = {
    id: workOrderId.value,
    newResponsiblePersonId: data.value,
    newResponsiblePersonName: data.label,
  }
}
// 转让工单
const transferRepairSubmit = () => {
  transferWorkOrder(transferRepairForm.value)
    .then((res) => {
      if (res.success) {
        toast.success('转让成功')
        // refresh()
        eventChannel.emit('refresh')
        // 返回上一页
        setTimeout(uni.navigateBack, 500)
      } else {
        toast.error(res.message)
      }
    })
    .catch((err) => {
      console.log(err)
    })
}
// 关闭工单
const closeRepair = () => {
  uni.navigateTo({
    url: `/pages-maintenance/repair-order/detail/closeRepairForm?id=${workOrderId.value}`,
    // 刷新列表
    success: (opt) => {
      opt.eventChannel.on('closeRepairSuccess', () => {
        refresh()
      })
    },
  })
}
</script>

<style scoped>
:deep(.wd-select-picker__field.wd-select-picker__field) {
  height: 100%;
}
:deep(.wd-select-picker__footer.wd-select-picker__footer .wd-button) {
  background: var(--theme-color-ops, #356afd);
  border-radius: 11px;
}
</style>
