<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '填写维修结果',
  },
}
</route>

<template>
  <view>
    <uni-forms ref="formRef" :modelValue="formData" :rules="rules" border err-show-type="toast" label-position="top"
      label-width="100">
      <uni-forms-item required label="维修结果" name="repairResults">
        <uni-data-checkbox class="pl-2" v-model="formData.repairResults" :localdata="[
          { text: '已修复', value: 'repaired' },
          { text: '未修复', value: 'unRepaired' },
        ]" @change="onRepairResultsChange"></uni-data-checkbox>
      </uni-forms-item>
      <uni-forms-item required label="维修备注" name="repairContent">
        <uni-easyinput class="pl-2" v-model="formData.repairContent" :inputBorder="false" :maxlength="200"
          placeholder="请输入" placeholderStyle="font-size:16px" type="textarea" />
      </uni-forms-item>
      <uni-forms-item required label="照片拍摄" name="photoList" v-if="formData.repairResults === 'repaired'">
        <CameraSheet :min="1" v-model="formData.photoList">
          <wd-cell :title="formData.photoList?.length ? `已拍摄${formData.photoList?.length}张` : '点击拍摄'
            " is-link />
        </CameraSheet>
      </uni-forms-item>
      <view>
        <LAButton size="xl" :round="false" block @click="handleSubmit">确定</LAButton>
      </view>
    </uni-forms>
  </view>
</template>

<script setup lang="ts">
import { modifyWorkOrder } from '@/service/maintenanceApi'
import CameraSheet from '@/components/camera-sheet.vue'
import { useToast } from 'wot-design-uni'
const toast = useToast()

/**
 * @file 维修工单-详情-填写维修结果
 * <AUTHOR>
 * @date 2025/4/17
 */
const formRef = ref()
const formData = reactive({
  repairResults: 'repaired',
  repairContent: '',
  photoList: [],
})
const rules = {
  repairResults: { rules: [{ required: true, errorMessage: '请选择维修结果' }] },
  repairContent: { rules: [{ required: true, errorMessage: '请输入维修备注' }] },
  photoList: {
    rules: [
      { required: true, errorMessage: '请至少拍摄一张照片' },
      // {
      //   validateFunction: function (rule, value, data, cb) {
      //     console.log(value)
      //     if (value.length > 0) {
      //       cb()
      //       return
      //     }
      //     cb('请至少拍摄一张照片')
      //   },
      // },
    ],
  },
}
// 维修结果变化
const onRepairResultsChange = (e) => {
  formData.repairContent = ''
  formData.photoList = []
}
const workOrderId = ref()
// 事件通道，在onLoad中进行获取
let eventChannel: {
  on: (eventName: string, cb: (...args: any) => void) => void
  emit: (eventName: string, ...args: any) => void
}
// 获取事件通道和维修工单ID
onLoad((option) => {
  const instance = getCurrentInstance().proxy
  // @ts-expect-error - eventChannel
  eventChannel = instance.getOpenerEventChannel()

  workOrderId.value = option.id
})

/**
 * 点击提交之后执行的函数
 */
const handleSubmit = () => {
  formRef.value
    .validate(['workOrderId'])
    .then((data) => {
      const params = {
        id: workOrderId.value,
        repairResults: formData.repairResults,
        repairContent: formData?.repairContent,
        // 只有 photoList 有元素时才添加字段
        ...(formData.photoList?.length > 0 && {
          filePaths: formData.photoList.join(','),
        }),
      }
      modifyWorkOrder(params).then((res) => {
        if (res.success) {
          eventChannel.emit('editRepairResultSuccess')
          toast.success('提交成功')
          setTimeout(uni.navigateBack, 500)
        }
      })
    })
    .catch((err) => {
      console.log('表单错误信息：', err)
    })
}
</script>

<style scoped>
:deep(.uni-forms) {
  @apply bg-white px-4 pb-2;
}

:deep(.wd-cell.wd-cell) {
  @apply pl-2;
}
</style>
