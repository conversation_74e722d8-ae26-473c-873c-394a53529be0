<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '维修工单',
  },
}
</route>
<template>
  <view>
    <PageList ref="pageRef" :requestApi="getRepairWorkOrderList" :search="search">
      <!-- 动态生成插槽 -->
      <!--      <template #reportPersonId="scoped">-->
      <!--        <view>-->
      <!--          <wd-slider step="1" v-model="sliderValue" ref="slider" />-->
      <!--          <LAButton block size="xl" suck @click="scoped.actions.updateValue(sliderValue)">-->
      <!--            提交按钮-->
      <!--          </LAButton>-->
      <!--        </view>-->
      <!--      </template>-->
      <template #default="{ item, refresh }">
        <wd-card custom-class="mt-2! mb-0! mx-2! rounded-xl! py-4!">
          <template #title>
            <view class="flex items-center justify-between" @click="toDetail(item, refresh)">
              <view class="font-bold">{{ item.repairWorkOrderNumber }}</view>
              <view class="flex items-center">
                <view
                  class="text-sm"
                  :style="{ color: repairOrderStatusEnum[item.workOrderStatus]?.color }"
                >
                  {{ repairOrderStatusEnum[item.workOrderStatus]?.text }}
                </view>
                <wd-icon
                  name="chevron-right"
                  size="16px"
                  :color="repairOrderStatusEnum[item.workOrderStatus]?.color"
                ></wd-icon>
              </view>
            </view>
          </template>
          <wd-divider color="#EBEDF1" custom-class="mt-0! p-0!" />
          <view class="flex flex-col gap-2" @click="toDetail(item, refresh)">
            <view class="flex justify-between" v-for="val in detailLabel" :key="val.field">
              <text class="text-[#979998]">{{ val.label }}</text>
              <text class="text-[#1A1A1A]">{{ item?.[val.field] }}</text>
            </view>
          </view>
        </wd-card>
      </template>
    </PageList>
  </view>
</template>
<script setup lang="ts">
/**
 * @file 维修工单-列表
 * <AUTHOR>
 * @date 2025/4/16
 */
import { getSellCarListAll, getRepairWorkOrderList } from '@/service/maintenanceApi'
import { repairOrderStatusEnum } from '@/pages-maintenance/repair-order/types'
const sliderValue = ref(66)
const pageRef = ref(null)
// 搜索插槽
const search = ref({
  placeholder: '维修工单号/维修/报修人',
  // 自定义请求中search字段名
  searchKey: 'search',
  // 搜索右侧表单
  searchForm: { showAdd: true, path: '/pages-maintenance/repair-order/addForm/index' },
  options: [
    // {
    //   prop: 'deviceId',
    //   requestApi: getSellCarListAll,
    //   replaceOptions: {
    //     label: 'carName',
    //     value: 'id',
    //   },
    //   slot: true,
    //   slotType: 'date',
    //   slotProps: {},
    //   title: '奶茶车名称',
    //   iconName: '',
    //   onChange(val) {
    //     console.log(val)
    //   },
    // },
    {
      prop: 'deviceId',
      title: '奶茶车名称',
      requestApi: getSellCarListAll,
      replaceOptions: {
        label: 'carName',
        value: 'id',
      },
    },
    {
      prop: 'orderStatus',
      title: '工单状态',
      options: [
        { label: '待维修', value: 'needRepair' },
        { label: '待评价', value: 'evaluation' },
        { label: '已关闭', value: 'closed' },
      ],
    },
    {
      prop: 'faultTime',
      slot: true, // 开启自定义插槽
      slotType: 'date', // 日期
      title: '故障时间',
    },
    // {
    //   prop: 'reportPersonId',
    //   title: computed(() => sliderValue.value),
    //   slot: true,
    //   initialValue: 66, // 默认值设定
    // },
  ],
})
const detailLabel = [
  { label: '奶茶车名称', field: 'deviceName' },
  { label: '报修人', field: 'reportPersonName' },
  { label: '维修责任人', field: 'repairResponsiblePersonName' },
  { label: '故障时间', field: 'faultTime' },
]
const toDetail = (item: any, refresh: any) => {
  uni.navigateTo({
    url: `/pages-maintenance/repair-order/detail/index?id=${JSON.stringify(item.id)}`,
    ...refresh,
  })
}
</script>
<style scoped lang="scss">
view {
  line-height: 100%;
}

:deep(.wd-card__title-content.wd-card__title-content) {
  @apply pt-0;
}
</style>
