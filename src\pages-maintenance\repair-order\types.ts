// 工单状态枚举
export enum OrderStatusEnum {
  /** 已关闭 */
  CLOSED = 'closed',
  /** 待维修 */
  NEED_REPAIR = 'needRepair',
  /** 待评价 */
  EVALUATION = 'evaluation',
}

// 工单状态信息
export const repairOrderStatusEnum = {
  [OrderStatusEnum.NEED_REPAIR]: {
    bg: 'rgba(0, 194, 144, 0.1)',
    color: 'rgba(0, 194, 144, 1)',
    text: '待维修',
  },
  [OrderStatusEnum.CLOSED]: {
    bg: 'rgba(101, 102, 102, 0.1)',
    color: 'rgba(101, 102, 102, 1)',
    text: '已关闭',
  },
  [OrderStatusEnum.EVALUATION]: {
    bg: 'rgba(119, 102, 249, 0.1)',
    color: 'rgba(119, 102, 249, 1)',
    text: '待评价',
  },
}
// 维修结果枚举
export enum RepairResultStatusEnum {
  // 已修复
  REPAIRED = 'repaired',
  // 未修复
  UN_REPAIRED = 'unRepaired',
}
// 维修结果状态信息
export const repairedResultInfo = new Map([
  [RepairResultStatusEnum.REPAIRED, '已修复'],
  [RepairResultStatusEnum.UN_REPAIRED, '未修复'],
])
