<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '关闭工单' },
}
</route>

<template>
  <view class="textarea pb-2 bg-white">
    <wd-textarea label="补料备注" required v-model="remark" placeholder="请输入" :maxlength="200" auto-focus show-word-limit />
    <view class="px-4">
      <LAButton block :round="false" size="xl" @click="handleSubmit">确定</LAButton>
    </view>
  </view>
</template>

<script setup lang="ts">
import { closeRestock } from '@/service/maintenanceApi'

const orderId = ref('')
onLoad((params) => {
  orderId.value = params.id
})
const remark = ref('')
const handleSubmit = async () => {
  const params = { id: orderId.value, remark: remark.value }
  try {
    const res = await closeRestock(params)
    if (res) {
      uni.redirectTo({ url: '/pages-maintenance/replenish-order/index' })
    }
  } catch (e) { }
}
</script>

<style lang="scss" scoped>
:deep(.wd-textarea) {
  display: block !important;
}
</style>
