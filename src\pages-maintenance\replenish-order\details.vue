<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '补料工单详情' },
}
</route>

<template>
  <view class="pb-2">
    <view class="py-2.5 px-2 bg-white">
      <view class="flex justify-between items-center p-3.5">
        <view class="font-bold text-black text-base">{{ data.code }}</view>
        <view class="text-sm" :style="{ color: statusMap.get(data.status)?.color }">
          {{ statusMap.get(data.status)?.name }}
        </view>
      </view>
      <view class="text-sm leading-7 p-4 mb-1.5 rounded-xl bg-[#F8F9FC]">
        <view class="flex justify-between">
          <view class="text-[#979998]">车辆名称</view>
          <view class="text-[#1A1A1A]">{{ data.carName }}</view>
        </view>
        <view class="flex justify-between">
          <view class="text-[#979998]">车牌号</view>
          <view class="text-[#1A1A1A]">{{ data.carPlateNo }}</view>
        </view>
        <view class="flex justify-between">
          <view class="text-[#979998]">车辆型号</view>
          <view class="text-[#1A1A1A]">{{ data.carModel }}</view>
        </view>
        <view class="flex justify-between">
          <view class="text-[#979998]">车辆编码</view>
          <view class="text-[#1A1A1A]">{{ data.carCode }}</view>
        </view>
        <view class="flex justify-between">
          <view class="text-[#979998]">投放区域</view>
          <view class="text-[#1A1A1A]">{{ data.runAreaName }}</view>
        </view>
        <view class="flex justify-between">
          <view class="text-[#979998]">工单生成时间</view>
          <view class="text-[#1A1A1A]">{{ data.createDate }}</view>
        </view>
        <view class="flex justify-between">
          <view class="text-[#979998]">补料负责人</view>
          <view class="text-[#1A1A1A]">{{ showValue(data.operatorName) }}</view>
        </view>
        <view class="flex justify-between">
          <view class="text-[#979998]">补料完成时间</view>
          <view class="text-[#1A1A1A]">{{ data.finshDate || '-' }}</view>
        </view>
      </view>
      <wd-select-picker v-if="data.status === 0" title="选中补料负责人" type="radio" filterable use-default-slot
        v-model="user1" :columns="userList" @confirm="handleDispatch('distribute')">
        <LAButton block :round="false" size="xl">派工</LAButton>
      </wd-select-picker>
      <view v-if="data.status === 1" class="flex justify-between items-center">
        <view class="flex-1">
          <LAButton custom-style="flex:1" :round="false" size="xl" type="primary" pale @click="handleComplete(data)">
            补料
          </LAButton>
        </view>
        <view class="flex-1 mx-1">
          <LAButton custom-style="flex:1" :round="false" size="xl" type="primary" pale @click="toClosed(data.id)">
            关闭工单
          </LAButton>
        </view>
        <view class="flex-1">
          <wd-select-picker title="选中补料负责人" type="radio" filterable use-default-slot v-model="user2"
            :columns="tranferList" @confirm="handleDispatch('tranfer')">
            <LAButton custom-style="flex:1" :round="false" size="xl" type="primary" pale>
              转让工单
            </LAButton>
          </wd-select-picker>
        </view>
      </view>
    </view>
    <template v-for="(item, i) in data.workDetailList" :key="item.id">
      <view v-if="item.restockWorkDetailList?.length > 0" class="bg-white mt-2 p-4 pb-0 text-sm text-[#656666]">
        <view>
          <view class="flex items-center">
            <view class="w-1 h-4 rounded-sm bg-[#356AFD] mr-1.5 mt-0.5"></view>
            <view class="font-bold text-base text-[#1a1a1a]">
              补料项目-{{ i === 0 ? '机器A' : '机器B' }}
            </view>
          </view>
          <view class="flex items-center font-bold text-[#1a1a1a] py-4.5">
            <view class="w-40">原料</view>
            <view class="w-20">剩余量</view>
            <view class="">剩余保质期</view>
          </view>
          <view class="flex items-center py-4.5 border-t border-t-solid border-slate-200"
            v-for="item2 in item.restockWorkDetailList" :key="item2.id">
            <view class="w-40">{{ item2.ingredientName }}</view>
            <view class="w-20 text-[#F25555]">
              {{ item2.currentNumber }}{{ item2.ingredientUnit }}
            </view>
            <view class="">{{ Math.floor(item2.currentShelfLife) }}小时</view>
          </view>
        </view>
      </view>
    </template>

    <view class="bg-white mt-2 p-4" v-if="data.workRecordList.length > 0">
      <view class="flex items-center">
        <view class="w-1 h-4 rounded-sm bg-[#356AFD] mr-1.5 mt-0.5"></view>
        <view class="font-bold text-base text-[#1a1a1a]">补料记录</view>
      </view>
      <view class="border-b border-b-solid border-slate-200 p-2" v-for="item in data.workRecordList" :key="item.id">
        <view class="flex items-center text-sm text-[#656666] mb-2">
          <view class="font-bold text-[#1a1a1a] mr-2.5">{{ item.createAccountName }}</view>
          <view>{{ handleMap.get(item.operation) }}了工单</view>
          <view class="ml-auto">{{ item.createDate }}</view>
        </view>
        <view class="flex items-center text-sm text-[#656666]" v-if="item.operation === 'closeOrder'">
          <view class="font-bold text-[#1a1a1a]">补料备注：</view>
          <view>{{ item.remark }}</view>
        </view>
        <view v-else-if="item.operation === 'transferOrder'" class="flex items-center text-sm text-[#656666]">
          <view class="font-bold text-[#1a1a1a]">补料负责人：</view>
          <view>{{ data?.operatorName }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { getRestockDetail, dispatchRestock, getUserInfoList } from '@/service/maintenanceApi'
import { showValue } from '../../utils'
import { useToast } from 'wot-design-uni'
import LAButton from '@/components/LAButton.vue'

const toast = useToast()

const orderId = ref('')
onLoad(async (params) => {
  orderId.value = params.id
  await getDetail(orderId.value)
  await getUserInfoList({ loginType: 'maintenance' }).then((res) => {
    userList.value = res.data.map((item) => ({ value: item.id, label: item.employeeName }))
    // 转让人列表去除自己operatorId
    tranferList.value = res.data
      .map((item) => ({ value: item.id, label: item.employeeName }))
      .filter((item) => item.value != data.value.operatorId)
  })
})
const data = ref({})
const getDetail = async (id: string) => {
  try {
    const res = await getRestockDetail({ id })
    if (res.success) {
      data.value = res.data
    }
  } catch (error) {
    console.log(error)
  }
}

const statusMap = new Map([
  [
    0,
    {
      name: '未派工',
      color: '#356AFD',
    },
  ],
  [
    1,
    {
      name: '待补料',
      color: '#00C290',
    },
  ],
  [
    2,
    {
      name: '已关闭',
      color: '#656666',
    },
  ],
])

const handleMap = new Map([
  ['assignWork', '派发'],
  ['transferOrder', '转让'],
  ['closeOrder', '关闭'],
])

const user1 = ref('')
// 转让工单
const user2 = ref('')
// 派工
const userList = ref([])
// 转让人列表
const tranferList = ref([])

// 派工/转让工单
const handleDispatch = async (op) => {
  try {
    const params = {
      userId: op === 'distribute' ? user1.value : user2.value,
      workOrderId: orderId.value,
    }
    const res = await dispatchRestock(params)
    if (res.success) {
      toast.success('操作成功')
      uni.redirectTo({ url: '/pages-maintenance/replenish-order/index' })
    } else {
      toast.error(res.message)
    }
  } catch (e) {
    console.log(e)
  }
}
const handleComplete = (item) => {
  uni.navigateTo({
    url: `/pages-maintenance/replenish/replenishEditForm/index?carData=${encodeURIComponent(JSON.stringify({ ...item, id: item.carId }))}`,
  })
}
const toClosed = (id) => {
  uni.navigateTo({ url: `/pages-maintenance/replenish-order/closed?id=${id}` })
}
</script>

<style lang="scss" scoped>
.wd-select-picker {
  :deep(.wd-action-sheet__header) {
    height: 50px;
    line-height: 50px;

    .wd-action-sheet__close {
      top: 18px;
    }
  }
}
</style>
