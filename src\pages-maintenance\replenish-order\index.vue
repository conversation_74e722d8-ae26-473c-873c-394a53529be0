<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '补料工单' },
}
</route>

<template>
  <view class="pb-2">
    <PageList :requestApi="fetchList" :search="search">
      <template #default="{ item }">
        <view class="bg-white rounded-xl py-3.5 px-4 mt-2 mx-2" @click="toDetails(item.id)">
          <view class="flex justify-between items-center pb-3.5 border-b border-b-solid border-slate-200">
            <view class="font-bold text-base text-black">{{ item.code }}</view>
            <view class="flex items-center" :style="{ color: statusMap.get(item.status).color }">
              <view class="text-sm">{{ statusMap.get(item.status).name }}</view>
              <wd-icon name="arrow-right" size="16px"></wd-icon>
            </view>
          </view>
          <view class="text-sm pt-3.5">
            <view class="flex justify-between items-center">
              <view class="text-gray-400">车辆名称</view>
              <view class="text-[#1a1a1a]">{{ item.carName }}</view>
            </view>
            <view class="flex justify-between items-center">
              <view class="text-gray-400">补料负责人</view>
              <view class="text-[#1a1a1a]">{{ item.operatorName || '-' }}</view>
            </view>
            <view class="flex justify-between items-center">
              <view class="text-gray-400">工单生成时间</view>
              <view class="text-[#1a1a1a]">{{ item.createDate }}</view>
            </view>
          </view>
        </view>
      </template>
    </PageList>
  </view>
</template>

<script setup lang="ts">
import { getRestockList, getSellCarListAll } from '@/service/maintenanceApi'

const search = {
  placeholder: '补料工单号/补料人',
  searchKey: 'search',
  options: [
    {
      prop: 'carId',
      title: '车辆名称',
      requestApi: getSellCarListAll,
      replaceOptions: {
        label: 'carName',
        value: 'id',
      },
    },
    {
      prop: 'status',
      title: '工单状态',
      options: [
        { label: '未派工', value: 0 },
        { label: '待补料', value: 1 },
        { label: '已关闭', value: 2 },
      ],
    },
    {
      prop: 'createDate',
      slot: true, // 开启自定义插槽
      slotType: 'date', // 日期
      title: '生成时间',
    },
  ],
}

const toDetails = (id) => {
  uni.navigateTo({ url: `/pages-maintenance/replenish-order/details?id=${id}` })
}

const statusMap = new Map([
  [
    0,
    {
      name: '未派工',
      color: '#356AFD',
    },
  ],
  [
    1,
    {
      name: '待补料',
      color: '#00C290',
    },
  ],
  [
    2,
    {
      name: '已关闭',
      color: '#656666',
    },
  ],
])
const fetchList = async (params) => {
  return getRestockList(params)
}
</script>

<style scoped></style>
