<template>
  <view class="flex flex-col justify-between h-full">
    <wd-form
      ref="form"
      :model="model"
      :rules="rules"
      error-type="toast"
      custom-class="bg-[#eff1f5] flex-col flex gap-2"
    >
      <view class="py-2 bg-white surplusDisposal-clean" v-if="props.operateType === 'edit'">
        <view class="surplusDisposal-class">
          <!--编辑的动态处理-->
          <wd-form-item required label="剩余量处理">
            <wd-radio-group
              @change="surplusDisposalChange"
              shape="button"
              inline
              v-model="model.selectSurplusDisposal"
            >
              <wd-radio :value="'clean'">已清理</wd-radio>
              <wd-radio :value="'Unclean'">未清理</wd-radio>
            </wd-radio-group>
          </wd-form-item>
        </view>
        <view>
          <wd-form-item
            v-if="model.selectSurplusDisposal === 'clean'"
            :value="model.surplusDisposalData"
          >
            <wd-radio-group
              custom-class="surplusDisposal-class-item"
              inline
              shape="dot"
              v-model="model.surplusDisposalData"
            >
              <wd-radio :value="1">剩余清理</wd-radio>
              <wd-radio :value="2">过期清理</wd-radio>
            </wd-radio-group>
          </wd-form-item>
        </view>
      </view>
      <wd-select-picker
        @confirm="ingredientChange"
        v-if="props.operateType === 'add'"
        label="原料名称"
        required
        filterable
        filter-placeholder="原料名称"
        type="radio"
        :show-confirm="false"
        label-width="100px"
        prop="ingredientId"
        v-model="model.ingredientId"
        :columns="ingredientNameList"
        title="请选择原料名称"
      />
      <wd-input
        required
        label="补料后余量"
        label-width="100px"
        type="number"
        prop="number"
        clearable
        v-model="model.number"
        custom-class="number-class"
        placeholder="请输入"
      >
        <template #suffix>{{ data?.capacityUnit }}</template>
      </wd-input>
      <wd-calendar
        label="生产日期"
        label-width="100px"
        placeholder="请选择"
        prop="productionDate"
        :max-date="Date.now()"
        clearable
        v-model="model.productionDate"
        custom-class="ops-calendar"
      />
    </wd-form>
    <view class="px-4 py-2 bg-white">
      <LAButton :round="false" type="primary" block size="lg" @click="handleSubmit">保存</LAButton>
    </view>
  </view>
</template>

<script setup lang="ts">
/**
 * @file 补料表单-单项原料配置-下方动态表单
 * <AUTHOR>
 * @date 2025/4/10
 */
import { getIngredientList } from '@/service/maintenanceApi'
import dayjs from 'dayjs'
import { useToast } from 'wot-design-uni'
const toast = useToast()

// 编辑
const props = defineProps<{
  data?: any
  operateType: 'add' | 'edit'
}>()
const emit = defineEmits(['submit'])
const form = ref()
const model = ref({
  ingredientCategoriesId: '',
  ingredientId: '',
  number: '',
  productionDate: '',
  selectSurplusDisposal: null,
  surplusDisposalData: null,
  surplusDisposal: null,
  ingredientImg: '',
  ingredientName: '',
  shelfLife: '',
})
const ingredientNameList = ref([])
const rules = {
  ingredientId: [{ required: true, message: '请输入原料名称' }],
  number: [
    {
      required: true,
      message: '请输入补料后余量',
      validator: (value) => {
        if (value == null) {
          // eslint-disable-next-line prefer-promise-reject-errors
          return Promise.reject('请输入补料后余量')
        }
        // 必须大于0
        if (value <= 0) {
          // eslint-disable-next-line prefer-promise-reject-errors
          return Promise.reject('补料后余量必须大于0')
        }
        if (value > props.data.capacity) {
          // eslint-disable-next-line prefer-promise-reject-errors
          return Promise.reject('补料后余量不能大于容量')
        }
        // 编辑时格外判断
        // 未清理不能小于余量不能大于容量
        if (
          props.operateType === 'edit' &&
          value < props.data.number &&
          model.value.selectSurplusDisposal === 'Unclean'
        ) {
          // eslint-disable-next-line prefer-promise-reject-errors
          return Promise.reject('补料后余量不能小于当前剩余量')
        }
        return Promise.resolve()
      },
    },
  ],
  productionDate: [{ required: true, message: '请选择生产日期' }],
}
const ingredientChange = ({ value, selectedItems }) => {
  model.value.ingredientImg = selectedItems.ingredientImg
  model.value.ingredientName = selectedItems.label
  model.value.shelfLife = selectedItems.shelfLife
}
onMounted(() => {
  // 复制一份
  model.value = JSON.parse(JSON.stringify(props.data))
  // 请求获取原料名称
  getIngredientList({ ingredientTypeId: model.value.ingredientCategoriesId }).then((res: any) => {
    ingredientNameList.value = (res.data || []).map((item) => ({
      label: item.ingredientName,
      value: item.id,
      ingredientImg: item.imgPath,
      shelfLife: item.expirationDate,
    }))
  })
})

const surplusDisposalChange = (val) => {
  if (val.value === 'clean') {
    model.value.surplusDisposalData = ''
  } else {
    // 删除
    delete model.value.surplusDisposalData
  }
}

// 保存
const handleSubmit = () => {
  // 只有编辑才会必选择剩余量： 处理剩余量处理表单项
  if (model.value.selectSurplusDisposal && props.operateType === 'edit') {
    // 未清理直接赋值0
    if (model.value.selectSurplusDisposal === 'Unclean') {
      model.value.surplusDisposal = 0
    }
    // 已清理则根据动态处理(0:未清理,1:剩余清理,2:过期清理)
    if (model.value.selectSurplusDisposal === 'clean') {
      if (model.value.surplusDisposalData) {
        model.value.surplusDisposal = model.value.surplusDisposalData
      } else {
        toast.warning('请选择剩余量处理')
        return
      }
    }
  }
  if (props.operateType === 'edit' && !model.value.selectSurplusDisposal) {
    toast.warning('请选择剩余量处理')
    return
  }
  form.value.validate().then(async ({ valid }) => {
    if (valid) {
      // 把时间戳格式化
      model.value.productionDate = dayjs(model.value.productionDate).format('YYYY-MM-DD')

      emit('submit', model.value)
    }
  })
}
</script>
<!--wot组件库的样式穿透-->
<script lang="ts">
export default {
  options: {
    styleIsolation: 'shared',
  },
}
</script>
<style lang="scss" scoped>
:deep(.surplusDisposal-class-item) {
  margin-top: 12px !important;
}
:deep(.wd-cell__wrapper) {
  align-items: center !important;
}
:deep(.is-required) {
  font-weight: 900;
}
:deep(.wd-radio.is-button .wd-radio__label) {
  border-radius: 6px;
  background: #fff;
  border: 1px solid #dde2e8;
}
:deep(.surplusDisposal-class .wd-radio__shape) {
  display: none !important;
}

:deep(.surplusDisposal-clean .wd-radio-group) {
  display: flex;
}

:deep(.number-class .wd-input__suffix) {
  display: flex;
}
</style>
<style lang="scss">
// .ops-calendar {
//   :deep(.wd-calendar__confirm.wd-calendar__confirm .wd-button) {
//     height: 40px;
//     background: var(--theme-color-ops, #356afd);
//     border-radius: 10px;
//   }
// }
</style>
