<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '补料' },
}
</route>
<template>
  <view class="flex flex-col h-full">
    <view class="flex-1 flex flex-col h-0 overflow-auto">
      <!--顶部区域距离-->
      <view class="p-2 bg-white">
        <view class="flex justify-between">
          <view class="flex items-center">
            <wd-icon name="location" size="16px"></wd-icon>
            <text class="text-sm">{{ carData?.runAreaName }}</text>
          </view>
          <view class="flex gap-1 items-center">
            <text class="text-sm">{{ carData?.carName }}</text>
            <text class="text-sm" :style="{ color: carData?.status == 1006 ? '#656666' : 'rgba(0, 194, 144, 1)' }">
              {{ carData?.status == 1006 ? '离线' : '在线' }}
            </text>
            <wd-divider custom-class="!h-[0.75rem]" vertical />

            <text class="text-sm">距你{{ formatDecimal(carData?.distance) }}</text>
          </view>
        </view>
        <wd-segmented v-if="currentMachineRawMaterial.length > 0" :options="machineList" v-model:value="currentMachine"
          custom-class="w-max! mt-4"></wd-segmented>
      </view>
      <view class="flex items-center bg-white my-2 pl-4" v-if="currentMachineRawMaterial.length > 0">
        <view class="font-bold text-sm text-black label before:content['*']">照片拍摄</view>
        <view class="flex-1">
          <CameraSheet :allowAlbum="false" v-model="photoList" :min="1" style="padding-bottom: 0">
            <wd-cell is-link :title="photoList?.length ? `已拍摄${photoList?.length}张` : '点击拍摄'" />
          </CameraSheet>
        </view>
      </view>

      <!--补料区域-->
      <view class="mb-2" v-for="(dataItem, groupIndex) in groupedMaterials" :key="groupIndex">
        <view v-for="item in dataItem" :key="item.recordId"
          class="border-b border-b-solid border-b-color-[#EFF1F5] p-4 bg-white">
          <view class="flex justify-between items-center mb-2">
            <view class="fw-bold text-base">{{ item?.componentName }}</view>
            <template v-if="item.ingredientId">
              <view class="flex justify-end">
                <view class="flex">
                  <view class="rounded-md py-[3px] px-2.5 mr-4 text-sm text-[#F25555] bg-[#feeded]"
                    @click="onClearMaterial(item)">
                    清除
                  </view>
                  <view class="rounded-md py-[3px] px-2.5 text-sm text-[#356AFD] bg-[#eaf0ff]"
                    @click="editRawMaterial(item, operateTypeFn(item))">
                    编辑
                  </view>
                </view>
              </view>
            </template>
          </view>

          <view v-if="!item.ingredientId"
            class="bg-[#f4f7ff] rounded-3 w-12 h-12 flex-col flex justify-center text-center"
            @click="editRawMaterial(item, operateTypeFn(item))">
            <wd-icon name="add" size="22px" color="rgba(53, 106, 253, 1)"></wd-icon>
          </view>

          <view v-else class="flex">
            <view class="size-20 rounded-xl mr-3 bg-[#F1F2F2] overflow-hidden">
              <image mode="scaleToFill" :src="item.ingredientImg" class="w-full h-full rounded-xl"></image>
            </view>
            <view>
              <view class="text-[#1A1A1A] text-sm font-blod">{{ item?.ingredientName }}</view>
              <view class="text-[#656666] text-sm mt-1.5">
                剩余量：
                <text :class="[
                  item?.number <= (item?.capacity * item?.waringPercent) / 100
                    ? 'text-[#f25c5c]'
                    : 'text-[#656666]',
                ]">
                  {{ item?.number + item?.capacityUnit }}
                </text>
              </view>
              <view class="text-[#656666] text-sm mt-1.5">
                剩余保质期：{{ item?.remainingShelfLife }}h
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="flex-1 flex flex-col justify-center items-center" v-if="currentMachineRawMaterial.length === 0">
        <image src="../../../static/images/noData.png" class="w-12 h-12" mode="scaleToFill" />
        <text>暂无机器</text>
      </view>
    </view>

    <!-- 底部固定按钮 -->
    <view class="bg-white p-2 mt-2" v-if="currentMachineRawMaterial.length > 0">
      <LAButton type="primary" size="xl" block :round="false" @click="submitEditForm">
        提交
      </LAButton>
    </view>

    <wd-gap safe-area-bottom height="0"></wd-gap>

    <wd-message-box selector="wd-message-box-clean" custom-class="clean-form">
      <view class="pt-2 text-lg fw-bold" v-if="!isEditData">是否确认清除操作？</view>
      <view class="flex bg-white p-2">
        <view class="size-20 rounded-xl mr-3 bg-[#F1F2F2] overflow-hidden">
          <image mode="scaleToFill" :src="cleanDetailData?.ingredientImg" class="w-full h-full rounded-xl"></image>
        </view>
        <view>
          <view class="text-[#1A1A1A] text-sm font-bold">
            {{ cleanDetailData?.ingredientName }}
          </view>
          <view class="text-[#656666] text-sm mt-1.5">
            剩余量：
            <text :class="[
              cleanDetailData?.number <=
                (cleanDetailData?.capacity * cleanDetailData?.waringPercent) / 100
                ? 'text-[#f25c5c]'
                : 'text-[#656666]',
            ]">
              {{ cleanDetailData?.number + cleanDetailData?.capacityUnit }}
            </text>
          </view>
          <view class="text-[#656666] text-sm mt-1.5">
            剩余保质期：{{ cleanDetailData?.remainingShelfLife }}h
          </view>
        </view>
      </view>
      <wd-form :model="cleanForm" v-if="isEditData">
        <wd-form-item label="清除原因" required>
          <wd-radio-group shape="button" inline v-model="cleanForm.reason">
            <wd-radio :value="0">换料</wd-radio>
          </wd-radio-group>
        </wd-form-item>
        <wd-form-item label="剩余量" required>
          <wd-radio-group shape="button" inline v-model="cleanForm.surplusHandle">
            <wd-radio :value="0">损耗</wd-radio>
            <wd-radio :value="1">回收</wd-radio>
          </wd-radio-group>
        </wd-form-item>
      </wd-form>
    </wd-message-box>
  </view>
</template>

<script setup lang="ts">
/**
 * @file 补料表单
 * <AUTHOR>
 * @date 2025/4/8
 */
import { MaterialDataItem, MachineMaterialData } from '../types'
import { useMessage, useToast } from 'wot-design-uni'
import { getComponentList, restockSubmit } from '@/service/maintenanceApi'
import CameraSheet from '@/components/camera-sheet.vue'
import { formatDecimal } from '@/utils'

// 当前车辆数据
const carData = ref()
const cleanMessage = useMessage('wd-message-box-clean')
const toast = useToast()
// 事件通道，在onLoad中进行获取
let eventChannel: {
  on: (eventName: string, cb: (...args: any) => void) => void
  emit: (eventName: string, ...args: any) => void
}
onUnload(() => {
  uni.$off('updateRawMaterial', updateRawMaterial)
})

// 更新原料的值
interface UpdateRawMaterial {
  currentMachine: string
  data: MaterialDataItem
}

const updateRawMaterial = (data: UpdateRawMaterial) => {
  const { currentMachine, data: materialData } = data
  // 对比原始值(originalMachineRawMaterial)里的这条数据，如果有变化，则修改machineRawMaterial的值
  const index = originalMachineRawMaterial.value.findIndex(
    (item) => item.machineName === currentMachine,
  )
  // 原始机器数据
  const machine = originalMachineRawMaterial.value[index]
  // 当前修改原料数据所在原始数据的索引
  const materialIndex = machine.detailVOS.findIndex(
    (item) => item.componentId === materialData.componentId,
  )
  // 对比是否相同
  if (JSON.stringify(machine.detailVOS[materialIndex]) !== JSON.stringify(materialData)) {
    machineRawMaterial.value[index].detailVOS[materialIndex] = materialData
  }
}

onShow(() => {
  uni.$on('updateRawMaterial', updateRawMaterial)
})
const photoList = ref<string[]>([])
onLoad(async (options) => {
  const instance = getCurrentInstance().proxy
  // @ts-expect-error - eventChannel
  eventChannel = instance.getOpenerEventChannel()
  carData.value = JSON.parse(decodeURIComponent(options.carData))
  console.log(11, JSON.parse(decodeURIComponent(options.carData)))
  await fetchMachineRawMaterial().then((res: any) => {
    for (const item of res.data) {
      if (item.sort === 1) {
        item.machineName = '机器A'
      } else if (item.sort === 2) {
        item.machineName = '机器B'
      }
    }
    machineList.value = res.data.map((item: any) => item.machineName)
    currentMachine.value = machineList.value[0]
    // 存放原始机器数据
    originalMachineRawMaterial.value = JSON.parse(JSON.stringify(res.data))
    // 存放当前修改中机器的数据
    machineRawMaterial.value = JSON.parse(JSON.stringify(res.data))
    // 给当前修改中机器添加初始照片数组字段
    machineRawMaterial.value.forEach((item) => {
      item.photoList = []
    })
    photoList.value = machineRawMaterial.value[0].photoList
  })
})
// 机器选择
const machineList = ref<string[]>([])
// 当前机器
const currentMachine = ref()
// 原始数据：机器的总原料数据
const originalMachineRawMaterial = ref<MachineMaterialData[]>([])
// 计算属性：按 componentType 分组数据
const groupedMaterials = computed(() => {
  const groups = {}
  currentMachineRawMaterial.value.forEach((item) => {
    if (!groups[item.componentType]) {
      groups[item.componentType] = []
    }
    groups[item.componentType].push(item)
  })
  console.log('数据', Object.values(groups))
  return Object.values(groups)
})
// 机器的总原料数据
const machineRawMaterial = ref<MachineMaterialData[]>([])
// 请求获取机器总的原料数据
const fetchMachineRawMaterial = async () => {
  return getComponentList({
    carId: carData.value.id,
  })
}
// 当前选中的机器原料数据
const currentMachineRawMaterial = ref<MaterialDataItem[]>([])
// 判断当前操作是否为新增/编辑
const operateTypeFn = (data: MaterialDataItem): string => {
  // 先找到对应机器索引,再通过元器件id找到列表中的原始数据原料数据（originalMachineRawMaterial）
  const index = originalMachineRawMaterial.value.findIndex(
    (item) => item.machineName === currentMachine.value,
  )
  // 找到机器
  const originalMachine = originalMachineRawMaterial.value[index]
  if (data.ingredientId) {
    // 跟原始数据对比，如果原料id不同就是新增
    if (
      originalMachine.detailVOS.find((item) => item.componentId === data.componentId)
        .ingredientId === data.ingredientId
    ) {
      return 'edit'
    } else {
      return 'add'
    }
  } else {
    return 'add'
  }
}

// 跳转原料表单
const editRawMaterial = (item: any, type: string) => {
  uni.navigateTo({
    url: `/pages-maintenance/replenish/replenishEditForm/rawMaterialForm?operateType=${type}&rawMaterialInfo=${encodeURIComponent(JSON.stringify(item))}&currentMachine=${currentMachine.value}`,
    // success: (opt) => {
    //   opt.eventChannel.on('submit', (params: object) => {
    //     console.log('接收', params)
    //   })
    // },
  })
}
const cleanForm = ref({
  reason: null,
  surplusHandle: null,
})

function cleanMessageBox(data: MaterialDataItem, materialIndex: number) {
  cleanMessage
    .confirm({
      title: `清除原料-${data.ingredientName}`,
      cancelButtonProps: {
        round: false,
        size: 'large',
        customStyle: 'background: rgba(248, 249, 252, 1); border: none; width: 100%;',
      },
      confirmButtonProps: {
        round: false,
        size: 'large',
        customStyle: 'background: rgba(53, 106, 253, 1); border: none; width: 100%',
      },
      beforeConfirm: ({ resolve }) => {
        if (isEditData.value && !('reason' in data)) {
          if (cleanForm.value.reason == null) {
            toast.warning('请选择清除原因')
            return resolve(false)
          }
          if (cleanForm.value.surplusHandle == null) {
            toast.warning('请选择剩余量处理')
            return resolve(false)
          }
        }
        resolve(true)
      },
    })
    .then(() => {
      // 先找到对应机器索引,再通过元器件id找到列表中的原始数据原料数据（originalMachineRawMaterial）
      const index = originalMachineRawMaterial.value.findIndex(
        (item) => item.machineName === currentMachine.value,
      )
      // 原始的当条数据
      const originalMaterialData = originalMachineRawMaterial.value[index].detailVOS[materialIndex]
      // 取数据里的元器件 容量 容量单位 库存预警比例赋值给新数据
      const newMaterialData = {
        componentId: originalMaterialData.componentId,
        componentName: originalMaterialData.componentName,
        capacity: originalMaterialData.capacity,
        capacityUnit: originalMaterialData.capacityUnit,
        waringPercent: originalMaterialData?.waringPercent,
        ingredientCategoriesId: originalMaterialData?.ingredientCategoriesId,
        // 如果是清除系统原始原料需添加表单字段
        ...(isEditData.value
          ? {
            reason: cleanForm.value.reason,
            surplusHandle: cleanForm.value.surplusHandle,
            surplusNumber: originalMaterialData.number,
          }
          : {}),
      }

      machineRawMaterial.value[index].detailVOS[materialIndex] = newMaterialData

      toast.success('清除成功')
    })
    .catch((error) => {
      console.log(error)
    })
}

// 清除时按钮操作判断数据源
const isEditData = ref(null)
const cleanDetailData = ref()
// 清除原料
const onClearMaterial = (data: MaterialDataItem) => {
  cleanDetailData.value = data
  cleanForm.value.reason = null
  cleanForm.value.surplusHandle = null
  // 通过元器件id找到列表中的原料数据
  const materialData = currentMachineRawMaterial.value.find(
    (item) => item.componentId === data.componentId,
  )
  // 先找到对应机器索引,再通过元器件id找到列表中的原始数据原料数据（originalMachineRawMaterial）
  const index = originalMachineRawMaterial.value.findIndex(
    (item) => item.machineName === currentMachine.value,
  )
  // 找到原始机器
  const originalMachine = originalMachineRawMaterial.value[index]
  // 找到原始数据原料索引
  const materialIndex = originalMachine.detailVOS.findIndex(
    (item) => item.componentId === materialData.componentId,
  )
  // 根据当前操作的清除数据是否属于新增原料进行不同的清除逻辑
  // 原料id相同(编辑)
  isEditData.value =
    materialData.ingredientId === originalMachine.detailVOS[materialIndex]?.ingredientId &&
    !('reason' in materialData)

  cleanMessageBox(data, materialIndex)
}

const submitEditForm = async () => {
  // 创建深拷贝避免污染原始数据
  // 原始数据
  const copyOriginalData = JSON.parse(JSON.stringify(originalMachineRawMaterial.value))
  // 复制一份现在数据
  const copyCurrentData = JSON.parse(JSON.stringify(machineRawMaterial.value))

  // 初始化数据容器
  const cleanMaterialData: any[] = []
  const updateMaterialData: any[] = []
  // 遍历所有机器
  copyCurrentData.forEach((currentMachine: MachineMaterialData, machineIndex: number) => {
    // 原始每一台机器数据
    const originalMachine = copyOriginalData[machineIndex]

    // 遍历当前机器下的每个组件
    currentMachine.detailVOS.forEach(
      (
        currentComponent: MaterialDataItem & {
          // 自定义剩余量处理字段
          surplusDisposalData?: number
          selectSurplusDisposal?: number
          // 剩余量处理
          surplusDisposal?: number
        },
      ) => {
        // 原始元器件数据
        const originalComponent = originalMachine.detailVOS.find(
          (item: MaterialDataItem) => item.componentId === currentComponent.componentId,
        )

        // 清除条件判断
        if (originalComponent && originalComponent.ingredientId) {
          // 情况1：原有原料ID，现在没有原料ID（被清空）
          // 情况2：原料ID发生变化（需要先清除旧原料）
          // 情况3: 当前数据存在清除标记reason
          if (
            !currentComponent.ingredientId ||
            currentComponent.ingredientId !== originalComponent.ingredientId ||
            'reason' in currentComponent
          ) {
            cleanMaterialData.push({
              machineId: currentMachine.machineId,
              componentId: originalComponent.componentId,
              ingredientId: originalComponent.ingredientId,
              reason: currentComponent.reason,
              surplusHandle: currentComponent.surplusHandle,
              surplusNumber: originalComponent.number,
            })
          }
        }

        // 更新条件判断
        if (currentComponent.ingredientId) {
          delete currentComponent?.surplusDisposalData
          delete currentComponent?.selectSurplusDisposal
          // 情况1：新增原料（原始没有原料ID）
          // 情况2：修改原料（ID相同但其他属性变化）
          // 情况3：更换原料（ID不同，此时上面的清除逻辑会处理旧ID）
          if (
            !originalComponent?.ingredientId ||
            (originalComponent.ingredientId === currentComponent.ingredientId &&
              JSON.stringify(originalComponent) !== JSON.stringify(currentComponent)) ||
            (originalComponent.ingredientId &&
              originalComponent.ingredientId !== currentComponent.ingredientId)
          ) {
            updateMaterialData.push({
              machineId: currentMachine.machineId,
              componentId: currentComponent.componentId,
              ingredientId: currentComponent.ingredientId,
              number: currentComponent.number,
              productionDate: currentComponent.productionDate,
              ...(currentComponent.surplusDisposal
                ? {
                  surplusDisposal: currentComponent?.surplusDisposal,
                }
                : {}),
            })
          }
        }
      },
    )
  })

  // 提交的接口数据
  const params = []
  // 遍历当前的数据取机器id 组装接口数据
  copyCurrentData.forEach((item) => {
    params.push({
      machineId: item.machineId,
      carId: carData.value.id,
      // 加restockCleanList字段如果遍历cleanMaterialData如果params里的machineId等于cleanMaterialData里的数据的machineId就将那一项添加进restockCleanList
      restockCleanList: cleanMaterialData.filter(
        (cleanItem) => cleanItem.machineId === item.machineId,
      ),
      restockDetailList: updateMaterialData.filter(
        (editItem) => editItem.machineId === item.machineId,
      ),
      restockPhotoList: item.photoList.map((img: string) => {
        return {
          machineId: item.machineId,
          photoImg: img,
        }
      }),
    })
  })
  // 提交数据
  // 判断copyCurrentData里的每一个.photoList是否至少有3项否则提示
  if (copyCurrentData.some((item) => item.photoList.length < 1)) {
    toast.warning('每个机器请上传至少1张照片')
    return
  }

  await restockSubmit(params).then(async (res) => {
    if (res.success) {
      toast.success('提交成功')
      setTimeout(() => {
        eventChannel.emit('refresh')
        uni.navigateBack()
      }, 1000)
    } else {
      toast.error('操作失败')
    }
  })
  // console.log('清除数据:', cleanMaterialData)
  // console.log('更新数据:', updateMaterialData)
}
// 每当currentMachine变化时重新将MachineRawMaterial的对应机器名
watch(
  currentMachine,
  (newVal) => {
    currentMachineRawMaterial.value =
      machineRawMaterial.value.find((item) => item.machineName === newVal)?.detailVOS || []
    photoList.value = machineRawMaterial.value.find(
      (item) => item.machineName === newVal,
    )?.photoList
  },
  {
    immediate: true,
  },
)
watch(photoList, (newValue) => {
  machineRawMaterial.value.find((item) => item.machineName === currentMachine.value).photoList =
    newValue
})
</script>
<!--wot组件库的样式穿透-->
<script lang="ts">
export default {
  options: {
    styleIsolation: 'shared',
  },
}
</script>
<style scoped lang="scss">
:deep(.camera-sheet-class) {
  padding-bottom: 0 !important;
}

.label {
  &::before {
    content: '*';
    color: #356afd;
    margin-right: 2px;
  }
}

view {
  line-height: initial;
}

:deep(.wd-segmented__item--active.wd-segmented__item--active) {
  height: 60rpx;
  border-radius: 30rpx;
  background-color: #356afd;
  @apply bg-[#356afd] p-0;
}

:deep(.wd-segmented.wd-segmented) {
  height: 60rpx;
  border-radius: 30rpx;
  padding: 0;
  width: v-bind('Math.min(machineList.length * 25, 100) + "%"');
}

:deep(.wd-segmented__item-label.wd-segmented__item-label) {
  @apply flex flex-col justify-center h-full;
}

:deep(.clean-form .wd-message-box__content) {
  text-align: left;
}

:deep(.wd-segmented__item.is-active) {
  color: #fff;
}

:deep(.clean-form .wd-message-box__title) {
  @apply text-left py-0 fw-bold text-lg;
}

:deep(.clean-form .wd-message-box__body) {
  @apply pt-4 px-4;
}

:deep(.clean-form .wd-form-item) {
  @apply pl-0;
}

:deep(.clean-form .wd-form-item .wd-cell__left.wd-cell__left) {
  @apply mr-0 fw-bold h-[64rpx] max-w-[180rpx] min-w-[180rpx];
}

:deep(.clean-form .wd-form-item .wd-radio__label.wd-radio__label) {
  border: 1px solid #dde2e8;
  width: 120rpx;
  @apply bg-white rounded-2;
}

:deep(.clean-form .wd-form-item .wd-radio__shape) {
  display: none !important;
}

:deep(.clean-form .wd-form-item .wd-radio-group) {
  @apply flex;
}
</style>
