<template>
  <view class="flex flex-col h-full">
    <wd-cell-group custom-class="mb-2 p-3 top-cell">
      <wd-cell
        custom-title-class="text-[#979998] "
        custom-class="!p-0"
        v-for="data in machineLabel"
        :key="machineLabel[data.field]"
        :title="data.label"
        :value="data.formatter ? data.formatter(rawMaterialInfo) : rawMaterialInfo?.[data.field]"
      />
    </wd-cell-group>
    <template v-if="operateType === 'edit'">
      <view class="flex bg-white p-4">
        <view class="size-20 rounded-xl mr-3 bg-[#F1F2F2] overflow-hidden">
          <image
            mode="scaleToFill"
            :src="rawMaterialInfo.ingredientImg"
            class="w-full h-full rounded-xl"
          ></image>
        </view>
        <view>
          <view class="text-[#1A1A1A] text-sm font-bold">
            {{ rawMaterialInfo.ingredientName }}
          </view>
          <view class="text-[#656666] text-sm mt-4">
            剩余量：
            <text
              :class="[
                rawMaterialInfo.number <=
                (rawMaterialInfo.capacity * rawMaterialInfo.waringPercent) / 100
                  ? 'text-[#f25c5c]'
                  : 'text-[#656666]',
              ]"
            >
              {{ rawMaterialInfo.number + rawMaterialInfo.capacityUnit }}
            </text>
          </view>
          <view class="text-[#656666] text-sm mt-4">
            剩余保质期：{{ rawMaterialInfo.remainingShelfLife }}h
          </view>
        </view>
      </view>
      <wd-cell-group custom-class="mb-2 px-2 pb-3 ingredient-info">
        <wd-cell
          custom-title-class="text-[#979998] "
          custom-class="!p-0"
          v-for="data in detailLabel"
          :key="rawMaterialInfo[data.field]"
          :title="data.label"
          :value="data.formatter ? data.formatter(rawMaterialInfo) : rawMaterialInfo[data.field]"
        />
      </wd-cell-group>
    </template>

    <Form :data="rawMaterialInfo" :operateType="operateType" @submit="submit" class="flex-1" />
    <wd-gap safe-area-bottom height="0"></wd-gap>
  </view>
</template>

<script setup lang="ts">
/**
 * @file 补料表单-单项原料配置
 * <AUTHOR>
 * @date 2025/4/10
 */
import { MaterialDataItem } from '@/pages-maintenance/replenish/types'
import Form from '@/pages-maintenance/replenish/replenishEditForm/Form.vue'
import { useToast } from 'wot-design-uni'

const toast = useToast()

const rawMaterialInfo = ref<MaterialDataItem>()
// 机器信息
const machineLabel = [
  { label: '料仓名称', field: 'componentName' },
  {
    label: '整仓容量',
    field: 'capacity',
    formatter: (data) => {
      return data?.capacity + data?.capacityUnit
    },
  },
  {
    label: '预警值',
    field: 'waringPercent',
    formatter: (data) => {
      return data?.waringPercent + '%'
    },
  },
]
// 列表字段
const detailLabel = [
  { label: '编码', field: 'ingredientCode' },
  { label: '分类', field: 'ingredientTypeName' },
  { label: '品牌', field: 'ingredientModel' },
  {
    label: '更换保质期',
    field: 'ingredientUseShelfLife',
    formatter: (data) => {
      return data.ingredientUseShelfLife + 'h'
    },
  },
]
const operateType = ref<'add' | 'edit'>()
const currentMachine = ref()
onLoad((opt) => {
  const instance = getCurrentInstance().proxy
  rawMaterialInfo.value = JSON.parse(decodeURIComponent(opt.rawMaterialInfo))
  operateType.value = opt.operateType
  currentMachine.value = opt.currentMachine
  uni.setNavigationBarTitle({
    title: rawMaterialInfo.value.componentName,
  })
})
const submit = (e) => {
  uni.$emit('updateRawMaterial', {
    data: toRaw(e),
    currentMachine: currentMachine.value,
  })
  const tips = operateType.value === 'edit' ? '修改成功' : '新增成功'
  toast.success(tips)
  setTimeout(() => uni.navigateBack(), 1000)
}
</script>
<!--wot组件库的样式穿透-->
<script lang="ts">
export default {
  options: {
    styleIsolation: 'shared',
  },
}
</script>
<style scoped lang="scss">
view {
  line-height: 1 !important;
}
:deep(.top-cell .wd-cell-group__body) {
  @apply flex flex-col gap-2;
}
:deep(.wd-cell__wrapper.wd-cell__wrapper) {
  line-height: inherit;
  padding: 0;
}

:deep(.ingredient-info .wd-cell-group__body) {
  @apply rounded-3 overflow-hidden p-3 bg-[#F8F9FC] flex flex-col gap-2;
}

:deep(.ingredient-info .wd-cell) {
  @apply bg-inherit;
}
</style>
