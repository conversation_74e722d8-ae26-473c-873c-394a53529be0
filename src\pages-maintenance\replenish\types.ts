// 车辆状态枚举
import { BulldozerStatusEnum } from '@/pages/sell-car/types'

export enum CarStatusEnum {
  /* 就绪 */
  SELL_CAR_FREE = 1000,
  /* 运行中 */
  SELL_CAR_BUSY = 1001,
  /* 充电中 */
  SELL_CAR_CHARGING = 1002,
  /* 故障中 */
  SELL_CAR_FAILURE = 1003,
  /* 等待中 */
  SELL_CAR_WAIT = 1004,
  /* 离线中 */
  SELL_CAR_OFFLINE = 1006,
}

// 车辆状态显示
export const CarStatusMap = {
  [CarStatusEnum.SELL_CAR_FREE]: {
    bg: 'rgba(234, 240, 255)',
    status: BulldozerStatusEnum.OFFLINE,
    color: 'rgba(0, 194, 144, 1)',
    text: '在线',
  },
}
// componentType: 'material' // material:原材料，water:用水，package:包装材料,trash:废料
// 定义componentType的类型
export type ComponentType = 'material' | 'water' | 'package' | 'trash'
export interface MaterialDataItem {
  // 补料id
  recordId?: number
  // 元器件id
  componentId: number
  // 元器件名称
  componentName: string
  // 原料id
  ingredientId?: number
  // 原料code
  ingredientCode?: string
  // 原料名称
  ingredientName?: string
  // 原料图片
  ingredientImg?: string
  // 原料类型
  ingredientType?: string
  // 原料型号
  ingredientModel?: string
  // 使用保质期(小时)
  ingredientUseShelfLife?: string
  // 原料分类id
  ingredientCategoriesId: string
  // 剩余保质期
  remainingShelfLife?: string
  // 当前库存
  number?: number
  // 保质期(小时)
  shelfLife?: number
  // 容量
  capacity: number
  // 容量单位
  capacityUnit: string
  // 库存预警比例
  waringPercent: string
  // 生产日期
  productionDate?: string
  // 清除原因
  reason?: number
  // 剩余量处理
  surplusHandle?: number
  // 元器件类型
  componentType?: ComponentType
  // 元器件子类型名称:只有当componentType是water,package,trash时这个字段才有用，是water时对应0:制作用水1制冰用水2清洗用水，是package时0杯子1杯盖2封口膜3喷码墨，是trash时0表示废水桶1表示废渣桶
  componentMiniType?: number
}
// 接口机器原料总数据
export interface MachineMaterialData {
  carId: number
  machineId: number
  machineName: string
  detailVOS: MaterialDataItem[]
  photoList?: string[]
}
