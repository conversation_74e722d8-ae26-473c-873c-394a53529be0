<template>
  <wd-message-box
    selector="wd-message-box-slot"
    custom-class="show-code-message-box"
  ></wd-message-box>
</template>
<script lang="ts" setup>
import { useToast, useMessage as useWotMessage } from 'wot-design-uni'
import { remoteBrake } from '@/service/maintenanceApi'

// 提示弹窗
const message = useWotMessage('wd-message-box-slot')
const toast = useToast()

type MessageProps = {
  carId?: string
  recovery?: string
  title?: string
  tips?: string
  onSubmitSuccess?: () => void
}

const show = (inner?: MessageProps) => {
  const { carId, recovery, title, tips, onSubmitSuccess } = inner
  message
    .confirm({
      title,
      msg: tips,
      cancelButtonProps: {
        round: false,
        size: 'large',
        customStyle: 'background: rgba(248, 249, 252, 1); border: none; width: 100%;',
      },
      confirmButtonProps: {
        round: false,
        size: 'large',
        customStyle: 'background: rgba(53, 106, 253, 1); border: none; width: 100%',
      },
    })
    .then(() => {
      remoteBrake({ carId, recovery }).then((res) => {
        if (res.success) {
          toast.success('操作成功')
          onSubmitSuccess()
        } else {
          toast.error(res.message)
        }
      })
    })
    .catch((error) => {
      console.log(error)
    })
}

// 暴露方法给父组件
defineExpose({ show })
</script>

<style lang="scss">
.show-code-message-box {
  width: 92vw !important;

  .wd-message-box__title {
    font-weight: bold;
    font-size: 36rpx;
    --wot-dark-color: white;
    text-align: left;
  }

  .wd-message-box__content {
    border-radius: 8px;
    margin-top: 16px;
  }
}
</style>
