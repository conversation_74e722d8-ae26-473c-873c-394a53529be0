<template>
  <wd-message-box selector="wd-message-box-slot" custom-class="mark-point-message-box">
    <wd-input
      no-border
      custom-class="m-2 rounded-lg border border-solid border-gray-300"
      size="large"
      v-model="form.name"
      placeholder="输入编号"
    />
    <wd-input
      no-border
      custom-class="m-2 rounded-lg border border-solid border-gray-300 mt-4"
      size="large"
      v-model="form.remark"
      placeholder="备注"
    />
  </wd-message-box>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import { useMessage as useWotMessage, useToast } from 'wot-design-uni'
const props = defineProps<{ fetch: (params: any) => Promise<any>; data?: any }>()

// 提示弹窗
const message = useWotMessage('wd-message-box-slot')
const toast = useToast()
const form = ref({ name: '', remark: '', ...props.data })
watch(
  () => props.data,
  (newVal) => {
    form.value = { name: '', remark: '', ...newVal }
  },
)
type MessageProps = {
  title?: string
  onSubmitSuccess?: () => void
}

const show = (inner?: MessageProps) => {
  const { title, onSubmitSuccess } = inner
  message
    .confirm({
      title,
      cancelButtonProps: {
        round: false,
        size: 'large',
        block: true,
        customStyle: 'background: rgba(248, 249, 252, 1); border: none; width: 100%',
      },
      confirmButtonProps: {
        round: false,
        size: 'large',
        customStyle: 'background: rgba(53, 106, 253, 1); border: none; width: 100%',
      },
      beforeConfirm: async ({ resolve }) => {
        if (!form.value.name) {
          resolve(false)
          toast.error('请输入编号')
          return
        }
        // todo 新增点并验证点是否存在
        props
          .fetch({ ...form.value })
          .then((res) => {
            if (res.statusCode === 101) {
              resolve(true)
              toast.success('操作成功')
              onSubmitSuccess?.()
            } else {
              resolve(false)
              toast.error(res.message)
            }
          })
          .catch((error) => {
            console.log('error', error)
          })
      },
    })
    .catch((error) => {
      console.log(error)
    })
}

// 暴露方法给父组件
defineExpose({ show })
</script>

<style lang="scss">
.mark-point-message-box {
  width: 92vw !important;

  .wd-message-box__title {
    font-weight: bold;
    font-size: 36rpx;
    --wot-dark-color: white;
    text-align: left;
  }

  .wd-message-box__content {
    border-radius: 8px;
    margin-top: 16px;
  }
}
</style>
