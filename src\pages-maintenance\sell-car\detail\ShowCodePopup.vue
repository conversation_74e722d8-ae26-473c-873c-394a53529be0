<template>
  <wd-message-box selector="wd-message-box-slot" custom-class="show-code-message-box">
    <view class="flex justify-center items-center gap-1 my-4">
      <view
        class="flex flex-col items-center justify-center border border-solid border-[#DDE2E8] rounded-lg w-10 h-10"
        v-for="text in code.split('')"
        :key="text"
      >
        <text class="text-xl font-bold text-[#356AFD]">{{ text }}</text>
      </view>
    </view>
  </wd-message-box>
</template>
<script lang="ts" setup>
import { useMessage as useWotMessage } from 'wot-design-uni'
import { generateCode } from '@/utils/crypto'

// 提示弹窗
const message = useWotMessage('wd-message-box-slot')
// 调试码
const code = ref('')
type MessageProps = {
  carId?: string
  title?: string
  onSubmitSuccess?: () => void
}

// 调试码有效期为一小时
const show = (inner?: MessageProps) => {
  const { title, carId } = inner
  generateCode(carId, new Date()).then((res) => {
    code.value = res
  })
  message
    .confirm({
      title,
      confirmButtonText: '我知道了',
      cancelButtonProps: {
        round: false,
        size: 'large',
        block: true,
        customStyle: 'background: rgba(248, 249, 252, 1); border: none; width: 100%; display: none',
      },
      confirmButtonProps: {
        round: false,
        size: 'large',
        customStyle: 'background: rgba(53, 106, 253, 1); border: none; width: 100%',
      },
    })
    .catch((error) => {
      console.log(error)
    })
}

// 暴露方法给父组件
defineExpose({ show })
</script>

<style lang="scss">
.show-code-message-box {
  width: 92vw !important;

  .wd-message-box__title {
    font-weight: bold;
    font-size: 36rpx;
    --wot-dark-color: white;
    text-align: left;
  }

  .wd-message-box__content {
    border-radius: 8px;
    margin-top: 16px;
  }
}
</style>
