<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '奶茶车' },
}
</route>
<template>
  <view class="h-full overflow-hidden">
    <view class="flex flex-col justify-between h-full">
      <view class="bg-white p-2">
        <wd-cell
          :title="data.carName"
          :custom-style="`--wot-cell-arrow-color: ${getDeviceStatus(data.status).color}`"
          custom-class="rounded-lg"
          custom-title-class="font-bold text-lg -mt-1"
        >
          <text class="text-sm text-[#1A1A1A] mr-2">
            距你{{ formatDecimal(data?.distance || distance) }}
          </text>
          <text class="text-sm" style="color: var(--wot-cell-arrow-color)">
            {{ getDeviceStatus(data.status).text }}
          </text>
        </wd-cell>
        <view class="rounded-lg bg-[#F8F9FC] p-4">
          <view v-for="key of detailKeys" :key="key.field" class="flex justify-between leading-6">
            <text class="text-sm text-[#979998]">{{ key.label }}</text>
            <text class="text-sm text-[#1A1A1A]">{{ data[key.field] || '-' }}</text>
          </view>
        </view>
        <LAButton
          custom-class="mt-2"
          block
          :round="false"
          size="xl"
          type="primary"
          pale
          @click="openShowCodePopup"
        >
          生成调试码
        </LAButton>
        <LAButton
          custom-class="mt-2"
          block
          :round="false"
          size="xl"
          type="primary"
          pale
          @click="toOutletDebugPage"
        >
          出料量调试
        </LAButton>
        <wd-select-picker
          custom-class="w-full point-picker"
          custom-content-class="point-picker-content"
          title="呼叫车辆"
          type="radio"
          v-model="currentCar"
          :show-confirm="false"
          align-right
          :columns="pointList"
          @change="callingCar"
          use-default-slot
        >
          <LAButton custom-class="mt-2" block :round="false" size="xl" type="primary" pale>
            呼叫车辆
          </LAButton>
          <template #item="{ item }">
            <view class="flex items-center justify-between">
              <text>{{ item.label }}</text>
              <text>距您{{ formatDecimal(item?.distance) }}</text>
            </view>
          </template>
        </wd-select-picker>
        <LAButton
          custom-class="mt-2"
          block
          :round="false"
          size="xl"
          type="primary"
          pale
          @click="resumeOperations"
        >
          恢复运营
        </LAButton>
        <LAButton
          custom-class="mt-2"
          block
          :round="false"
          size="xl"
          type="error"
          pale
          @click="onBrakeChange"
        >
          {{ isBrake ? '远程恢复' : '远程制动' }}
        </LAButton>
      </view>
      <view class="p-2 mt-auto bg-white">
        <LAButton type="primary" :round="false" size="lg" block @click="openMarkPointPopup">
          标记当前目标点
        </LAButton>
      </view>
    </view>
    <!-- 标记当前目标点弹窗 -->
    <MarkPointPopup ref="markPointPopupRef" :fetch="fetch" />
    <!-- 生成调试码弹窗 -->
    <ShowCodePopup ref="showCodePopupRef" :fetch="fetch" />
    <!-- 恢复运营弹窗 -->
    <ResumeOperation ref="resumeOperationRef" />
    <!-- 远程制动弹窗 -->
    <BrakePopup ref="brakePopupRef" />
  </view>
</template>
<script lang="ts" setup>
import MarkPointPopup from './MarkPointPopup.vue'
import ShowCodePopup from './ShowCodePopup.vue'
import ResumeOperation from './ResumeOperation.vue'
import BrakePopup from './BrakePopup.vue'
import { onLoad } from '@dcloudio/uni-app'
import { getSellCarDetail, markTargetPoint } from '@/service/api'
import { getDeviceStatus } from '../utils'
import { formatDecimal } from '@/utils'
import { useToast } from 'wot-design-uni'
import { callCar, getPointList } from '@/service/maintenanceApi'
import useNativeLocation from '@/hooks/useNativeLocation'

const { getRawLocation } = useNativeLocation()
const toast = useToast()

const markPointPopupRef = ref()
const showCodePopupRef = ref()
const resumeOperationRef = ref()
const brakePopupRef = ref()

onLoad((options) => {
  try {
    const carData = JSON.parse(decodeURIComponent(options.carData))
    const carId = options.carId
    distance.value = carData.distance

    fetchDetailData(carId)
  } catch (error) {
    toast.warning('数据解析失败')
    console.log(error)
  }
})

// 标记目标点请求
const fetch = (params: any) => {
  return markTargetPoint({ ...params, carId: data.value.id })
}

const distance = ref()
// 远程数据
const data = ref<{
  id?: string
  carName?: string
  carCode?: string
  plateNo?: string
  modelNumber?: string
  runAreaName?: string
  status?: string
  distance?: number
}>({})

const detailKeys = [
  { field: 'carCode', label: '编码' },
  { field: 'plateNo', label: '车牌号' },
  { field: 'modelNumber', label: '型号' },
  { field: 'runAreaName', label: '投放区域' },
]

// 生成调试码
const openShowCodePopup = () => {
  showCodePopupRef.value.show({
    title: `生成调试码-${data.value.carName}`,
    carId: data.value.id,
  })
}
// 出料量调试
const toOutletDebugPage = () => {
  uni.navigateTo({
    url: `/pages-maintenance/sell-car/outlet-debug/index?data=${JSON.stringify(data.value)}`,
  })
}

const currentCar = ref()
const pointList = ref([])
const getMapList = async () => {
  const location = await getRawLocation()
  const params = {
    mapId: data.value?.runAreaId,
    location: `${location.longitude} ${location.latitude}`,
  }
  getPointList(params).then((res: any) => {
    pointList.value = res.data.map((item: any) => ({
      label: item.name,
      value: item.id,
      distance: item.distance,
      location: item.location,
    }))
  })
}
getMapList()
const callingCar = (value: any) => {
  const params = {
    pointId: value.value,
    carId: data.value.id,
  }
  callCar(params).then((res: any) => {
    if (res.success) {
      toast.success('呼叫成功，请稍等')
    } else {
      toast.error(res.message)
    }
  })
}

const openMarkPointPopup = () => {
  markPointPopupRef.value.show({
    title: `标记当前目标点-${data.value.carName}`,
    onSubmitSuccess: () => {
      console.log('onSubmitSuccess')
    },
  })
}

function fetchDetailData(carId: string) {
  getSellCarDetail({ carId }).then((res: any) => {
    data.value = res.data
    // 除了1004，其他都可以远程制动
    isBrake.value = data.value.status === 1004
  })
}

const resumeOperations = () => {
  resumeOperationRef.value.show({
    carId: data.value.id,
    onSubmitSuccess: () => {
      console.log('onSubmitSuccess')
    },
  })
}

//  远程制动
const isBrake = ref(false)
const onBrakeChange = () => {
  // recovery 0 恢复 1 制动
  const title = isBrake.value ? '是否远程恢复？' : '是否远程制动？'
  const tips = isBrake.value ? '确认后车辆将恢复行驶' : '确认后车辆将停止行驶'
  brakePopupRef.value.show({
    title,
    tips,
    carId: data.value.id,
    recovery: isBrake.value ? 0 : 1,
    onSubmitSuccess: () => {
      isBrake.value = !isBrake.value
    },
  })
}
</script>

<style lang="scss" scoped>
.box-table :deep(.wd-table__content--header .wd-table__value) {
  font-size: 28rpx;
  font-weight: bold;
}
</style>
<style lang="scss">
.point-picker-content {
  .wd-radio__label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
}
</style>
