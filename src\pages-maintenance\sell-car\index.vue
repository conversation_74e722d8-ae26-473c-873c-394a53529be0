<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '奶茶车' },
}
</route>
<template>
  <PageList :requestApi="request" :search="search" ref="pageListRef" :auto="false">
    <template #default="{ item }">
      <view class="m-2">
        <wd-cell :title="item.carName" :label="item.runAreaName || '-'" is-link
          :to="`${nextPageUrl}?carId=${item.id}&carData=${encodeURIComponent(JSON.stringify(item))}`"
          :custom-style="`--wot-cell-arrow-color: ${getDeviceStatus(item.status).color}`" custom-class="rounded-lg"
          custom-title-class="font-bold text-lg -mt-1" custom-label-class="text-[#1A1A1A]! text-sm!">
          <view class="inline-flex gap-2 justify-between -mr-2 items-center">
            <text class="text-[#1A1A1A] text-sm!" v-if="item?.distance">距你{{ formatDecimal(item.distance) }}</text>
            <text style="color: var(--wot-cell-arrow-color)">
              {{ getDeviceStatus(item.status).text }}
            </text>
          </view>
        </wd-cell>
      </view>
    </template>
  </PageList>
</template>
<script lang="ts" setup>
import { getSellCarList } from '@/service/api'
import useNativeLocation from '@/hooks/useNativeLocation'
import { getDeviceStatus } from './utils'
import { formatDecimal } from '@/utils'
const { getRawLocation } = useNativeLocation({ autoInitial: false })
const search = { placeholder: '奶茶车名称/所在区域', searchKey: 'miniSearch' }
const pageListRef = ref<any>(null)
const nextPageUrl = ref<string>('')
const location = ref<any>(null)

const request = (params: any) => {
  // 先获取位置再进行请求接口，阻断主流程
  return getSellCarList({
    ...params,
    ...(location.value?.latitude && location.value?.longitude
      ? {
        lat: Number(location.value?.latitude.toFixed(8)),
        lon: Number(location.value?.longitude.toFixed(8)),
      }
      : {}),
  })
}

onLoad((options: { url: string }) => {
  nextPageUrl.value = options.url
  getRawLocation().then((loc) => {
    location.value = loc
    pageListRef.value?.paging.reload()
  })
})
</script>
