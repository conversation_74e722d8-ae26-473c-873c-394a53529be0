<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '出料量调试' },
}
</route>
<template>
  <view class="flex flex-col h-full">
    <view class="flex-1 h-0 overflow-auto">
      <!--顶部区域距离-->
      <view class="p-2 bg-white">
        <view class="flex justify-between">
          <view>
            <wd-icon name="location" size="16px"></wd-icon>
            <text class="text-sm">{{ carData?.runAreaName }}</text>
          </view>
          <view class="flex gap-1 items-center">
            <text class="text-sm">{{ carData?.carName }}</text>
            <text class="text-sm" :style="{ color: getDeviceStatus(carData?.status).color }">
              {{ getDeviceStatus(carData?.status).text }}
            </text>
            <wd-divider custom-class="!h-[0.75rem]" vertical />

            <text class="text-sm">{{ formatDecimal(carData?.distance) }}</text>
          </view>
        </view>
        <wd-segmented
          v-if="machineList.length > 0"
          :options="machineList"
          v-model:value="selectedMachine"
          @change="changeMachine"
          custom-class="w-max! mt-4"
        />
      </view>

      <!--补料区域-->
      <template v-if="bucketList.length > 0">
        <view v-for="item in bucketList" class="mt-2 p-4 bg-white" :key="item.recordId">
          <view class="flex justify-between items-center">
            <view class="fw-bold text-base">{{ item?.componentName }}</view>
            <template v-if="item.ingredientId">
              <view class="flex justify-end">
                <view class="flex">
                  <view
                    class="rounded-md py-[3px] px-2.5 text-sm text-[#356AFD] bg-[#eaf0ff]"
                    @click="openOutletDebugMessage(item)"
                  >
                    校准
                  </view>
                </view>
              </view>
            </template>
            <view v-else class="text-sm text-[#F25555]">未获取到原料信息</view>
          </view>

          <view class="flex mb-2" v-if="item.ingredientId">
            <view class="size-20 rounded-xl mr-3 bg-[#F1F2F2] overflow-hidden">
              <GoodsImage :src="item.ingredientImgPath" />
            </view>
            <view>
              <view class="text-[#1A1A1A] text-sm font-bold">{{ item?.ingredientName }}</view>
              <view class="text-[#656666] text-sm mt-1.5">
                <text>单位出料量(5秒): {{ item.quantityPerUnit + item.ingredientUnit }}</text>
              </view>
            </view>
          </view>
        </view>
      </template>
      <view
        class="flex-1 flex flex-col justify-center items-center w-full h-full aaa"
        v-if="machineList.length === 0"
      >
        <image src="@/static/images/noData.png" class="w-12 h-12" mode="scaleToFill" />
        <text>暂无机器</text>
      </view>
    </view>

    <wd-message-box selector="outlet-debug-message-box" custom-class="outlet-debug-message-box">
      <view class="flex bg-white p-2">
        <view class="flex">
          <view class="size-20 rounded-xl mr-3 bg-[#F1F2F2] overflow-hidden">
            <GoodsImage :src="outletDebugMessageData?.ingredientImgPath" />
          </view>
          <view>
            <view class="text-[#1A1A1A] text-sm font-bold text-left">
              {{ outletDebugMessageData?.ingredientName }}
            </view>
            <view class="text-[#656666] text-sm mt-1.5">
              <text>
                单位出料量(5秒):
                {{ outletDebugMessageData.quantityPerUnit + outletDebugMessageData.ingredientUnit }}
              </text>
            </view>
          </view>
        </view>
      </view>
      <view>
        <view class="text-sm text-[#1A1A1A] fw-bold mb-2 text-left wd-input__label is-required">
          <text style="color: var(--wot-cell-required-color)">*</text>
          单位出料量(5秒)
        </view>
        <view
          class="flex justify-between border border-[#DDE2E8] rounded-2 border-solid w-full box-border p-2"
        >
          <wd-input
            no-border
            v-model="outletDebugForm.submitOutletDebug"
            placeholder="请输入单位出料量"
          />
          <view class="text-sm">{{ outletDebugMessageData.ingredientUnit }}</view>
        </view>
      </view>
    </wd-message-box>
  </view>
</template>

<script setup lang="ts">
/**
 * @file 补料表单
 * <AUTHOR>
 * @date 2025/4/8
 */
import { useMessage, useToast } from 'wot-design-uni'
import { getOutletDebugList2, submitOutletDebug } from '@/service/maintenanceApi'
import GoodsImage from '@/components/GoodsImage.vue'
import { getDeviceStatus } from '../utils'
import { formatDecimal } from '@/utils'
const toast = useToast()
// 机器列表
const machineList = ref<string[]>([])
// 当前车辆数据
const carData = ref()
// 选中的机器
const selectedMachine = ref()
const outletDebugMessage = useMessage('outlet-debug-message-box')
const outletDebugMessageData = ref<{
  ingredientImgPath?: string
  ingredientName?: string
  quantityPerUnit?: number
  ingredientUnit?: string
}>({})

const machineId = computed(() => {
  return selectedMachine.value === '机器A'
    ? carData.value.deviceMachineCarIdA
    : carData.value.deviceMachineCarIdB
})

// 料仓列表数据
const bucketList = ref([])
const outletDebugForm = ref({ submitOutletDebug: '' })

const openOutletDebugMessage = (item: any) => {
  outletDebugMessageData.value = item
  outletDebugForm.value.submitOutletDebug = item.quantityPerUnit
  outletDebugMessage.confirm({
    title: `校准-${item.componentName}`,
    cancelButtonProps: {
      round: false,
      size: 'large',
      block: true,
      customStyle: 'background: rgba(248, 249, 252, 1); border: none; width: 100%',
    },
    confirmButtonProps: {
      round: false,
      size: 'large',
      customStyle: 'background: rgba(53, 106, 253, 1); border: none; width: 100%',
    },
    beforeConfirm: async ({ resolve }) => {
      if (!outletDebugForm.value.submitOutletDebug) {
        resolve(false)
        toast.error('请输入单位出料量')
        return
      }
      if (Number.isNaN(Number(outletDebugForm.value.submitOutletDebug))) {
        resolve(false)
        toast.error('请输入数字')
        return
      }
      if (Number(outletDebugForm.value.submitOutletDebug) <= 0) {
        resolve(false)
        toast.error('请输入大于0的单位出料量')
        return
      }
      const params = {
        carId: carData.value.id,
        machineId: machineId.value,
        componentId: item.componentId,
        ingredientId: item.ingredientId,
        ingredientUnit: item.ingredientUnit,
        quantityPerUnit: outletDebugForm.value.submitOutletDebug,
      }

      if (item.adjustId) {
        params.id = item.adjustId
      }
      submitOutletDebug(params).then((res: any) => {
        if (res.statusCode === 101) {
          resolve(true)
          toast.success('操作成功')
          changeMachine()
        } else {
          resolve(false)
          toast.error(res.message)
        }
      })
    },
  })
}

const changeMachine = () => {
  getOutletDebugList2({
    machineId: machineId.value,
  }).then((res: any) => {
    if (Array.isArray(res.data)) {
      bucketList.value = res.data
    }
  })
}

onLoad(async (options) => {
  carData.value = JSON.parse(decodeURIComponent(options.data))
  machineList.value = [
    carData.value.deviceMachineCarNameA ? '机器A' : '',
    carData.value.deviceMachineCarNameB ? '机器B' : '',
  ].filter(Boolean)
})
</script>
<!--wot组件库的样式穿透-->
<script lang="ts">
export default { options: { styleIsolation: 'shared' } }
</script>

<style lang="scss">
.outlet-debug-message-box {
  width: 92vw !important;

  .wd-message-box__title {
    font-weight: bold;
    font-size: 36rpx;
    --wot-dark-color: white;
    text-align: left;
  }

  .wd-message-box__content {
    border-radius: 8px;
    margin-top: 16px;
  }
}
</style>

<style scoped lang="scss">
:deep(.wd-segmented__item--active.wd-segmented__item--active) {
  height: 60rpx;
  border-radius: 30rpx;
  background-color: #007aff;
  @apply bg-[#007aff] p-0;
}

:deep(.wd-segmented.wd-segmented) {
  height: 60rpx;
  border-radius: 30rpx;
  padding: 0;
  width: v-bind('Math.min(machineList.length * 25, 100) + "%"');
}

:deep(.wd-segmented__item-label.wd-segmented__item-label) {
  @apply flex flex-col justify-center h-full;
}

:deep(.wd-segmented__item.is-active) {
  color: #fff;
}
</style>
