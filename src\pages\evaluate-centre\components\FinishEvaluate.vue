<template>
  <z-paging ref="paging" refresher-only @onRefresh="onRefresh" :use-page-scroll="true">
    <view
      v-if="userEvaluateData.length"
      class="m-2 p-2 rounded-2 box-border h-full flex gap-4 flex-col"
    >
      <view
        v-for="evaluateDataItem in userEvaluateData"
        :key="evaluateDataItem.orderEvaluate.id"
        class="bg-white p-4 rounded-2"
      >
        <view class="text-sm text-[#979998] box-border flex flex-col gap-4">
          <view>{{ evaluateDataItem.orderEvaluate.userReviewDate }}</view>
          <view class="flex gap-2 items-center">
            <wd-rate
              disabled
              disabled-color="#F9744B"
              :modelValue="
                getHighestScore({
                  serverScore: evaluateDataItem.orderEvaluate.serverScore,
                  efficiencyScore: evaluateDataItem.orderEvaluate.efficiencyScore,
                  deliveryScore: evaluateDataItem.orderEvaluate.deliveryScore,
                }).maxScore
              "
              active-color="#F9744B"
            />
            <view v-for="scoreItem in scoreFormLabel.carScore" :key="scoreItem.label">
              {{ scoreItem.label
              }}{{
                evaluateDataItem.orderEvaluate[scoreItem.field] === 5
                  ? '5星'
                  : ratingTextMap[evaluateDataItem?.orderEvaluate[scoreItem.field]]
              }}
            </view>
          </view>
          <wd-divider color="#D9DCDC" custom-class="m-0! w-full p-0!"></wd-divider>
        </view>
        <view
          v-for="(item, index) in evaluateDataItem.orderGoodsEvaluates"
          class="box-border flex flex-col gap-4 mt-4"
          :key="item.id"
        >
          <view class="flex justify-between">
            <view class="box-border flex flex-col gap-4">
              <view class="text-base text-[#1A1A1A]">{{ item.goodsName }}</view>
              <view class="text-sm text-[#656666]">
                {{ item.cup }},{{ item.temperature }},{{ item.sweet }}
              </view>
            </view>
            <GoodsImage width="36" height="36" :src="item.goodsUrl" />
          </view>

          <view class="flex gap-2 items-center">
            <wd-rate
              disabled
              disabled-color="#F9744B"
              :modelValue="
                getHighestScore({
                  tasteScore: item.tasteScore,
                  healthScore: item.healthScore,
                  costScore: item.costScore,
                }).maxScore
              "
              active-color="#F9744B"
            />
            <view
              class="text-sm text-[#979998]"
              v-for="scoreItem in scoreFormLabel.drinkScore"
              :key="scoreItem.label"
            >
              {{ scoreItem.label
              }}{{ item[scoreItem.field] === 5 ? '5星' : ratingTextMap[item[scoreItem.field]] }}
            </view>
          </view>
          <wd-divider
            v-if="
              index !== evaluateDataItem?.orderGoodsEvaluates?.length - 1 ||
              evaluateDataItem?.orderEvaluate?.userReview ||
              evaluateDataItem?.orderEvaluate?.userEvaluateFiles?.length ||
              evaluateDataItem?.orderEvaluate?.merchantReview
            "
            color="#D9DCDC"
            custom-class="m-0! w-full p-0!"
          ></wd-divider>
        </view>
        <view
          class="flex flex-col gap-4 mt-4"
          v-if="
            evaluateDataItem.orderEvaluate.userReview ||
            evaluateDataItem?.orderEvaluate?.userEvaluateFiles?.length > 0
          "
        >
          <view class="text-base text-[#1A1A1A]" v-if="evaluateDataItem.orderEvaluate.userReview">
            {{ evaluateDataItem.orderEvaluate.userReview }}
          </view>
          <view
            class="flex gap-1 flex-wrap"
            v-if="evaluateDataItem?.orderEvaluate?.userEvaluateFiles?.length > 0"
          >
            <GoodsImage
              enablePreview
              v-for="img in evaluateDataItem.orderEvaluate.userEvaluateFiles"
              :src="img"
              :key="img"
            />
          </view>
          <wd-divider
            v-if="
              evaluateDataItem.orderEvaluate.merchantReview &&
              (evaluateDataItem.orderEvaluate.userReview ||
                evaluateDataItem?.orderEvaluate?.userEvaluateFiles?.length > 0)
            "
            color="#D9DCDC"
            custom-class="m-0! w-full p-0!"
          ></wd-divider>
        </view>
        <view
          class="flex flex-col gap-4 mt-4"
          v-if="evaluateDataItem?.orderEvaluate?.merchantReviewDate"
        >
          <view class="text-sm text-[#979998]">
            <text class="mr-2">商家回复</text>
            {{ evaluateDataItem.orderEvaluate.merchantReviewDate }}
          </view>
          <view class="text-base text-[#1A1A1A]">
            {{ evaluateDataItem.orderEvaluate.merchantReview }}
          </view>
          <view
            class="flex gap-1 flex-wrap"
            v-if="evaluateDataItem?.orderEvaluate?.merchantEvaluateFiles.length > 0"
          >
            <GoodsImage
              enablePreview
              v-for="img in evaluateDataItem.orderEvaluate.merchantEvaluateFiles"
              :src="img"
              :key="img"
            />
          </view>
        </view>
      </view>
    </view>
    <view v-else class="h-[90vh] flex flex-col justify-center items-center">
      <image src="../../../static/images/noData.png" class="w-12 h-12" mode="scaleToFill" />
      <text class="mt-2">暂无数据</text>
    </view>
  </z-paging>
</template>

<script setup lang="ts">
import { scoreFormLabel, ratingTextMap } from '@/pages/evaluate-centre/types'
import { ref } from 'vue'
import GoodsImage from '@/components/GoodsImage.vue'
import { getAlreadyEvaluateList } from '@/service/api'
// 传递userEvaluateData
// const props = defineProps({
//   userEvaluateData: {
//     type: Array,
//     default: () => [
//       {
//         id: 1234,
//         time: '2025-07-03 10:00:00',
//         carScore: {
//           // 服务评分
//           serviceScore: 4,
//           // 出餐效率评分
//           efficiencyScore: 2,
//           // 配送评分
//           deliveryScore: 4,
//         },
//         drinkScore: [
//           {
//             skuId: '1',
//             name: '茉莉花香拿铁',
//             skuName: '标准杯、冰、标准糖',
//             tasteScore: 4,
//             hygieneScore: 5,
//             costPerformanceScore: 1,
//             goodsImg:
//               'http://************:9099/group1/M00/00/04/wKgARWheE9yAEc3LAADXn0UMQo0065.jpg',
//           },
//           {
//             skuId: '2',
//             name: '生椰拿铁',
//             skuName: '标准杯、冰、半糖',
//             tasteScore: 3,
//             hygieneScore: 2,
//             costPerformanceScore: 3,
//             goodsImg:
//               'http://************:9099/group1/M00/00/04/wKgARWhnRBaALC57AADzOZH3aWo517.jpg',
//           },
//         ],
//         content: '方便测试.................测试',
//         images: [
//           'http://************:9099/group1/M00/00/04/wKgARWheE9yAEc3LAADXn0UMQo0065.jpg',
//           'http://************:9099/group1/M00/00/04/wKgARWheE9yAEc3LAADXn0UMQo0065.jpg',
//           'http://************:9099/group1/M00/00/04/wKgARWheE9yAEc3LAADXn0UMQo0065.jpg',
//           'http://************:9099/group1/M00/00/04/wKgARWhnRBaALC57AADzOZH3aWo517.jpg',
//         ],
//         merchantResponse: {
//           time: '2025-07-03 10:00:00',
//           content: '商家回复.........',
//           images: ['http://************:9099/group1/M00/00/04/wKgARWhnRBaALC57AADzOZH3aWo517.jpg'],
//         },
//       },
//     ],
//   },
// })
const userEvaluateData = ref([])
const paging = ref(null)
onShow(() => {
  getEvaluateData()
})
// 获取数据
const getEvaluateData = async () => {
  await getAlreadyEvaluateList().then((res: any) => {
    console.log('刷新', res)
    userEvaluateData.value = res.data.records
  })
}
const onRefresh = async () => {
  await getEvaluateData()

  setTimeout(() => {
    paging.value.complete()
  }, 1000)
}

// 传入对象,返回最高分的字段以及分数比如serviceScore: 5,
const getHighestScore = (scores) => {
  if (!scores || typeof scores !== 'object') {
    return { maxScore: 0, maxScoreField: '' }
  }

  let maxScore = 0
  let maxScoreField = ''

  // 遍历对象的所有属性
  for (const [field, score] of Object.entries(scores)) {
    if (score > maxScore) {
      maxScore = score as number
      maxScoreField = field
    }
  }

  return { maxScore, maxScoreField }
}
// 模拟需要的数据
const value = ref(3)
</script>

<style scoped lang="scss">
view {
  line-height: 100%;
}
</style>
