<template>
  <PageList ref="pageListRef" :use-page-scroll="true" :requestApi="fetch" :search="search">
    <template #default="{ item, refresh }">
      <view class="p-4 bg-white rounded-xl m-2">
        <view
          @click="toDetails(item.id, item.orderStatus, item.sysOrderNum)"
          class="flex justify-between items-center pb-3 border-b border-b-[#EBEDF1] border-b-solid"
        >
          <view class="flex items-center">
            <view class="text-base text-black font-bold mr-1">{{ item.carName }}</view>
            <view
              class="h-4 text-xs rounded text-[#6d3202] px-1 bg-[#f0eae5]"
              v-if="ORDER_TYPE_MAP[item.orderType]"
            >
              {{ ORDER_TYPE_MAP[item.orderType] }}
            </view>
          </view>
          <view
            class="flex items-center text-sm text-gray-500"
            :style="{ color: statusColorMap[item.orderStatus] }"
          >
            <view>
              {{ orderStatusMap?.[item.orderStatus] }}
            </view>
            <wd-icon
              name="arrow-right"
              size="16px"
              :color="statusColorMap[item.orderStatus]"
            ></wd-icon>
          </view>
        </view>
        <view
          @click="toDetails(item.id, item.orderStatus, item.sysOrderNum)"
          class="flex justify-between items-center mt-4"
        >
          <view class="flex max-w-64">
            <view
              class="shrink-0 size-14 mr-1 rounded-lg overflow-hidden"
              v-for="img in item.goodsUrls?.slice(0, 4)"
              :key="img"
            >
              <GoodsImage :src="img" width="100%" height="100%" />
            </view>
            <view
              class="ml-3 text-sm text-[#1a1a1a] line-clamp-2"
              v-if="item.goodsNames.length === 1"
            >
              {{ item.goodsNames[0] }}
            </view>
          </view>
          <view class="text-sm">
            <view class="flex justify-end text-[#1A1A1A]">
              <view>￥</view>
              <view class="font-bold">{{ item.mainTotalPrice }}</view>
            </view>
            <view class="text-[#656666] text-right">共{{ item.total }}件</view>
          </view>
        </view>
        <view class="flex justify-end mt-4">
          <LAButton
            @click="openEvaluate(item.id, refresh)"
            plain
            custom-style="width:78px"
            type="primary"
          >
            去评价
          </LAButton>
        </view>
      </view>
    </template>
  </PageList>
</template>

<script setup lang="ts">
import { ORDER_TYPE_MAP, OrderStatus, orderStatusMap, statusColorMap } from '@/types/consumer'
import GoodsImage from '@/components/GoodsImage.vue'
import { getOrderList } from '@/service/api'
const fetch = (params) => {
  return getOrderList(params).then((res: any) => {
    // 过滤 records，只保留 evaluate === 0 && orderStatus === 2 的数据
    const filteredRecords = res.data.records.filter((item) => {
      return item.evaluate === 0 && item.orderStatus === 2
    })

    // 返回处理后的数据，保持其他字段不变
    return {
      ...res,
      data: {
        ...res.data,
        records: filteredRecords, // 替换为过滤后的 records
      },
    }
  })
}

const openEvaluate = (orderId, refresh) => {
  uni.navigateTo({
    url: `/pages/order/evaluate-form?orderId=${orderId}`,
    ...refresh,
  })
}
const toDetails = (orderId, orderStatus, sysOrderNum) => {
  switch (orderStatus.toString()) {
    case OrderStatus.WAITING:
      uni.navigateTo({
        url: `/pages/order/unpaid-detail?orderId=${orderId || ''}&sysOrderNum=${sysOrderNum || ''}`,
      })
      break
    case OrderStatus.CANCELLED:
      uni.navigateTo({
        url: `/pages/order/cancel-detail?orderId=${orderId || ''}&sysOrderNum=${sysOrderNum || ''}`,
      })
      break
    case OrderStatus.COMPLETED:
      uni.navigateTo({
        url: `/pages/order/complete-detail?orderId=${orderId || ''}&sysOrderNum=${sysOrderNum || ''}`,
      })
      break
    default:
      uni.navigateTo({
        url: `/pages/order/details?orderId=${orderId || ''}&sysOrderNum=${sysOrderNum || ''}`,
      })
      break
  }
}
</script>

<style scoped></style>
