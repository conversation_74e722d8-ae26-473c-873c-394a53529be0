<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '评价中心' },
}
</route>

<template>
  <view class="h-full">
    <wd-tabs swipeable color="#6D3202" custom-class="h-full bg-[#EFF1F5]!" v-model="tab">
      <wd-tab title="待评价" name="待评价">
        <WaitEvaluate />
      </wd-tab>
      <wd-tab title="已评价" name="已评价">
        <FinishEvaluate />
      </wd-tab>
    </wd-tabs>
  </view>
</template>

<script setup lang="ts">
import WaitEvaluate from '@/pages/evaluate-centre/components/WaitEvaluate.vue'
import FinishEvaluate from '@/pages/evaluate-centre/components/FinishEvaluate.vue'

const tab = ref('待评价')
</script>

<style scoped>
:deep(.wd-tabs__line) {
  background: #6d3202 !important;
}
</style>
