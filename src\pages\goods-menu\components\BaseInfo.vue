<template>
  <view class="flex-col gap-2 p-3">
    <!-- 定位开启消息提示 -->
    <view
      class="flex items-center justify-between bg-[#FFF7E4] text-[#6d3202] rounded-lg px-2 py-1 text-sm border border-[#EFE9E4] border-solid mb-3"
      @click="openSetting"
      v-if="globalStore.permission === 'denied'"
    >
      <text>请开启定位，享受完整服务</text>
      <view class="flex items-center gap-1">
        <text class="font-bold">去开启</text>
        <wd-icon name="arrow-right" size="10" />
      </view>
    </view>
    <!-- 位置信息 -->
    <view class="flex items-center justify-between">
      <view class="flex gap-1">
        <view class="w-5 h-5">
          <image :src="LocationIcon" class="w-5 h-5 -mt-1" />
        </view>
        <view class="flex flex-col gap-1">
          <view
            class="text-sm font-bold text-gray-800 flex items-center gap-1 whitespace-nowrap"
            @click="selectArea"
          >
            {{ carInfo.runAreaName }}
            <wd-icon
              name="thin-arrow-left"
              size="10"
              custom-class="transform rotate-180 -mt-.4"
              v-if="referer === 'remote'"
            />
          </view>
          <view class="text-xs text-gray-500">距您{{ formatDecimal(carInfo.distance) }}</view>
        </view>
      </view>
      <image v-if="referer === 'remote'" :src="ScanIcon" class="w-7 h-7 -mt-1" @click="scan" />
      <view v-else class="flex flex-col justify-between">
        <view class="flex items-center justify-end whitespace-nowrap gap-2 text-sm">
          <text class="text-end">{{ carInfo.carName }}</text>
          <text class="text-end" :style="{ color: getDeviceStatus(carInfo.status)?.color }">
            {{ getDeviceStatus(carInfo.status)?.text }}
          </text>
        </view>
        <view
          class="flex items-center justify-center text-[#6d3202] whitespace-nowrap"
          @click="toOrderDetailPage"
        >
          <text class="ml-auto text-xs">{{ carInfo.orderStatus }}</text>
          <wd-icon
            v-if="carInfo.orderStatusCode === 'underProduction'"
            name="arrow-right"
            size="10"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import LocationIcon from '../imgs/location.png'
import ScanIcon from '../imgs/scan.png'
import { formatDecimal, getDeviceStatus } from '@/utils'
import useNativeLocation from '@/hooks/useNativeLocation'
import { useBaseInfoStore, useGlobalStore } from '@/store'
import { storeToRefs } from 'pinia'
import { scanCode } from '@/utils/scan'

const emit = defineEmits(['selectedArea'])
const globalStore = useGlobalStore()
const { openSetting } = useNativeLocation({ autoInitial: true, showModalWhenDenied: false })

const baseInfoStore = useBaseInfoStore()
const { carInfo, referer } = storeToRefs(baseInfoStore)

const scan = () => {
  scanCode(['scan', 'app'])
}

// 跳转区域选择页面
const selectArea = () => {
  // 只有线上购买的页面才可以进行区域切换
  if (referer.value !== 'remote') return
  uni.navigateTo({
    url: `/pages/select-area/index`,
    success: (opt) => {
      opt.eventChannel.on('selectedArea', () => {
        emit('selectedArea')
      })
    },
  })
}
// 跳转订单详情页面
const toOrderDetailPage = () => {
  uni.navigateTo({
    url: `/pages/order/details?orderId=${carInfo.value.orderDetailId}`,
  })
}
</script>
