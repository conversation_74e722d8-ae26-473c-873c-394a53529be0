<template>
  <wd-action-sheet v-model="show" title="已选商品">
    <view class="p-4 pt-0 max-h-[60vh] pb-24! overflow-y-auto">
      <!-- 购物车商品列表 -->
      <view
        v-for="goods in items"
        :key="goods.id"
        class="flex bg-white rounded-lg pb-3 items-center"
      >
        <GoodsItem :goods="goods" referrer="cart" class="w-full" @update:remove="onRemove" />
      </view>
    </view>
  </wd-action-sheet>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useCart } from '../../useCart'
import GoodsItem from '../goods-item/index.vue'

const show = ref(false)
const { items } = useCart()
// 删除最后一个商品时, 关闭弹窗
const onRemove = () => {
  const timer = setTimeout(() => {
    // 如果购物车为空, 则关闭弹窗
    if (items.value.length === 0) close()
    clearTimeout(timer)
  })
}

const open = () => {
  show.value = true
}
const close = () => {
  show.value = false
}

defineExpose({ open, close })
</script>

<style scoped>
.text-primary {
  color: #85310f;
}
</style>
