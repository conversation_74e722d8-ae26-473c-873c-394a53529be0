<template>
  <view
    class="fixed left-0 z-999 w-full flex bg-white items-center justify-between border-t border-[#eff1f5] border-t-solid"
    style="bottom: calc(env(safe-area-inset-bottom) + 94rpx)"
    v-if="totalCount"
  >
    <view class="flex items-center relative px-4 py-2" @click="handleCartClick">
      <wd-badge
        :model-value="totalCount"
        show-zero
        type="danger"
        :max="99"
        bg-color="#85310f"
        custom-class="h-5! items-center"
      >
        <wd-icon name="cart" size="24px" color="#333333" />
      </wd-badge>

      <!-- 总价显示 -->
      <view class="text-sm text-[#85310f] ml-4 mt-2">
        <text class="font-bold mr-.5">¥</text>
        <text class="text-lg font-bold">{{ computedPrice.payPrice }}</text>
        <text
          class="text-xs text-[#1a1a1a]"
          v-if="computedPrice.payPrice !== computedPrice.originalTotalPrice"
        >
          <text class="ml-1">
            <text v-if="computedPrice.couponId">券后</text>
            <text>已优惠¥{{ computedPrice.totalDiscountAmount }}</text>
          </text>
        </text>
      </view>
    </view>

    <!-- 结算按钮 -->
    <view class="px-4 py-2">
      <LAButton
        @click="handlePay"
        size="lg"
        :disabled="!items.length"
        custom-style="font-size:16px;font-weight:bold;"
      >
        去结算
      </LAButton>
    </view>
  </view>
  <goods-sheet ref="goodsSheetRef" />
</template>

<script setup lang="ts">
import { useCart } from '../../useCart'
import GoodsSheet from './goods-sheet.vue'

const { items, total, totalCount, computedPrice } = useCart()
const goodsSheetRef = ref()
const handleCartClick = () => {
  if (items.value.length === 0) return
  goodsSheetRef.value?.open()
}

const handlePay = () => {
  goodsSheetRef.value?.close()
  uni.navigateTo({ url: '/pages/goods-menu/payments/index' })
}
</script>

<style scoped>
.bg-primary {
  background-color: #85310f;
}
</style>
