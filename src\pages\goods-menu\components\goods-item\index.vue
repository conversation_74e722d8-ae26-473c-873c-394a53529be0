<template>
  <view class="flex w-full" style="--wot-button-primary-bg-color: #6d3202">
    <GoodsImage :src="goods.imagePath" />
    <view class="flex-1 ml-3">
      <view class="flex items-start justify-between mb-2">
        <view class="flex-1">
          <text class="block text-[15px] font-medium text-gray-800">
            {{ goods.productName }}
          </text>
          <text class="block text-xs text-gray-400 mt-1">
            {{
              referrer === 'cart'
                ? (goods as CartItem).$fullSpecNames.join(',')
                : goods.productRemark
            }}
          </text>
        </view>
      </view>
      <!-- 购物车商品促销标签， 会根据促销活动、优惠券等显示不同的标签 -->
      <view
        v-if="referrer === 'cart' && (goods as CartItem).promotionLabel"
        class="flex items-center justify-between rounded text-xs text-[#6D3202] w-fit border border-solid border-[#F1EBE6] p-.5 px-1 whitespace-nowrap"
      >
        <text>{{ (goods as CartItem).promotionLabel }}</text>
      </view>
      <!-- 菜单商品促销标签， 固定不变 -->
      <view
        v-else-if="promotion"
        class="flex items-center justify-between rounded text-xs text-[#6D3202] w-fit border border-solid border-[#F1EBE6] p-.5 px-1 whitespace-nowrap"
        :class="referrer === 'menu' ? 'mt-4' : 'mt-2'"
      >
        <text>{{ promotion?.labelContent }}</text>
      </view>

      <view class="flex justify-between items-center text-[#6D3202]">
        <!-- 购物车商品促销价, 会根据促销活动、优惠券等显示不同的价格 -->
        <view v-if="referrer === 'cart'">
          <text class="text-lg font-semibold">
            <text class="text-sm mr-.5">¥</text>
            <text>{{ (goods as CartItem).discountPrice }}</text>
          </text>
          <text
            v-if="(goods as CartItem).discountPrice !== goods.price"
            class="text-xs line-through text-gray-600 ml-1"
          >
            ¥{{ goods.price }}
          </text>
        </view>
        <!-- 菜单商品促销价, 固定不变 -->
        <view v-else>
          <text class="text-lg font-semibold">
            <text class="text-sm mr-.5">¥</text>
            <text>{{ promotion?.promotionalPrice || goods.price }}</text>
          </text>
          <text
            v-if="promotion && promotion?.promotionalPrice !== goods.price"
            class="text-xs line-through text-gray-600 ml-1"
          >
            ¥{{ goods.price }}
          </text>
        </view>
        <template v-if="referrer === 'menu'">
          <view
            v-if="(goods as GoodsItem).status === GoodsStatus.ON_SALE"
            class="w-6 h-6 flex items-center justify-center bg-[#6D3202] rounded-full ml-2"
          >
            <wd-icon name="add" size="14px" custom-class="text-white" />
          </view>
          <button
            v-else
            class="h-6 px-2 rounded-full text-xs flex items-center m-0 after:border-none"
            disabled
          >
            {{ (goods as GoodsItem).status === GoodsStatus.MAINTENANCE ? '维护中' : '已售罄' }}
          </button>
        </template>
        <template v-if="referrer === 'cart'">
          <view class="flex items-center justify-space">
            <slot name="cart-prefix"></slot>
            <view
              class="w-6 h-6 flex items-center justify-center rounded-full"
              @click.stop="cart.decreaseFromCart(goods as CartItem, beforeDecrease)"
            >
              <wd-icon name="minus-circle" size="24px" custom-class="text-[#6D3202]" />
            </view>
            <view class="w-8 text-center mr-1">{{ (goods as CartItem).$quantity }}</view>
            <view
              class="w-6 h-6 flex items-center justify-center bg-[#6D3202] rounded-full"
              @click.stop="cart.addToCart(goods as CartItem)"
            >
              <wd-icon name="add" size="14px" custom-class="text-white" />
            </view>
          </view>
        </template>
      </view>
    </view>
    <wd-message-box selector="delete-goods-message-box" custom-class="message-box-confirm" />
  </view>
</template>

<script lang="ts" setup>
// 商品 item 菜单 购物车 订单详情使用
import { GoodsStatus, GoodsItem, GoodsDetails, CartItem } from '../../types'
import GoodsImage from '@/components/GoodsImage.vue'
import { useCart } from '../../useCart'
import { useMessage } from 'wot-design-uni'
import Big from 'big.js'
import { computed } from 'vue'

const emit = defineEmits(['update:remove'])
const message = useMessage('delete-goods-message-box')

const beforeDecrease = () => {
  return message.confirm({ msg: '确认从购物车中删除此商品？', title: '确定删除？' }).then(() => {
    emit('update:remove')
    return true
  })
}

const props = defineProps<
  | { goods: GoodsItem; referrer: 'menu'; selectedCouponId?: string }
  | { goods: GoodsDetails; referrer: 'cart' | 'order'; selectedCouponId?: string }
>()

// 计算商品促销价
const promotion = computed(() => {
  const promotionalList = props.goods?.promotionalList
    ?.filter(
      (item) =>
        // 特价促销
        (item.promotionType === '1' && item.goodsId === props.goods.id) ||
        // 打折促销
        item.promotionType === '0' ||
        // 第N杯折扣, 第一杯折扣
        (item.promotionType === '2' && +item.businessParam === 1),
    )
    .map((item) => ({ ...item }))
  const price = props.goods.price
  const promotionalPriceList = [...promotionalList].map((item) => {
    switch (item.promotionType) {
      case '0':
        return Big(price).times(item.businessValue).times(0.1).toNumber()
      case '1':
        return item.promotionalPrice
      case '2':
        return Big(price).times(item.businessValue).times(0.1).toNumber()
      default:
        return price
    }
  })

  if (promotionalPriceList.length > 0) {
    const minPriceIndex = promotionalPriceList.indexOf(Math.min(...promotionalPriceList))
    const promotion = promotionalList[minPriceIndex]
    return {
      ...promotion,
      promotionalPrice:
        promotionalPriceList[minPriceIndex] < 0.01 ? 0.01 : promotionalPriceList[minPriceIndex],
    }
  }
  // 如果没有单杯活动，尝试获取多杯活动如果有的话则显示
  const promotions = props.goods?.promotionalList?.filter(
    (item) =>
      // 特价促销
      (item.promotionType === '1' && item.goodsId === props.goods.id) ||
      // 打折促销
      item.promotionType === '0' ||
      // 第N杯折扣, 第一杯折扣
      item.promotionType === '2',
  )
  const promotionLabel = promotions[0]?.labelContent
  return promotionLabel ? { labelContent: promotionLabel } : null
})

const cart = useCart(computed(() => props.selectedCouponId))
</script>
<style lang="scss">
.delete-goods-message-box {
  .wd-button {
    background-color: #6d3202;
  }
}
</style>
