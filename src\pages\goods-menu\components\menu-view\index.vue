<template>
  <view
    class="overflow-hidden flex bg-white"
    :class="
      activeCategory && activeCategory === categories.length - 1 ? 'last-category__active' : ''
    "
    :style="{
      height: tabbarHeight
        ? `calc(100% - ${tabbarHeight}px + env(safe-area-inset-bottom))`
        : 'calc(100% - env(safe-area-inset-bottom) - 80rpx)',
    }"
  >
    <!-- 左侧分类导航 -->
    <wd-sidebar v-model="activeCategory" @change="handleChange">
      <wd-sidebar-item
        v-for="(category, index) in categories"
        :key="index"
        :value="index"
        :label="category.typeName"
        custom-class="menu-sidebar-item before:hidden"
      />
    </wd-sidebar>

    <!-- 右侧商品列表 -->
    <scroll-view
      class="flex-1 bg-white"
      :show-scrollbar="false"
      scroll-y
      scroll-with-animation
      :throttle="false"
      :scroll-top="scrollTop"
      @scroll="onScroll"
    >
      <view
        v-for="(category, categoryIndex) in categories"
        :key="category.type"
        class="menu-category"
      >
        <view class="text-sm px-3 text-gray-800">
          {{ category.typeName }}
        </view>
        <view
          v-for="(goods, goodsIndex) in category.goodsMenuVOList"
          :key="goods.id"
          class="flex flex-col rounded-lg p-3 items-center"
          @click="handleGoodsClick(goods)"
        >
          <GoodsItem :goods="goods" referrer="menu" class="w-full" />
          <!-- 最后一个位置增加一个占位符，避免被购物车遮挡 -->
          <view
            v-if="
              goodsIndex === category.goodsMenuVOList.length - 1 &&
              categoryIndex === categories.length - 1
            "
            :class="items.length > 0 ? 'h-20 w-full' : 'h-10 w-full'"
          ></view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, getCurrentInstance } from 'vue'
import GoodsItem from '../goods-item/index.vue'
import type { GoodsItem as GoodsItemType } from '../../types'
import { GoodsStatus } from '@/pages/goods-menu/types'
import { fetchMenuListByCarId, fetchMenuListByAreaId } from '@/service/api'
import { getRect, isArray } from 'wot-design-uni/components/common/util'
import { useBaseInfoStore } from '@/store/baseInfo'
import { useGlobalStore } from '@/store'
import { storeToRefs } from 'pinia'
import { useCart } from '../../useCart'
import { useToast } from 'wot-design-uni'

const toast = useToast()
const { tabbarHeight } = storeToRefs(useGlobalStore())

const props = defineProps<{ carId?: string }>()
const { areaInfo, areaId } = storeToRefs(useBaseInfoStore())
const { items } = useCart()
interface Category {
  // 车辆id
  carId: string
  // 机器id
  machineId: string
  // 菜单名称
  typeName: string
  // 菜单
  type: string
  // 商品列表
  goodsMenuVOList: GoodsItemType[]
}

// 定义事件
const emit = defineEmits<{
  (e: 'select', goods: GoodsItemType): void
  (e: 'mounted'): void
}>()
// 切换菜单分类如果刚好卡在分类的位置，会造成菜单回弹的问题
const isScrollByClickCategroy = ref(false)
// 商品数据
const categories = ref<Category[]>([])
// 当前选中的分类
const activeCategory = ref(0)
const scrollTop = ref<number>(0)
const itemScrollTop = ref<number[]>([])
const instance = getCurrentInstance()

// 请求菜单列表 本机购扫码购会有carId 线上购买只有 areaId, refresh 为 true 时，代表页面加载过 onShow重新刷新数据
function fetchMenuList(refresh: boolean = false) {
  if (!props.carId && !areaInfo.value?.runAreaId) return
  const promise = props.carId
    ? fetchMenuListByCarId({ carId: props.carId })
    : fetchMenuListByAreaId({ areaId: areaInfo.value.runAreaId })
  promise.then((res: any) => {
    if (!res.success) {
      toast.warning(res.message)
      if (!refresh) emit('mounted')
      return
    }
    categories.value = res.data
    if (refresh) return
    // 暂无数据
    if (!categories.value.length) {
      emit('mounted')
      return
    }
    nextTick(() => {
      getRect('.menu-category', true, instance).then((rects) => {
        if (isArray(rects)) {
          itemScrollTop.value = rects.map((item) => item.top || 0)
          scrollTop.value = rects[activeCategory.value].top || 0
        }
        emit('mounted')
      })
    })
  })
}
// 当区域id或carid改变时需要重新获取商品菜单
watch(
  () => [props.carId, areaId.value],
  () => fetchMenuList(),
  { immediate: true },
)

// 处理商品点击
const handleGoodsClick = (goods: GoodsItemType) => {
  if (goods.status !== GoodsStatus.ON_SALE) {
    const tips = goods.status === GoodsStatus.MAINTENANCE ? '商品维护中' : '商品已售罄'
    return toast.warning(tips)
  }
  emit('select', goods)
}

function handleChange({ value }) {
  isScrollByClickCategroy.value = true
  activeCategory.value = value
  scrollTop.value = itemScrollTop.value[value]
}

function onScroll(e) {
  const { scrollTop } = e.detail
  const threshold = 50 // 下一个标题与顶部的距离
  if (scrollTop < threshold) {
    activeCategory.value = 0
    return
  }
  const index = itemScrollTop.value.findIndex(
    (top) => top > scrollTop && top - scrollTop <= threshold,
  )
  if (index > -1 && !isScrollByClickCategroy.value) activeCategory.value = index
  isScrollByClickCategroy.value = false
}

defineExpose({ fetchMenuList })
</script>
<script lang="ts">
export default { options: { styleIsolation: 'shared' } }
</script>
<style lang="scss" scoped>
:deep(.menu-sidebar-item) {
  --wot-sidebar-active-color: #6d3202;
  .wd-badge {
    white-space: nowrap;
    font-size: 28rpx;
  }
}

.last-category__active :deep(.wd-sidebar__padding) {
  border-top-right-radius: 16rpx;
}
</style>
