<template>
  <view class="flex-1 flex flex-col h-full justify-between overflow-hidden">
    <view class="flex flex-col h-full">
      <view class="relative p-4 pt-0 h-20" v-if="menuMounted">
        <button
          v-if="!userInfo?.userName"
          style="
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            z-index: 999;
            padding: 0;
          "
          open-type="getPhoneNumber"
          @getphonenumber="getUserPhone"
        ></button>
        <Swiper :list="swiperList" @click="handleSwiperClick" />
      </view>
      <MenuView
        class="flex-1 overflow-hidden"
        @select="handleGoodsSelect"
        :car-id="carId"
        ref="menuViewRef"
        @mounted="onMenuMounted"
      />
    </view>
    <CartView />
    <CouponPopup ref="couponPopupRef" />
  </view>
</template>

<script lang="ts" setup>
import Swiper from './swiper.vue'
import MenuView from './menu-view/index.vue'
import CartView from './cart-view/index.vue'
import CouponPopup from '@/components/CouponPopup.vue'
import { storeToRefs } from 'pinia'
import type { GoodsItem } from '../types'
import { getUserInfo, loginApi, fetchQueryAdvertises, fetchReceiveCoupon } from '@/service/api'
import DefaultAd from '../imgs/default-ad.png'
import { useBaseInfoStore } from '@/store/baseInfo'
import { useToast } from 'wot-design-uni'
import { getUrlObj, getLoginCode } from '@/utils'
import { useUserStore } from '@/store'
const { userInfo } = storeToRefs(useUserStore())

const { carId, areaId, referer } = storeToRefs(useBaseInfoStore())
const toast = useToast()
const emit = defineEmits<{ (e: 'mounted'): void }>()
const menuViewRef = ref()

const couponPopupRef = ref(null)

defineProps({
  menuMounted: { type: Boolean },
})
// 展示默认广告图片
const swiperList = ref([{ image: DefaultAd }])
fetchQueryAdvertises({
  scopId: referer.value === 'remote' ? areaId.value : carId.value,
  scopType: referer.value === 'remote' ? 'scop_area' : 'scop_car',
  taskMaterialType: 'appletHome',
}).then((res: any) => {
  const records = res.data
  if (!res.success || !Array.isArray(records) || records.length === 0) return
  // 先遍历records,再里面的dispatchAdMaterialVOList遍历把里面的每一项的jumpPath,fullPath取出来
  const adList = []
  records.forEach((record: any) => {
    record.dispatchAdMaterialVOList.forEach((item: any) => {
      adList.push({
        image: item.filePath,
        skipUrl: item.jumpPath,
        id: item.id,
        taskId: record.taskId,
        interval: Number(`${record.timeInterval}000`),
      })
    })
  })

  if (adList?.length) {
    swiperList.value = adList
  } else {
    swiperList.value = [{ image: DefaultAd }]
  }
})

// 菜单挂载
function onMenuMounted() {
  emit('mounted')
}

const tabBarUrl = ['/pages/index/index', '/pages/order/index', '/pages/my/index']

// 轮播图点击
function handleSwiperClick(item: any) {
  const isCoupon = item?.skipUrl?.includes('isCoupon')
  if (isCoupon) {
    const {
      query: { id, img },
    } = getUrlObj(item.skipUrl)

    fetchReceiveCoupon({ id, isBannerShow: true }).then((res) => {
      console.log('领取优惠券', res)
      if (res.data) {
        toast.info('您已经领取过了哦~')
      } else {
        couponPopupRef.value?.open({ id, img })
      }
    })
  } else {
    if (tabBarUrl.includes(item.skipUrl)) {
      uni.switchTab({ url: item.skipUrl })
    } else {
      uni.navigateTo({ url: item.skipUrl })
    }
  }
}

// 商品选择
function handleGoodsSelect(goods: GoodsItem) {
  uni.navigateTo({
    url: `/pages/goods-menu/details/index?productId=${goods?.id}`,
  })
}

const getUserPhone = async (e) => {
  const code = await getLoginCode()
  // 用户同意登录
  if (e.detail.errMsg === 'getPhoneNumber:ok') {
    const params = { loginCode: code, phoneCode: e.detail.code, loginType: 'normal' }
    // 获取用户token接口
    await loginApi(params).then((res: any) => {
      useUserStore().setToken(res.data.jwtToken as string)
    })
    const userInfo = await getUserInfo().then((res) => {
      return res.data
    })
    useUserStore().setUserInfo(userInfo)
  }
}

defineExpose({
  fetchMenuList: (refresh: boolean) => menuViewRef.value?.fetchMenuList?.(refresh),
})
</script>
