<template>
  <!-- 轮播组件 -->
  <wd-swiper :list="swiperList" :interval="currentInterval" :indicator="false" :autoplay="shouldAutoplay"
    v-model:current="current" @click="handleClick" height="80px" ref="swiperRef"></wd-swiper>

  <!-- 视频播放器（隐藏，仅用于控制播放） -->
  <video v-if="showVideoPlayer" :src="currentVideoUrl" autoplay @loadedmetadata="handleVideoLoaded"
    @ended="handleVideoEnded" style="position: absolute; width: 0; height: 0; opacity: 0"></video>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue'

interface SwiperItem {
  image: string
  interval?: number | string
  skipUrl?: string
  taskId: string

  [key: string]: any
}

const props = defineProps<{ list: SwiperItem[] }>()
const emit = defineEmits<{ (e: 'click', item: SwiperItem): void }>()

const current = ref<number>(0)
const swiperRef = ref<any>(null)
const currentInterval = ref<number>(3000) // 默认 3s 储存毫秒
const shouldAutoplay = ref<boolean>(true)
const showVideoPlayer = ref<boolean>(false)
const currentVideoUrl = ref<string>('')

// 动态生成轮播列表（仅提取 image 字段）
const swiperList = computed(() => props.list.map((item) => item.image))

// 判断当前项是否是视频
const isCurrentVideo = computed(() => {
  const currentItem = props.list[current.value]
  return currentItem?.image?.match(/\.(mp4|webm|ogg)$/i)
})

// 监听 current 变化，处理视频/图片逻辑
watch(
  current,
  (newIndex) => {
    const currentItem = props.list[newIndex]
    if (isCurrentVideo.value) {
      // 如果是视频，暂停自动轮播，并显示 video 播放器,获取视频源时长赋值
      shouldAutoplay.value = false
      currentVideoUrl.value = currentItem.image
      showVideoPlayer.value = true
    } else {
      // 如果是图片，恢复自动轮播，并隐藏 video 播放器
      shouldAutoplay.value = true
      currentInterval.value = Number(currentItem?.interval) || 3000
      showVideoPlayer.value = false
    }
  },
  { immediate: true },
)

// 视频加载完成，获取时长并设置 interval
const handleVideoLoaded = (event: Event) => {
  const video = event.target as HTMLVideoElement
  currentInterval.value = video.duration * 1000 // 转为毫秒
}

// 视频播放结束，切换到下一张
const handleVideoEnded = () => {
  if (swiperRef.value) {
    swiperRef.value.next()
  }
}

// 点击事件
function handleClick() {
  emit('click', props.list[current.value])
}
</script>
