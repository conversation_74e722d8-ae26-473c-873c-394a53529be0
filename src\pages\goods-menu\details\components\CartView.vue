<template>
  <view class="flex flex-col bg-white px-4 pt-0 gap-4 border-t border-[#eff1f5] border-t-solid">
    <view class="mt-2 flex justify-between">
      <view class="flex flex-col">
        <view class="text-base font-semibold text-[#6D3202]">
          <text class="text-sm mr-.5">¥</text>
          <text class="text-lg">{{ promotionalPrice || price }}</text>
          <text
            v-if="promotionalPrice && promotionalPrice !== price"
            class="text-xs font-semibold line-through text-gray-600 ml-1"
          >
            ¥{{ price }}
          </text>
        </view>
        <slot name="left" />
      </view>

      <view class="flex items-center justify-space">
        <view
          class="w-6 h-6 flex items-center justify-center rounded-full"
          @click.stop="decreaseQuantity"
        >
          <wd-icon
            name="minus-circle"
            size="24px"
            custom-class="text-[#6D3202]"
            :custom-style="quantity === 1 ? 'opacity: 0.5' : ''"
          />
        </view>
        <view class="w-8 text-center mr-1 text-[#656666]">{{ quantity }}</view>
        <view
          class="w-6 h-6 flex items-center justify-center bg-[#6D3202] rounded-full"
          @click.stop="increaseQuantity"
        >
          <wd-icon name="add" size="14px" custom-class="text-white" />
        </view>
      </view>
    </view>
    <view class="flex justify-between gap-1">
      <wd-button
        plain
        custom-class="border-[#6D3202]! text-[#6D3202]! w-full"
        size="large"
        @click="handleAddToCart"
      >
        加入购物车
      </wd-button>
      <wd-button custom-class="bg-[#6D3202]! w-full" size="large" @click="handlePay">
        立即下单
      </wd-button>
    </view>
    <wd-gap safe-area-bottom height="0"></wd-gap>
  </view>
</template>

<script lang="ts" setup>
import { useCart } from '../../useCart'
import type { GoodsDetails } from '../../types'
import { ref } from 'vue'

const { addToCart, backupCart, clearCart } = useCart()

const props = defineProps<{
  price: number
  goods: GoodsDetails
  specification: { temp: string; sweet: string; cup: string }
  fullSpecNames: string[]
  sugarData: Record<string, Record<string, Record<string, number>>>
  promotionalPrice: number
}>()
const quantity = ref(1)

const decreaseQuantity = () => {
  if (quantity.value > 1) {
    quantity.value--
  }
}
const increaseQuantity = () => {
  quantity.value++
}

const handleAddToCart = () => {
  addToCart(
    {
      ...props.goods,
      price: props.price,
      $quantity: quantity.value,
      $specification: props.specification,
      $fullSpecNames: props.fullSpecNames,
      $sugarData: props.sugarData,
    },
    false,
  )

  uni.navigateBack()
}
const handlePay = () => {
  backupCart()
  clearCart()

  addToCart(
    {
      ...props.goods,
      price: props.price,
      $quantity: quantity.value,
      $specification: props.specification,
      $fullSpecNames: props.fullSpecNames,
      $sugarData: props.sugarData,
    },
    false,
  )
  uni.navigateTo({ url: '/pages/goods-menu/payments/index' })
}
</script>
