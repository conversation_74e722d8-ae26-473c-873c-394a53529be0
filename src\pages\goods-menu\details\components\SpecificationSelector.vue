<template>
  <view
    class="px-4 bg-white"
    style="
      --wot-radio-button-height: 80rpx;
      --wot-radio-button-radius: 12rpx;
      --wot-radio-button-min-width: 180rpx;
      --wot-radio-large-label-fs: 28rpx !important;
    "
  >
    <!-- 杯型选择 -->
    <view class="mb-4 flex gap-4">
      <text class="font-bold text-sm whitespace-nowrap pt-2">杯型</text>
      <view class="flex gap-2">
        <wd-radio-group
          v-model="selectedCupValue"
          shape="button"
          checked-color="#6D3202"
          size="large"
        >
          <wd-radio
            v-for="cup in availableCupTypes"
            :key="cup.code"
            :value="cup.code"
            :disabled="isCupTypeDisabled(cup.code)"
            custom-class="text-gray-900 rounded text-sm! sku-radio"
          >
            {{ cup.name }}
          </wd-radio>
        </wd-radio-group>
      </view>
    </view>

    <!-- 温度选择 -->
    <view class="mb-4 flex gap-4">
      <text class="font-bold text-sm whitespace-nowrap pt-2">温度</text>
      <view class="flex gap-2">
        <wd-radio-group
          v-model="selectedTempValue"
          shape="button"
          checked-color="#6D3202"
          size="large"
        >
          <wd-radio
            v-for="temp in availableTemperatures"
            :key="temp.code"
            :value="temp.code"
            :disabled="isTemperatureDisabled(temp.code)"
            custom-class="text-gray-900 rounded text-sm sku-radio"
          >
            {{ temp.name }}
          </wd-radio>
        </wd-radio-group>
      </view>
    </view>

    <!-- 甜度选择 -->
    <view class="mb-4 flex gap-4">
      <text class="font-bold text-sm whitespace-nowrap pt-2">甜度</text>
      <view class="flex gap-2">
        <wd-radio-group
          v-model="selectedSweetValue"
          shape="button"
          size="large"
          checked-color="#6D3202"
        >
          <wd-radio
            v-for="sweet in sweetList"
            :key="sweet.formulaCode"
            :value="sweet.formulaCode"
            custom-class="text-gray-900 rounded text-sm sku-radio whitespace-nowrap mb-2"
          >
            {{ sweet.formulaName }}
          </wd-radio>
        </wd-radio-group>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import type { GoodsDetails } from '../../types'
import { ref, watch } from 'vue'

const props = defineProps<{
  cupPriceList: GoodsDetails['cupPriceList']
  sweetList: GoodsDetails['sweetList']
  selectedCup: string
  selectedTemp: string
  selectedSweet: string
  fullSpecNames: string[]
  isTemperatureDisabled: (tempCode: string) => boolean
  isCupTypeDisabled: (cupCode: string) => boolean
  availableTemperatures: { code: string; name: string }[]
  availableCupTypes: { code: string; name: string }[]
}>()

const emit = defineEmits<{
  (e: 'update:selectedCup', value: string): void
  (e: 'update:selectedTemp', value: string): void
  (e: 'update:selectedSweet', value: string): void
  (e: 'update:fullSpecNames', value: string[]): void
}>()

// 保持与父组件的双向绑定
const selectedCupValue = ref(props.selectedCup)
const selectedTempValue = ref(props.selectedTemp)
const selectedSweetValue = ref(props.selectedSweet)

// 监听内部状态变化，并向父组件提交更新
watch(selectedCupValue, (newValue) => {
  emit('update:selectedCup', newValue)
})

watch(selectedTempValue, (newValue) => {
  emit('update:selectedTemp', newValue)
})

watch(selectedSweetValue, (newValue) => {
  emit('update:selectedSweet', newValue)
})

// 监听父组件传递下来的props变化，更新内部状态
watch(
  () => props.selectedCup,
  (newValue) => {
    if (selectedCupValue.value !== newValue) {
      selectedCupValue.value = newValue
    }
  },
)

watch(
  () => props.selectedTemp,
  (newValue) => {
    if (selectedTempValue.value !== newValue) {
      selectedTempValue.value = newValue
    }
  },
)

watch(
  () => props.selectedSweet,
  (newValue) => {
    if (selectedSweetValue.value !== newValue) {
      selectedSweetValue.value = newValue
    }
  },
)
</script>
<script lang="ts">
export default { options: { styleIsolation: 'shared' } }
</script>
<style lang="scss" scoped>
:deep(.sku-radio) {
  .wd-radio__label {
    display: flex;
    font-size: 28rpx;
    align-items: center;
    justify-content: center;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }
}
</style>
