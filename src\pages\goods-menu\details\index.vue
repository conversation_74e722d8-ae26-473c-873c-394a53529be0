<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '商品详情',
  },
}
</route>
<template>
  <view class="flex flex-col h-full bg-white overflow-hidden">
    <template v-if="pageLoaded">
      <view class="flex-1 overflow-auto">
        <!-- 商品主图 -->
        <view class="w-full aspect-square">
          <image
            v-if="goodsDetail?.imagePath"
            :src="goodsDetail.imagePath"
            mode="aspectFill"
            class="w-full h-full"
          />
        </view>
        <view class="bg-white rounded-tl-xl rounded-tr-xl -mt-2 relative">
          <!-- 商品信息 -->
          <view class="p-4">
            <text class="text-lg font-bold">{{ goodsDetail?.productName }}</text>
            <text class="text-gray-500 text-xs mt-1 block">{{ goodsDetail?.productRemark }}</text>
          </view>
          <view class="px-4 mb-4 flex gap-1 items-center" v-if="promotion.length > 0">
            <view
              v-for="item of promotion"
              :key="item.labelContent"
              class="flex items-center justify-between rounded text-xs text-[#6D3202] w-fit border border-solid border-[#F1EBE6] p-.5 px-1"
            >
              <text>{{ item.labelContent }}</text>
            </view>
          </view>
          <!-- 规格选择 -->
          <SpecificationSelector
            v-if="goodsDetail"
            v-model:selectedCup="selectedCup"
            v-model:selectedTemp="selectedTemp"
            v-model:selectedSweet="selectedSweet"
            :full-spec-names="fullSpecNames"
            :cup-price-list="goodsDetail.cupPriceList"
            :sweet-list="goodsDetail.sweetList"
            :is-temperature-disabled="isTemperatureDisabled"
            :is-cup-type-disabled="isCupTypeDisabled"
            :available-temperatures="availableTemperatures"
            :available-cup-types="availableCupTypes"
          />
          <view class="bg-[#F5F6F8] h-2 w-full"></view>

          <!-- 原料信息 -->
          <view class="p-4 pt-3 bg-white">
            <text class="font-bold text-sm">详情</text>
            <view class="flex gap-2 text-sm py-3.5">
              <text class="text-gray-600">商品描述：</text>
              <text class="text-[#1A1A1A] flex-1">{{ goodsDetail?.productDescribe }}</text>
            </view>
            <text class="font-bold text-sm">原料</text>
            <view class="mt-2">
              <view
                class="flex text-sm text-[#1A1A1A] bg-[#F5F1EE] py-2 rounded-tl-lg rounded-tr-lg"
              >
                <text class="flex-1 text-center">原料名称</text>
                <text class="flex-1 text-center">品牌</text>
                <text class="flex-1 text-center">生产日期</text>
                <text class="flex-1 text-center">保质期</text>
              </view>
              <view
                v-for="item in goodsDetail?.baseList"
                :key="item.id"
                class="flex text-sm py-2 border-b border-gray-100"
              >
                <text class="flex-1 text-center text-gray-600 whitespace-nowrap">
                  {{ item.name }}
                </text>
                <text class="flex-1 text-center text-gray-600">{{ item.brand }}</text>
                <text class="flex-1 text-center text-gray-600">
                  {{ item.productionDate?.split(' ')[0] }}
                </text>
                <text class="flex-1 text-center text-gray-600">{{ item.expirationDate }}小时</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 购物车栏 -->
      <CartView
        :goods="goodsDetail"
        :price="selectedPrice"
        :promotional-price="selectedPromotion"
        :specification="selectedSpecification"
        :full-spec-names="fullSpecNames"
        :sugar-data="sugarMapData"
      >
        <template #left>
          <text class="text-xs text-[#1A1A1A] mt-1">{{ fullSpecNames.join(',') }}</text>
        </template>
      </CartView>
    </template>
  </view>
</template>

<script lang="ts" setup>
import { onLoad } from '@dcloudio/uni-app'
import CartView from './components/CartView.vue'
import SpecificationSelector from './components/SpecificationSelector.vue'
import { useCart } from '../useCart'
import { useGoodsDetail } from './useGoodsDetail'
import { fetchMenuDetailById } from '@/service/api'
import { ref } from 'vue'
import { useBaseInfoStore } from '@/store'

const pageLoaded = ref(false)
const baseInfoStore = useBaseInfoStore()

const { getGoodsDetails } = useCart()
const {
  goodsDetail,
  selectedTemp,
  selectedSweet,
  selectedCup,
  fullSpecNames,
  setGoodsDetail,
  selectedPrice,
  selectedPromotion,
  selectedSpecification,
  isTemperatureDisabled,
  isCupTypeDisabled,
  availableTemperatures,
  availableCupTypes,
  sugarMapData,
} = useGoodsDetail()

const promotion = computed(() => {
  const promotionalList = goodsDetail.value?.promotionalList
  if (!Array.isArray(promotionalList)) return []
  const seen = new Set()
  return promotionalList
    .filter((item) => {
      if (seen.has(item.promotionId)) {
        return false
      }
      seen.add(item.promotionId)
      return true
    })
    .map((item) => {
      return {
        labelContent: item.labelContent,
        promotionalPrice: item.promotionalPrice,
      }
    })
})

onLoad(async (options: { productId: string }) => {
  if (options.productId) {
    let detail = await getGoodsDetails(options.productId)?.then((res: any) => res?.data)
    if (!detail) {
      const res = (await fetchMenuDetailById(
        JSON.parse(
          JSON.stringify({
            productId: options.productId,
            carId: baseInfoStore.carId || undefined,
            areaId: baseInfoStore.areaId || undefined,
          }),
        ),
      )) as any
      detail = res.data
    }
    pageLoaded.value = true
    setGoodsDetail(detail)
  }
})
</script>
