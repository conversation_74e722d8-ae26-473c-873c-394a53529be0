import { ref, computed, watch } from 'vue'
import type { GoodsDetails } from '../types'
import Big from 'big.js'

export function useGoodsDetail() {
  const goodsDetail = ref<GoodsDetails>()
  const selectedTemp = ref('')
  const selectedSweet = ref('')
  const selectedCup = ref('')
  const fullSpecNames = ref<string[]>([])
  const sugarMapData = ref<Record<string, Record<string, Record<string, number>>>>({})

  // 所有杯型和温度的集合
  const cupTypes = computed(() => {
    if (!goodsDetail.value?.cupPriceList) return []

    const types = new Map<string, { code: string; name: string }>()
    goodsDetail.value.cupPriceList.forEach((item) => {
      if (!types.has(item.cupTypeCode)) {
        types.set(item.cupTypeCode, {
          code: item.cupTypeCode,
          name: item.cupTypeName,
        })
      }
    })
    return Array.from(types.values())
  })

  const temperatures = computed(() => {
    if (!goodsDetail.value?.cupPriceList) return []

    const temps = new Map<string, { code: string; name: string }>()
    goodsDetail.value.cupPriceList.forEach((item) => {
      if (!temps.has(item.temperature)) {
        temps.set(item.temperature, {
          code: item.temperature,
          name: item.temperatureName,
        })
      }
    })
    return Array.from(temps.values())
  })

  // 杯型和温度的可用组合关系
  const validCombinations = computed(() => {
    if (!goodsDetail.value?.cupPriceList) return new Map()

    const combinations = new Map<string, Set<string>>()
    goodsDetail.value.cupPriceList.forEach((item) => {
      if (!combinations.has(item.cupTypeCode)) {
        combinations.set(item.cupTypeCode, new Set())
      }
      combinations.get(item.cupTypeCode)?.add(item.temperature)
    })

    return combinations
  })

  // 是否可选
  const isTemperatureDisabled = (tempCode: string) => {
    if (!selectedCup.value) return false
    return !validCombinations.value.get(selectedCup.value)?.has(tempCode)
  }

  const isCupTypeDisabled = (cupCode: string) => {
    if (!selectedTemp.value) return false
    const validTemps = validCombinations.value.get(cupCode)
    return validTemps ? !validTemps.has(selectedTemp.value) : true
  }

  // 根据已选杯型，获取可用的温度选项
  const availableTemperatures = computed(() => {
    // 返回所有温度选项，禁用状态由isTemperatureDisabled控制
    return temperatures.value
  })

  // 根据已选温度，获取可用的杯型选项
  const availableCupTypes = computed(() => {
    // 返回所有杯型选项，禁用状态由isCupTypeDisabled控制
    return cupTypes.value
  })

  // 当选中项变化时，确保更新显示
  watch(selectedCup, () => {
    updateFullSpecNames()
  })

  watch(selectedTemp, () => {
    updateFullSpecNames()
  })

  watch(selectedSweet, () => {
    updateFullSpecNames()
  })

  // 更新完整的规格名称数组
  const updateFullSpecNames = () => {
    const names: string[] = []

    // 获取杯型名称
    if (selectedCup.value) {
      const cupType = cupTypes.value.find((cup) => cup.code === selectedCup.value)
      if (cupType) {
        names.push(cupType.name)
      }
    }

    // 获取温度名称
    if (selectedTemp.value) {
      const temperature = temperatures.value.find((temp) => temp.code === selectedTemp.value)
      if (temperature) {
        names.push(temperature.name)
      }
    }

    // 获取甜度名称
    if (selectedSweet.value && goodsDetail.value?.sweetList) {
      const sweet = goodsDetail.value.sweetList.find((s) => s.formulaCode === selectedSweet.value)
      if (sweet) {
        names.push(sweet.formulaName)
      }
    }

    fullSpecNames.value = names
  }

  // 检查当前选择是否有效
  const isCurrentSelectionValid = computed(() => {
    if (!selectedCup.value || !selectedTemp.value) return false

    const validTemps = validCombinations.value.get(selectedCup.value)
    if (!validTemps) return false

    return validTemps.has(selectedTemp.value)
  })

  // 获取当前选中的 SKU
  const selectedSku = computed(() => {
    // 只有当选择有效时才返回SKU
    if (!isCurrentSelectionValid.value || !goodsDetail.value?.cupPriceList) return null

    return goodsDetail.value.cupPriceList.find(
      (item) => item.cupTypeCode === selectedCup.value && item.temperature === selectedTemp.value,
    )
  })

  const selectedPrice = computed(() => {
    return selectedSku.value?.price || 0
  })

  const selectedPromotion = computed(() => {
    const promotionalList = goodsDetail.value?.promotionalList?.filter(
      (item) =>
        // 特价促销
        (item.promotionType === '1' && item.businessParam === selectedSku.value?.id) ||
        // 打折促销
        item.promotionType === '0' ||
        // 第N杯折扣, 第一杯折扣
        (item.promotionType === '2' && +item.businessParam === 1),
    )
    const price = selectedPrice.value
    const promotionalPriceList = promotionalList.map((item) => {
      switch (item.promotionType) {
        case '0':
          return Big(price).times(item.businessValue).times(0.1).toNumber()
        case '1':
          return item.promotionalPrice
        case '2':
          return Big(price).times(item.businessValue).times(0.1).toNumber()
        default:
          return price
      }
    })
    const minPrice = Math.min(...promotionalPriceList, selectedPrice.value)
    return minPrice < 0.01 ? 0.01 : minPrice
  })

  const selectedSpecification = computed(() => {
    return { temp: selectedTemp.value, sweet: selectedSweet.value, cup: selectedCup.value }
  })

  const setGoodsDetail = (detail: GoodsDetails) => {
    goodsDetail.value = detail

    // 重置选择状态
    selectedTemp.value = ''
    selectedSweet.value = ''
    selectedCup.value = ''

    // 默认选择第一个有效选项
    // 先选杯型
    if (detail.cupPriceList?.length) {
      selectedCup.value = detail.cupPriceList[0].cupTypeCode

      // 存储甜度数据
      detail.cupPriceList.forEach((item) => {
        if (!Array.isArray(item.productGoodsSkuAttrs)) return
        item.productGoodsSkuAttrs.forEach((attr) => {
          if (!Array.isArray(attr.productGoodsSkuSugarVOList)) return
          attr.productGoodsSkuSugarVOList.forEach((sugar) => {
            if (!sugarMapData.value[item.skuCode]) {
              sugarMapData.value[item.skuCode] = {}
            }
            if (!sugarMapData.value[item.skuCode][sugar.sugarType]) {
              sugarMapData.value[item.skuCode][sugar.sugarType] = {}
            }
            sugarMapData.value[item.skuCode][sugar.sugarType][attr.goodsFormulaId] =
              sugar.sugarPercent
          })
        })
      })

      // 选择杯型对应的可用温度
      const validTemps = detail.cupPriceList
        .filter((item) => item.cupTypeCode === selectedCup.value)
        .map((item) => item.temperature)

      if (validTemps.length) {
        selectedTemp.value = validTemps[0]
      }
    }

    // 选择甜度
    if (detail.sweetList?.length) {
      selectedSweet.value = detail.sweetList[0].formulaCode
    }

    // 更新规格名称
    updateFullSpecNames()
  }

  return {
    goodsDetail,
    selectedTemp,
    selectedSweet,
    selectedCup,
    fullSpecNames,
    selectedPrice,
    selectedPromotion,
    selectedSpecification,
    setGoodsDetail,
    cupTypes,
    temperatures,
    isTemperatureDisabled,
    isCupTypeDisabled,
    availableTemperatures,
    availableCupTypes,
    isCurrentSelectionValid,
    sugarMapData,
  }
}
