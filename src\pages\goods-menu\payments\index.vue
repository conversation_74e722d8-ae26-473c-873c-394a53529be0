<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '订单结算',
  },
}
</route>
<template>
  <view class="h-full overflow-hidden">
    <template v-if="!signExpired">
      <view class="flex flex-col justify-between h-full">
        <view
          class="flex flex-col p-2 box-border pb-0"
          style="--wot-color-white: transparent"
          v-if="referer === 'remote'"
        >
          <view class="flex flex-col bg-white rounded-lg">
            <wd-select-picker
              custom-class="w-full"
              label="选择取货时间"
              title="选择取货时间"
              v-model="pickTime"
              :columns="dateColumns"
              :show-confirm="false"
              align-right
              type="radio"
            />
            <view class="h-px mx-4 bg-gray-200"></view>
            <wd-cell title="选择取货点" :value="pickPointName" is-link @click="handlePointClick" />
          </view>
        </view>
        <view class="p-2 box-border" v-if="items.length">
          <view class="p-4 flex flex-col gap-4 rounded-lg bg-white">
            <GoodsItem
              v-for="item in items"
              :key="item.id"
              :goods="item"
              referrer="cart"
              :selected-coupon-id="selectedCouponId"
              @update:remove="handleRemove"
            >
              <template #cart-prefix>
                <view class="text-sm text-[#f25555] mr-2" v-if="payFailReason?.[item.id]">
                  <text>{{ payFailReason?.[item.id] }}</text>
                </view>
              </template>
            </GoodsItem>
          </view>
        </view>
        <view class="flex flex-col p-2 box-border py-0 mb-auto">
          <view class="flex flex-col bg-white rounded-lg overflow-hidden">
            <wd-cell
              v-if="computedPrice.activityDiscountAmount"
              title="商品直减"
              :value="`-¥${Math.abs(computedPrice.activityDiscountAmount)}`"
            />
            <wd-cell title="优惠券" is-link @click="selectCoupon">
              <view class="flex justify-end -mr-2 gap-1 text-[#6D3202] w-[60vw] mt-[3px]">
                <view
                  class="text-xs text-[#6D3202] border border-solid border-[#6D3202] rounded px-1"
                >
                  优惠券
                </view>
                <view class="text-sm">
                  {{
                    computedPrice.couponId
                      ? `-¥${Math.abs(computedPrice.couponPrice)}`
                      : '未选择优惠券'
                  }}
                </view>
              </view>
            </wd-cell>
            <view class="h-px mx-4 bg-gray-200"></view>
            <view class="px-4 text-sm text-[#85310f] flex items-center justify-end gap-5">
              <text
                v-if="computedPrice.payPrice !== computedPrice.originalTotalPrice"
                class="text-[#1a1a1a]"
              >
                <text v-if="computedPrice.couponId">券后</text>
                <text>已优惠</text>
                <text class="text-[#6D3202]">¥{{ computedPrice.totalDiscountAmount }}</text>
              </text>
              <view class="flex items-center py-2">
                <text class="text-[#1a1a1a] mr-1">应付:</text>
                <text class="font-bold">¥</text>
                <text class="text-lg font-bold">{{ computedPrice.payPrice }}</text>
              </view>
            </view>
          </view>
        </view>

        <view class="h-14 bg-white px-4 flex items-center">
          <view class="flex-1 inline-flex gap-2 items-center text-sm text-gray-900">
            <text class="mt-1">共{{ totalCount }}件</text>
            <view class="text-sm text-[#85310f]" v-if="total">
              <text class="font-bold">¥</text>
              <text class="text-lg font-medium ml-1">{{ computedPrice.payPrice }}</text>
              <text
                class="text-xs text-[#1a1a1a] ml-1"
                v-if="computedPrice.payPrice !== computedPrice.originalTotalPrice"
              >
                <text v-if="computedPrice.couponId">券后</text>
                <text>已优惠 ¥{{ computedPrice.totalDiscountAmount }}</text>
              </text>
            </view>
          </view>
          <button
            class="bg-[#6D3202] h-10 flex items-center justify-center text-white rounded-full font-bold px-6 text-base"
            @click="handlePay"
            :open-type="!userStore.token ? 'getPhoneNumber' : ''"
            @getphonenumber="getUserPhone"
          >
            去支付
          </button>
        </view>
        <wd-gap safe-area-bottom height="0"></wd-gap>
      </view>
      <SuccessPopup ref="successPopup" />
    </template>
    <view v-else class="flex flex-col items-center justify-center h-[80vh] gap-4">
      <wd-status-tip image="search" tip="扫码失败，请重新扫码" />
      <LAButton @click="handleScan" size="lg" custom-style="font-size:16px;font-weight:bold;">
        重新扫码
      </LAButton>
    </view>
  </view>
</template>
<script lang="ts" setup>
import SuccessPopup from './success-popup/index.vue'
import GoodsItem from '../components/goods-item/index.vue'
import {
  createOrder,
  loginApi,
  getUserInfo,
  getDeliveryPointByLocation,
  getOrderDetailBySign,
} from '@/service/api'
import { useUserStore } from '@/store/user'
import { getLoginCode, getUserPhoneCode } from '@/utils'
import { useCart } from '../useCart'
import { onLoad, onUnload } from '@dcloudio/uni-app'
import dayjs from 'dayjs'
import { storeToRefs } from 'pinia'
import { useToast } from 'wot-design-uni'
import { useBaseInfoStore } from '@/store'
import useNativeLocation from '@/hooks/useNativeLocation'
import { ORDER_TYPE } from '@/types/consumer'
import { scanCode } from '@/utils/scan'
import Big from 'big.js'

// const pointSelectRef = ref()

const baseInfoStore = useBaseInfoStore()
const { areaId, referer, areaInfo, carId, carInfo } = storeToRefs(baseInfoStore)
// 扫码支付来源传递的参数
const scanPayParams: any = {}
const { setReferer, reset } = baseInfoStore
const toast = useToast()
// 成功弹窗
const successPopup = ref()

// 用户自己选择的优惠券id
const selectedCouponId = ref<string>()
const { items, total, totalCount, restoreCart, clearCart, addToCart, computedPrice } =
  useCart(selectedCouponId)

// 初始化时间选择器数据
const dateColumns = ref(generateTimeSlots())
// 取货时间
const pickTime = ref(dateColumns.value[0].value)
// 取货时间
const pickupTime = ref('-')
// 时间刻度常量
const TIME_SLOT_MINUTES = 30
// 取货点
const pickPoint = ref()
// 取货点名称
const pickPointName = ref()
const userStore = useUserStore()
const { getRawLocation } = useNativeLocation()
// 保存调用支付之后返回的支付信息，避免重复创建 createOrder
const payInfo = ref<{ paySign?: any; orderId?: string; orderParams?: any; serialNum?: string }>({})
// 支付失败订单显示失败原因
const payFailReason = ref<Record<string, string>>({})
// 支付扫码 sign信息过期
const signExpired = ref(false)
// 生成当前时间的时间段
function generateTimeSlots() {
  const ALLOWED_DELAY_MINUTES = 20 // 允许延迟时间常量

  const now = dayjs()
  const slots = []
  const currentMinutes = now.minute()
  const currentHour = now.hour()

  // 计算当前时间所在的30分钟时间段
  const currentSlotStart = now.minute() < TIME_SLOT_MINUTES ? 0 : TIME_SLOT_MINUTES
  const startTime = now.hour(currentHour).minute(currentSlotStart)

  // 如果当前时间与时间段开始时间相差超过10分钟，则添加当前时间段
  if (Math.abs(currentMinutes - currentSlotStart) <= ALLOWED_DELAY_MINUTES) {
    slots.push({
      label: `${startTime.format('HH:mm')}-${startTime.add(TIME_SLOT_MINUTES, 'minute').format('HH:mm')}`,
      value: startTime.format('YYYY-MM-DD HH:mm'),
    })
  }

  // 往后推4小时，每半小时一个时间段
  let nextTime = startTime.add(TIME_SLOT_MINUTES, 'minute')
  const endTime = now.add(4, 'hour')

  while (nextTime.isBefore(endTime)) {
    slots.push({
      label: `${nextTime.format('HH:mm')}-${nextTime.add(TIME_SLOT_MINUTES, 'minute').format('HH:mm')}`,
      value: nextTime.format('YYYY-MM-DD HH:mm'),
    })
    nextTime = nextTime.add(TIME_SLOT_MINUTES, 'minute')
  }

  return slots
}

const selectCoupon = () => {
  if (!userStore.isLogined) {
    toast.show('请先登录')
    return
  }
  const couponId = computedPrice.value.couponId
  const { totalPrice, compensationTotalPrice, couponPrice } = computedPrice.value
  const price = Big(compensationTotalPrice || totalPrice)
    .plus(couponPrice || 0)
    .toNumber()
  uni.navigateTo({
    url: `/pages/my/MyPageButton/CouponSelect?couponId=${couponId}&price=${price}`,
    success: (opt) => {
      opt.eventChannel.on('select', (data) => {
        console.log('select coupon', data)
        selectedCouponId.value = data.couponId ?? null
      })
    },
  })
}

// 跳转选择取货点页面
const handlePointClick = () => {
  const params: Record<string, any> = {
    startDate: dayjs(pickTime.value).format('YYYY-MM-DD HH:mm'),
    endDate: dayjs(pickTime.value).add(TIME_SLOT_MINUTES, 'minute').format('YYYY-MM-DD HH:mm'),
    runAreaId: areaId.value,
  }
  uni.navigateTo({
    url: `/pages/goods-menu/payments/select-point/index?params=${JSON.stringify(params)}`,
    success: (opt) => {
      opt.eventChannel.on('select', (data) => {
        pickPoint.value = data.markerId
        pickPointName.value = data.markerName
      })
    },
  })
}
// 获取取货点数据
const getDeliveryPoint = async () => {
  const location = await getRawLocation()
  const params: Record<string, any> = {
    startDate: dayjs(pickTime.value).format('YYYY-MM-DD HH:mm'),
    endDate: dayjs(pickTime.value).add(TIME_SLOT_MINUTES, 'minute').format('YYYY-MM-DD HH:mm'),
    runAreaId: areaId.value,
  }
  if (location) {
    params.location = `${location.longitude} ${location.latitude}`
  }
  const { data } = await getDeliveryPointByLocation(params)
  if (!Array.isArray(data)) {
    toast.error('获取取货点失败, 请与工作人员联系')
    return
  }
  pointColumns.value = (data as any[]).map((item) => ({
    label: item.name,
    value: item.id,
    distance: item.distance,
    location: item.location,
  }))
}
// 获取用户手机号
const getUserPhone = async (e) => {
  const code = await getLoginCode()
  const phoneCode = await getUserPhoneCode(e)
  const params = { loginCode: code, phoneCode, loginType: 'normal' }
  // 获取用户token接口
  loginApi(params).then(async (response: any) => {
    const jwtToken = response?.data?.jwtToken
    if (jwtToken) {
      // 1. 存储token
      userStore.setToken(jwtToken)
      // 2. 获取用户信息
      const { data: userInfo } = (await getUserInfo()) as IResData<IUserInfo>
      // 登录成功，存储用户信息
      userStore.setUserInfo({
        ...userInfo,
        roleCode: 'customer',
      })
      // 调用支付
      handlePay()
    } else {
      toast.error(response.message || '登录失败')
    }
  })
}
// 删除商品
const handleRemove = () => {
  const timer = setTimeout(() => {
    if (items.value.length === 0) {
      uni.switchTab({ url: '/pages/index/index' })
    }
    clearTimeout(timer)
  })
}
// 监听取货时间, 取货时间改变时重新获取取货点数据
watch(
  pickTime,
  async () => {
    nextTick(async () => {
      // 线上支付订单才需要获取取货点数据
      if (referer.value === 'remote') {
        await getDeliveryPoint()
      }
    })
  },
  { immediate: true },
)

// 取货点
const pointColumns = ref([])
// 订单提交支付
const handlePay = async () => {
  // 兜底，如果商品为空，则不能支付
  if (!items.value.length) {
    toast.show('商品获取失败，请重新下单')
    return
  }
  // 如果用户没有token，则不能支付, 必须先进行登录
  if (!userStore.token) return
  // 线上下单需要的 参数
  let remoteParams = {}
  // 线上下单参数处理
  if (referer.value === 'remote') {
    const point = pointColumns.value.find((item) => item.value === pickPoint.value)

    if (!point) {
      toast.show('请先选择取货点')
      return
    }
    const { location, label } = point
    const [lon, lat] = location.split(' ')

    const pickupEndTime = dayjs(pickTime.value)
      .add(TIME_SLOT_MINUTES, 'minute')
      .format('YYYY-MM-DD HH:mm:ss')
    remoteParams = {
      pickupPoint: pickPoint.value,
      PickupStartTime: pickTime.value + ':00',
      PickupEndTime: pickupEndTime,
      deliveryAddr: label,
      lon,
      lat,
    }

    // 取货时间
    pickupTime.value = `${dayjs(pickTime.value).format('HH:mm')}-${dayjs(pickupEndTime).format('HH:mm')}`
    const [latestTime] = generateTimeSlots()
    // 在订单结算页面时间过久，导致设置的取货时间已经过期，则重新选择最新的时间，并刷新取货点，取货点如不存在则需要重新选择
    if (pickupTime.value !== latestTime.label && dayjs(pickTime.value) < dayjs(latestTime.value)) {
      // 初始化时间选择器数据
      dateColumns.value = generateTimeSlots()
      // 取货时间
      pickTime.value = dateColumns.value[0].value
      toast.show('取货时间已过期, 请重新选择')
      return
    }
  }
  const orderParams = {
    carId: referer.value === 'remote' ? '' : carId.value,
    carName: referer.value === 'remote' ? '' : carInfo.value.carName,
    areaId: areaId.value,
    areaName: areaInfo.value.runAreaName,
    ...scanPayParams,
    couponId: computedPrice.value.couponId,
    orderType: ORDER_TYPE[referer.value],
    goodsInfo: items.value.map((item) => {
      const {
        formulaId: sweetId,
        formulaName: sweetName,
        formulaCode,
      } = item.sweetList.find((i) => i.formulaCode === item.$specification.sweet)
      const {
        skuCode,
        cupTypeId,
        cupTypeName,
        temperatureId,
        temperatureName,
        id: skuId,
      } = item.cupPriceList.find(
        (i) =>
          i.cupTypeCode === item.$specification.cup && i.temperature === item.$specification.temp,
      )
      return {
        goodsId: item.id,
        number: item.$quantity,
        skuId,
        skuCode,
        pellets: [
          { type: 'size', pelletCode: cupTypeId, pelletValue: cupTypeName },
          { type: 'temperature', pelletCode: temperatureId, pelletValue: temperatureName },
          { type: 'sugar', pelletCode: sweetId, pelletValue: sweetName },
        ],
        sugar: formulaCode,
      }
    }),
    ...remoteParams,
  }
  // 如果支付信息不存在，或者支付信息中的订单参数与当前订单参数不一致，则重新创建订单
  if (!payInfo.value.paySign || payInfo.value.orderParams !== JSON.stringify(orderParams)) {
    // 调用支付 (扫码点餐参数)
    const { success, data, message } = await createOrder(orderParams)
    if (!success) {
      payFailReason.value = (data as any[])?.reduce((acc, curr) => {
        acc[curr.goodsId] = curr.oss
        return acc
      }, {})
      toast.show(message)
      return
    }

    const { paySign, orderId, serialNum } = data as any
    payInfo.value = { paySign, orderId, orderParams: JSON.stringify(orderParams), serialNum }
  }
  const { orderId, paySign, serialNum } = payInfo.value
  // 支付成功之后跳转的页面
  const goNextPage = (isCancel = false) => {
    // 没有拆单的情况 只会有一个订单
    if (orderId) {
      if (isCancel) {
        uni.redirectTo({ url: `/pages/order/unpaid-detail?sysOrderNum=${serialNum}` })
      } else {
        uni.redirectTo({ url: `/pages/order/details?orderId=${orderId}` })
      }
    } else {
      uni.switchTab({ url: `/pages/order/index` })
    }
  }

  uni.requestPayment({
    provider: 'wxpay',
    ...paySign,
    success() {
      // 启用订阅消息
      uni.requestSubscribeMessage({
        tmplIds: [
          'GctrMPqLo9aptHIdA7acGkeSpSd5zY9uvyW4KJJjHKM',
          '9rBQUMCs2dzgcGoHPV0QonnBPtbT4Vc-cr1iDvG0oao',
        ],
        complete: (res) => {
          console.log('订阅结果', res)
          clearCart()
          if (referer.value === 'remote') {
            successPopup.value.show(pickupTime.value, goNextPage)
          } else {
            goNextPage()
          }
        },
      })
    },
    fail() {
      toast.show('支付失败')
      setTimeout(() => {
        goNextPage(true)
      }, 1000)
    },
  })
}
// 本机购买跳转小程序付款时通过链接中的sign获取订单详情
const setOrderDetailBySign = async (sign: string) => {
  const { data } = await getOrderDetailBySign({ sign })
  try {
    const structureData = JSON.parse(data as string)
    if (typeof structureData !== 'object') {
      signExpired.value = true
      toast.show('已超时，请重新扫码支付')
      return
    }
    signExpired.value = false
    const { items, orderParams } = structureData
    Object.assign(scanPayParams, orderParams)
    items.forEach((item) =>
      addToCart(item, false, {
        areaId: orderParams.areaId,
        carId: orderParams.carId,
      }),
    )
  } catch (error) {
    signExpired.value = true
    toast.show('已超时，请重新扫码支付')
    console.log('error', error)
  }
}

// 重新扫码的逻辑
const handleScan = () => {
  scanCode(['app', 'scan'], { navigateToPaymentPage: false })
}

// 用户通过触摸屏下单直接使用小程序支付
onLoad((options) => {
  if (options.sign) {
    Object.assign(scanPayParams, { sign: options.sign })
    setReferer('app')
    setOrderDetailBySign(options.sign)
  }
})
// 离开页面时恢复购物车
onUnload(() => {
  // 本机购扫码支付时，离开页面时恢复到线上购买模式
  if (referer.value === 'app') {
    reset()
  }
  restoreCart()
})
</script>
<style lang="scss">
.point-picker-content {
  .wd-radio__label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
}
</style>
