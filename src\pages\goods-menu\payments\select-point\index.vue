<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '选择取货点',
  },
}
</route>
<template>
  <view>
    <view
      class="area-select rounded-tl-xl rounded-tr-xl bg-white py-2 fixed left-0 bottom-0 w-full z-10"
    >
      <!-- 列表数据 -->
      <view class="overflow-y-auto flex-1 px-4">
        <view
          v-for="item in pointColumns"
          :key="item.id"
          class="py-4 flex justify-between items-center text-sm border-b border-gray-200 border-b-solid"
          @click="handlePointChange(item.marker)"
        >
          <view class="text-sm text-gray-900">{{ item.label }}</view>
          <view class="text-gray-400">距您{{ formatDecimal(item?.distance) }}</view>
        </view>
        <wd-gap safe-area-bottom height="0"></wd-gap>
      </view>
    </view>
    <!-- 全屏地图 -->
    <map
      id="map"
      class="map"
      :longitude="centerLongitude"
      :latitude="centerLatitude"
      :scale="mapScale"
      :markers="markers"
      :show-location="true"
      :enable-3D="false"
      :show-compass="false"
      :enable-zoom="true"
      :enable-scroll="true"
      :enable-rotate="false"
      :enable-overlooking="false"
      :enable-satellite="false"
      :enable-traffic="false"
      @markertap="eventHandlers.onMarkerTap"
      @callouttap="eventHandlers.onCalloutTap"
      @controltap="handleControlTap"
    />
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { useMap } from '@/hooks/useMap'
import { useMapControls } from '@/hooks/useMapControls'
import { getDeliveryPointByLocation } from '@/service/api'
import useNativeLocation from '@/hooks/useNativeLocation'
import { formatDecimal } from '@/utils'

const { getRawLocation } = useNativeLocation()

// 初始化地图 hooks
const mapHooks = useMap()
// 事件通道，在onLoad中进行获取
let eventChannel: {
  on: (eventName: string, cb: (...args: any) => void) => void
  emit: (eventName: string, ...args: any) => void
}
// 解构地图相关数据和方法
const { centerLongitude, centerLatitude, mapScale, markers, createEventHandlers, init } = mapHooks

// 初始化控件 hooks
const controlsHooks = useMapControls(mapHooks)

// 解构控件相关数据和方法
const { handleControlTap } = controlsHooks
const handlePointChange = (marker) => {
  const { markerName, markerId } = marker
  eventChannel.emit('select', { markerName, markerId })
  uni.navigateBack()
}

// 创建事件处理器
const eventHandlers = createEventHandlers({
  onMarkerTap(event, marker) {
    handlePointChange(marker)
    console.log('onMarkerTap:', event, marker)
  },
  onCalloutTap(event, marker) {
    handlePointChange(marker)
    console.log('onCalloutTap::', event, marker)
  },
})

// 类型定义
export interface AreaListItem {
  id: string
  name: string
  distance: number
  boxCoordinate: string
}

export interface CarListItem {
  id: string
  name: string
  longitude?: string
  latitude?: string
  label?: string
  value?: string
}
const pointColumns = ref()

onLoad(async (options) => {
  init()
  const instance = getCurrentInstance().proxy
  // @ts-expect-error - eventChannel
  eventChannel = instance.getOpenerEventChannel()
  const params = JSON.parse(options.params)
  const location = await getRawLocation()
  if (location) {
    params.location = `${location.longitude} ${location.latitude}`
  }
  const { data } = await getDeliveryPointByLocation(params)
  pointColumns.value = (data as any[])
    .map((item, index) => {
      const [longitude, latitude] = item.location.toString().split(' ')
      return {
        label: item.name,
        value: item.id,
        distance: item.distance,
        longitude,
        latitude,
        marker: {
          longitude,
          latitude,
          id: index,
          markerId: item.id,
          markerName: item.name,
          callout: {
            content: item.name,
            color: '#1A1A1A',
            fontSize: 12,
            borderRadius: 4,
            bgColor: '#ffffff',
            padding: 8,
            display: 'ALWAYS',
          },
        },
      }
    })
    .filter((item) => item.longitude && item.latitude)
  markers.value.push(...pointColumns.value.map((item) => item.marker))
  console.log('markers.value', markers.value)
})
</script>

<style scoped>
.area-select {
  max-height: 60vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  position: fixed;
  bottom: 0;
}

.map {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1;
}
</style>
