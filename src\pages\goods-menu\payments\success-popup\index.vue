<template>
  <wd-popup v-model="show" custom-style="border-radius:32rpx;" :close-on-click-modal="false">
    <view
      class="flex flex-col w-[calc(100vw-64rpx)] box-border items-center bg-white rounded-xl p-10"
    >
      <!-- 成功图标 -->
      <view class="flex items-center justify-center gap-2 mb-10">
        <image :src="SuccessIcon" class="w-8 h-8" />
        <text class="text-2xl font-bold">支付成功</text>
      </view>

      <!-- 咖啡杯图标 -->
      <image :src="CupIcon" class="w-10 h-12" />

      <!-- 配送时间信息 -->
      <p class="mb-10 mt-4">
        <view class="text-center font-bold text-gray-900 mb-2 whitespace-nowrap">
          您的订单预计{{ deliveryTime }}送达
        </view>
        <view class="text-center text-sm text-gray-600">车辆到达后通知取货</view>
      </p>

      <!-- 查看订单按钮 -->
      <button
        class="py-3 px-6 bg-[#6d3202] text-white rounded-full h-12.5 w-32 flex items-center justify-center text-base"
        @click="toOrderPage"
      >
        查看订单
      </button>
    </view>
  </wd-popup>
</template>
+
<script setup lang="ts">
import { ref } from 'vue'
import CupIcon from './imgs/cup.png'
import SuccessIcon from './imgs/success.png'

const deliveryTime = ref('')
const show = ref(false)
let callback: () => void
const toOrderPage = () => {
  show.value = false
  callback?.()
}

defineExpose({
  show: (time: string, cb?: () => void) => {
    deliveryTime.value = time
    show.value = true
    callback = cb
  },
})
</script>
