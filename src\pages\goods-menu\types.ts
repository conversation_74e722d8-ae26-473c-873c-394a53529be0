// 商品状态
export enum GoodsStatus {
  // 已售罄
  SOLD_OUT = 1,
  // 维护中
  MAINTENANCE = 2,
  // 在售中
  ON_SALE = 0,
}

interface promotionalItem {
  promotionId: string
  promotionalPrice: number
  labelContent: string
  businessParam: string
  businessValue: number
  goodsId: string
  // 0 打折促销 1 特价促销 2 第N杯折扣
  promotionType: '0' | '1' | '2'
}

export interface GoodsItem {
  id: string
  // 商品名称
  productName: string
  // 原料名称
  baseName: string
  // 小料
  accessoryName: string
  // 甜度
  sweetName: string
  // 温度
  tempName: string
  // 菜单类型
  menuType: string
  // spu编码
  spuCode: string
  // sku编码
  skuCode: string
  // 容量
  capacity: string
  // 价格
  price: number
  // 商品图片
  imagePath: string
  // 商品描述
  productDescribe: string
  // 商品状态
  status: GoodsStatus
  /** 商品备注 */
  productRemark: string
  /** 营销活动相关 */
  promotionalList?: promotionalItem[]
}

export interface GoodsDetails {
  /** 商品id */
  id: string
  /** 商品名称 */
  productName: string
  /** 商品编码 */
  productCode: string
  /** spu编号 */
  spuCode: string
  /** 商品品牌 */
  brand: string
  /** 商品类别 */
  category: string
  /** 商品规格 */
  specification: string
  /** 商品价格 */
  price: number
  /** 商品图片 */
  imagePath: string
  /** 商品描述 */
  productDescribe: string
  /** 商品备注 */
  productRemark: string
  /** 分类id (表product_categories的id) */
  productCategoriesId: string
  /** 有效期（天） */
  expirationDate: number
  /** 商家id */
  merchantId: string
  /** 商家名称 */
  merchantName: string
  /** 创建人 */
  createAccount: string
  /** 创建时间 */
  createDate: string
  /** 修改人 */
  updateAccount: string
  /** 修改时间 */
  updateDate: string
  /** 辅料列表 */
  accessoryList: {
    /** 配方编码，若为温度和甜度，则从字典中取得，配料和辅料则通过原料表获取 */
    formulaCode: string
    /** 配方名称 */
    formulaName: string
    /** 配方类型，temperature:温度;sweet:甜度;base:基础原料;accessory:辅料 */
    formulaType: string
  }[]
  promotionalList?: promotionalItem[]
  /** 原料列表 */
  baseList: {
    /** 品牌 */
    brand: string
    /** 编码 */
    code: string
    /** 保质期 */
    expirationDate: number
    /** 保质期单位 */
    expirationDateUnit: string
    /** id */
    id: string
    /** 名称 */
    name: string
    /** 生产日期 */
    productionDate: string
  }[]
  /** 甜度列表 */
  sweetList: {
    /** 配方编码，若为温度和甜度，则从字典中取得，配料和辅料则通过原料表获取 */
    formulaCode: string
    /** 配方名称 */
    formulaName: string
    /** 配方类型，temperature:温度;sweet:甜度;base:基础原料;accessory:辅料 */
    formulaType: string
    formulaId: string
  }[]
  /** 温度列表 */
  tempList: {
    /** 配方编码，若为温度和甜度，则从字典中取得，配料和辅料则通过原料表获取 */
    formulaCode: string
    /** 配方名称 */
    formulaName: string
    /** 配方类型，temperature:温度;sweet:甜度;base:基础原料;accessory:辅料 */
    formulaType: string
  }[]
  /** 杯型列表 */
  cupPriceList: {
    /** 容量 */
    capacity: number
    /** 杯型id */
    cupTypeId: string
    /** 杯型编码 */
    cupTypeCode: string
    /** 杯型名称 */
    cupTypeName: string
    /** id */
    id: string
    /** 价格 */
    price: number
    /** 温度编码 */
    temperature: string
    /** 温度名称 */
    temperatureName: string
    /** 温度id */
    temperatureId: string
    /** SKU属性列表 */
    productGoodsSkuAttrs: {
      /** 商品配方ID */
      goodsFormulaId: string
      /** 损耗 */
      loss: number
      /** 单位 */
      unit: string
      /** 用量 */
      usage: number
      productGoodsSkuSugarVOList?: {
        // 对应糖比例
        sugarPercent: number
        // 加糖 正常糖
        sugarType: 'most' | 'normal'
        sugarTypeName: string
      }[]
    }[]
    /** 商品ID */
    productId: string
    /** SKU编码 */
    skuCode: string
    /** 库存 */
    stock: number
  }[]
}

export interface CartItem extends GoodsDetails {
  discountPrice?: number
  promotionLabel?: string
  $skuId?: string
  $quantity: number
  $specification: {
    temp: string
    sweet: string
    cup: string
  }
  $sugarData: Record<string, Record<string, Record<string, number>>>
  $fullSpecNames: string[]
}

export interface ComputedPrice {
  couponId?: string
  couponName?: string
  couponPrice?: number
  //  优惠后总价 包含优惠卷 优惠卷不包含 免费喝一杯
  totalPrice?: number
  // 优惠后总价 包含补偿卷(特指免费喝一杯)
  compensationTotalPrice?: number
  // 支付金额 合并计算后的价格，用户显示的最终支付金额
  payPrice?: number
  // 原总价
  originalTotalPrice?: number
  // 活动优惠金额 不包含优惠券
  activityDiscountAmount?: number
  // 总优惠金额 = 原总价 - 现总价
  totalDiscountAmount?: number
  goodsInfo: {
    goodsId: string
    lowestPrice: number
    compensationLowestPrice: number
    price: number
    number: number
    promotionId: string
    promotionLabel: string
    skuId: string
  }[]
}
