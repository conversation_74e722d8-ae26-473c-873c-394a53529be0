import { useCartStore } from '@/store/cart'
import { useBaseInfoStore } from '@/store'
import type { CartItem, GoodsDetails } from '@/pages/goods-menu/types'
import { computed, type Ref } from 'vue'
import { calculateLowestPrice } from '@/service/api'
import { storeToRefs } from 'pinia'

export function useCart(selectedCouponId?: Ref<string>) {
  const cartStore = useCartStore()
  const baseInfoStore = useBaseInfoStore()
  const { areaId, referer, carId } = storeToRefs(baseInfoStore)

  watch(selectedCouponId, (newVal) => {
    calculatePrice(newVal)
  })

  // increase 是否是数量+1操作
  const addToCart = (
    item: CartItem,
    increase: boolean = true,
    extraParams?: { areaId?: string; carId?: string },
  ) => {
    const $skuId = item.cupPriceList.find(
      (i) =>
        i.cupTypeCode === item.$specification.cup && i.temperature === item.$specification.temp,
    )?.id
    if (increase) {
      cartStore.addItem({ ...item, $quantity: 1, $skuId })
    } else {
      cartStore.addItem({ ...item, $skuId })
    }
    calculatePrice(selectedCouponId?.value, extraParams)
  }

  const decreaseFromCart = (item: CartItem, onLastItem?: () => Promise<boolean>) => {
    cartStore.decreaseItem(item, onLastItem).then(() => {
      calculatePrice(selectedCouponId?.value)
    })
  }

  const removeFromCart = (item: CartItem) => {
    cartStore.removeItem(item)
    calculatePrice(selectedCouponId?.value)
  }

  const clearCart = () => {
    cartStore.clearCart()
  }

  const total = computed(() => {
    return cartStore.total
  })

  const totalCount = computed(() => {
    return cartStore.totalCount
  })

  const setGoodsDetails = (goodsId: string, fetchDetail: Promise<GoodsDetails>) => {
    cartStore.setGoodsDetails(goodsId, fetchDetail)
  }

  const getGoodsDetails = (id: string | number) => {
    return cartStore.getGoodsDetails(id)
  }

  const items = computed(() => cartStore.items)

  const computedPrice = computed(() => cartStore.computedPrice)

  const calculatePrice = async (
    couponId?: string,
    extraParams?: { areaId?: string; carId?: string },
  ) => {
    const goodsInfo = items.value.map((item) => {
      const { id: skuId } = item.cupPriceList.find(
        (i) =>
          i.cupTypeCode === item.$specification.cup && i.temperature === item.$specification.temp,
      )
      return {
        goodsId: item.id,
        number: item.$quantity,
        skuId,
      }
    })

    // 没有商品信息，不计算价格
    if (!goodsInfo.length) return

    const res = await calculateLowestPrice({
      areaId: areaId.value,
      couponId: couponId || '',
      discountVoucherCalculation: couponId !== null,
      carId: referer.value === 'remote' ? '' : carId.value,
      ...extraParams,
      goodsInfo,
    })
    cartStore.setComputedPrice(res.data as never)
    return res.data
  }

  return {
    items,
    computedPrice,
    total,
    totalCount,
    addToCart,
    decreaseFromCart,
    removeFromCart,
    clearCart,
    backupCart: cartStore.backupCart,
    restoreCart: cartStore.restoreCart,
    setGoodsDetails,
    getGoodsDetails,
  }
}
