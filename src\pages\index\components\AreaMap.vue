<template>
  <view>
    <!--    <web-view :src="webviewSrc"></web-view>-->
    <!-- 显示当前订单 -->
    <view class="p-2 flex flex-col gap-2 fixed top-0 left-0 z-100 w-full box-border">
      <wd-cell
        v-for="item of orders"
        :key="item.title"
        :title="item.title"
        is-link
        custom-class="rounded-xl py-2 shadow-lg"
        custom-title-class="font-bold text-lg -mt-1 whitespace-nowrap"
        custom-label-class="text-[#1A1A1A]! text-sm!"
        :style="{ '--wot-cell-arrow-color': 'rgba(249, 116, 75, 1)' }"
        @click="toOrderTab"
      >
        <template #label>
          <view class="mt-1">
            <text class="text-[#1A1A1A] text-sm!">距你{{ formatDecimal(item?.distance) }}</text>
            <text class="text-[var(--wot-cell-arrow-color)] text-sm! ml-2">
              预计{{ item.time }}秒
            </text>
          </view>
        </template>
        <view class="inline-flex gap-2 justify-between -mr-2 items-center">
          <text style="color: var(--wot-cell-arrow-color)">查看订单</text>
        </view>
      </wd-cell>
    </view>
  </view>
</template>

<script lang="ts" setup>
/**
 * @file 地图
 * <AUTHOR>
 * @date 2025/2/26
 */

import { useTianDiMapWebsocket } from '@/hooks/useTianDiMapWebsocket'
import { formatDecimal } from '@/utils'
import { getMsgList, getNearbyVehicleList, getDeliveryPointList } from '@/service/api'
import { useUserStore } from '@/store'
import { onUnload } from '@dcloudio/uni-app'

const randomCode = Math.random().toString(36).substring(2, 15) + new Date().getTime()
const webviewSrc = import.meta.env.VITE_TIANDITU_WEBVIEW_BASEURL + `?code=${randomCode}`

const userStore = useUserStore()
const { setPickupPointsVisibility, setVehiclePointsVisibility } = useTianDiMapWebsocket({
  code: randomCode,
  url: import.meta.env.VITE_WEBSOCKET_BASEURL + `?code=${randomCode}`,
  onmessage: (message) => {
    switch (message.type) {
      case 'SELECT_PICKUP_POINT':
        console.log('【小程序xxxx接收消息】:', message.data)
        uni.navigateTo({ url: '/pages/sell-car/index?id=' + message.data.id })
        break
    }
  },
})

// 测试随机更新当前坐标
/* setInterval(() => {
  // 更新位置
  const random = Math.random() * 0.0001
  setSelfLocation({ latitude: 39.915 + random, longitude: 116.404 + random })
}, 500) */

// 当前订单
const orders = ref([])

// 获取消息提示
const fetchNotification = () => {
  // 消费者没登录不需要轮询获取订单消息
  if (!userStore.token) return
  getMsgList({ isTitle: '1' }).then((res) => {
    console.log(res)
  })
}

// 获取车辆点位列表
const fetchVehiclePointList = (location: { lat: number; lon: number }) => {
  getNearbyVehicleList(location).then((res) => {
    if (Array.isArray(res.data)) {
      setVehiclePointsVisibility(res.data, true)
    }
  })
}

// 获取配送点位列表
const fetchDeliveryPointList = (location: { lat: number; lon: number }) => {
  getDeliveryPointList(location).then((res) => {
    if (Array.isArray(res.data)) {
      setPickupPointsVisibility(res.data, true)
    }
  })
}

fetchNotification()
const location = ref({ lat: 39.9, lon: 116.3 })
fetchVehiclePointList(location.value)
fetchDeliveryPointList(location.value)
// 轮询获取消息
const notificationInterval = setInterval(fetchNotification, 1000 * 30)
// 轮询获取车辆和配送点
const vehiclePointInterval = setInterval(() => {
  fetchVehiclePointList(location.value)
}, 1000 * 10)

onUnload(() => {
  clearInterval(notificationInterval)
  clearInterval(vehiclePointInterval)
})

// 跳转订单tab
const toOrderTab = () => {
  uni.switchTab({
    url: '/pages/order/index',
  })
}
</script>

<style scoped></style>
