<template>
  <view class="area-select">
    <wd-search placeholder="搜索运营区域" hide-cancel placeholder-left @change="handleSearch" />
    <!-- 列表数据 -->
    <view class="area-container">
      <view
        v-for="item in filteredList"
        :key="item.id"
        class="area-list"
        @click="goToProductPage(item)"
      >
        <view>{{ item.name }}</view>
        <view class="text-gray">据您{{ item.range || '-' }}m</view>
      </view>
      <wd-gap safe-area-bottom height="0"></wd-gap>
    </view>
  </view>
</template>

<script setup lang="ts">
/**
 * @file 区域搜索组件
 * <AUTHOR>
 * @date 2025/4/3
 */

const props = defineProps({
  list: {
    type: Array as PropType<ListItem[]>,
    default: () => [
      {
        id: 1,
        name: '武侯区天相关大楼',
        range: 5,
      },
      {
        id: 1,
        name: '武侯区天相关大楼',
        range: 5,
      },
      {
        id: 1,
        name: '武侯区天相关大楼',
        range: 5,
      },
      {
        id: 1,
        name: '武侯区天相关大楼',
        range: 5,
      },
      {
        id: 1,
        name: '武侯区天相关大楼',
        range: 5,
      },
      {
        id: 1,
        name: '武侯区天相关大楼',
        range: 5,
      },
      {
        id: 2,
        name: '武侯区天mom大楼',
        range: 100,
      },
    ],
  },
})
const searchKeyword = ref<string>('')
// 过滤后的列表
const filteredList = computed(() => {
  return props.list.filter((item) =>
    item.name.toLowerCase().includes(searchKeyword.value.toLowerCase()),
  )
})
const goToProductPage = (data) => {
  // 跳转到商品页面
  uni.navigateTo({
    url: `/pages/goods-menu/index?areaData=${encodeURIComponent(JSON.stringify(data))}`,
  })
}
// 处理搜索事件（假设wd-search返回事件对象，值在e.detail中）
const handleSearch = (e) => {
  searchKeyword.value = e.value
}
</script>
<!--wot组件库的样式穿透-->
<script lang="ts">
// 类型定义
export interface ListItem {
  id: number
  name: string
  range: number // 统一为数字类型
}
export default {
  options: {
    styleIsolation: 'shared',
  },
}
</script>
<style scoped lang="scss">
.area-select {
  height: 40vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  position: fixed;
  bottom: 0;
}
.area-container {
  background: #fff;
  overflow-y: auto;
  flex: 1;
}
.area-list {
  border-bottom: 1px solid rgba(171, 168, 168, 0.4);
  @apply flex justify-between items-center h-[100rpx];
}
</style>
