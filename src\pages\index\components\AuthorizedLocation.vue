<template>
  <view class="h-screen flex justify-center items-center flex-col gap-5">
    <view class="flex flex-col items-center gap-2 -mb-1">
      <image :src="LocationIcon" class="w-10 h-10" />
      <view class="text-sm text-gray-800">请先授权当前定位</view>
    </view>
    <LAButton @click="openSetting" size="xl" custom-style="width:160px" type="primary">
      开启位置权限
    </LAButton>
    <LAButton @click="toSelectArea" size="xl" custom-style="width:160px" plain>
      选择运营区域
    </LAButton>
  </view>
</template>

<script setup lang="ts">
/**
 * @file 首页-位置权限组件
 * <AUTHOR>
 * @date 2025/4/3
 */
import LocationIcon from '../imgs/requestLocation.png'
import useNativeLocation from '@/hooks/useNativeLocation'
const { openSetting } = useNativeLocation({ showModalWhenDenied: false, autoInitial: false })

// 运营区域跳转
const toSelectArea = () => {
  uni.navigateTo({ url: '/pages/select-area/index' })
}
</script>
<!--wot组件库的样式穿透-->
<script lang="ts">
export default {
  options: {
    styleIsolation: 'shared',
  },
}
</script>
<style scoped>
:deep(.btn) {
  width: 350rpx;
  height: 100rpx;
  background: #6d3202 !important;
  border-radius: 25px;
}
</style>
