<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '首页',
  },
}
</route>
<template>
  <view class="flex flex-col justify-center h-screen bg-white relative" v-if="!isMaintenance">
    <view class="flex-1 h-full overflow-hidden">
      <BaseInfo v-if="menuMounted" @selectedArea="menuMounted = false" />
      <Menu
        v-if="(carId && carInfoLoaded) || areaInfoLoaded"
        :menu-mounted="menuMounted"
        @mounted="menuMounted = true"
        ref="menuRef"
      />
    </view>

    <!-- 非扫码购买进来的 没有授权的需要显示授权页面 授权获取当前位置 -->
    <AuthorizedLocation v-if="permission === 'unperssion' && !carId" />
    <TabBar v-model="activeTab" />
  </view>
</template>

<script lang="tsx" setup>
/**
 * @file tab首页
 * <AUTHOR>
 * @date 2025/2/26
 */

// 每次进入这个页面刷新
import TabBar from '@/components/TabBar.vue'
import AuthorizedLocation from '@/pages/index/components/AuthorizedLocation.vue'
import BaseInfo from '../goods-menu/components/BaseInfo.vue'
import Menu from '../goods-menu/components/menu.vue'
import useNativeLocation from '@/hooks/useNativeLocation'
import { onLoad } from '@dcloudio/uni-app'
import { useBaseInfoStore } from '@/store/baseInfo'
import { storeToRefs } from 'pinia'
import { useGlobalStore, useUserStore } from '@/store'
import { verifySign } from '@/utils/crypto'
import { useToast } from 'wot-design-uni'
import { refreshCarWaitTime } from '@/service/api'
import { useCart } from '@/pages/goods-menu/useCart'

const toast = useToast()
const { clearCart } = useCart()
const baseInfoStore = useBaseInfoStore()
const menuRef = ref()
const {
  setCarId,
  setReferer,
  fetchAreaInfoByLocation,
  fetchCarInfo,
  setScanParams,
  updateOrderStatus,
} = baseInfoStore
const { carId, areaId, carInfoLoaded, areaInfoLoaded, referer } = storeToRefs(baseInfoStore)
const globalStore = useGlobalStore()
const { permission } = storeToRefs(globalStore)
const userStore = useUserStore()
const { isMaintenance } = storeToRefs(userStore)
const { getRawLocation } = useNativeLocation({ autoInitial: false, showModalWhenDenied: false })
let interval = null
const isLoading = ref(false)

// 菜单是否已经加载完成,加载完成后才能显示其他元素,不然会导致菜单左侧sidebar 定位错误
const menuMounted = ref(false)

const activeTab = ref('home')

// 扫码进入页面参数
interface ScanOptions {
  c?: string
  s?: string
  t?: number
}

// 通过选择区域进入首页参数
interface SelectAreaOptions {
  selectedArea?: string
}

// 处理扫码进入页面参数， 删除之前保留的配置 重新通过 menu组件 获取车辆相关定位以及车辆菜单
function handleScanOptions(options: ScanOptions) {
  const { c, s, t } = options
  const { ok, msg } = verifySign(s, c, +t)
  // 验证签名
  if (!ok) {
    toast.error(msg)
    return
  }
  refreshCarWaitTime({ carId: c, timestamp: t, isLocal: 0 })
  clearCart()
  baseInfoStore.reset()
  menuMounted.value = false
  isLoading.value = true
  setCarId(c)
  setReferer('scan')
  setScanParams({ carId: c, timestamp: t })
  // 存在carId 则获取车辆详情接口
  getRawLocation().then((location) => {
    fetchCarInfo(location).then(() => {
      isLoading.value = false
    })
  })
}

onShow(() => {
  // 扫码传车辆id,线上小程序传区域id

  // 对应首页扫码支付下单进入小程序，从订单结算页面时会清空保存的信息，返回到首页（线上购模式）会导致 areaid 和 carid 为空，导致页面无法正常显示, 通过重新启动小程序来解决
  if (!areaInfoLoaded.value && !carInfoLoaded.value && !isLoading.value) {
    console.log('onShow relaunch')

    uni.reLaunch({ url: '/pages/index/index' })
  }
  if (interval) {
    clearInterval(interval)
  }
  // 现场购进入小程序，每5秒更新一次订单状态
  if (referer.value === 'scan') {
    interval = setInterval(updateOrderStatus, 1000 * 5)
  }
  // 重新打开页面时，刷新菜单列表数据
  if (menuRef.value) {
    menuRef.value?.fetchMenuList?.(true)
  }
})

// 切换模式时，清空购物车
watch(referer, () => {
  clearCart()
})

onHide(() => {
  if (interval) {
    clearInterval(interval)
  }
})

// 用户通过微信扫码进入页面，会通过url传入carId
onLoad((options: ScanOptions | SelectAreaOptions) => {
  isLoading.value = true
  // 初始化重置tabbar高度
  globalStore.setTabbarHeight(0)
  // 运维人员打开小程序也会运行首页，避免运维人员打开小程序时额外请求
  if (isMaintenance.value) return
  if ((options as ScanOptions).c) {
    handleScanOptions(options as ScanOptions)
  } else {
    menuMounted.value = false
    // setReferer('remote')
    // 如果从选择区域页面进入，则不获取位置, 重新设置区域
    if (!(options as SelectAreaOptions).selectedArea) {
      getRawLocation().then((location) => {
        fetchAreaInfoByLocation(location).then(() => {
          isLoading.value = false
        })
      })
    } else {
      isLoading.value = false
    }
  }
})
</script>
