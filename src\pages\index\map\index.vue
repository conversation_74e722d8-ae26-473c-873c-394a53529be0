<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '切换运营区域',
  },
}
</route>
<template>
  <view>
    <!--地图-->
    <AreaMap />
    <!--选择区域-->
    <AreaSearch />
  </view>
</template>

<script lang="tsx" setup>
/**
 * @file tab首页
 * <AUTHOR>
 * @date 2025/4/3
 */

import AreaSearch from '@/pages/index/components/AreaSearch.vue'
import AreaMap from '@/pages/index/components/AreaMap.vue'
</script>

<style scoped></style>
