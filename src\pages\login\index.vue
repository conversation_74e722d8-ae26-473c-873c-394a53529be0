<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '运维端登录',
  },
}
</route>
<template>
  <view class="login-background">
    <image :src="logo" class="h-40 ma logo-img" mode="aspectFit" />
    <view class="grow-8 bg-white rounded-4" style="padding: 20px">
      <view class="gap-5 grid">
        <view class="fw-bold text-xl">欢迎登录</view>
        <wd-input
          v-model="loginForm.phone"
          clearable
          no-border
          placeholder="请输入手机号"
        ></wd-input>
        <view class="form-code">
          <wd-input
            no-border
            v-model="loginForm.verificationCode"
            placeholder="请输入验证码"
          ></wd-input>
          <LAButton
            :disabled="countdown > 0"
            @click="handleCaptchaAction"
            pale
            isOps
            :round="false"
            size="xl"
            :type="countdown > 0 ? 'info' : 'primary'"
            custom-style="width:102px"
          >
            {{ countdown > 0 ? `${countdown}s` : '获取验证码' }}
          </LAButton>
        </view>
        <LAButton isOps block :round="false" size="xl" @click="login">立即登录</LAButton>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
/**
 * @file 登录
 * <AUTHOR>
 * @date 2024/12/25
 */
import logo from './imgs/logo.svg?url'
import { useUserStore } from '@/store'
import { loginApi, getUserInfo, sendVerificationCode } from '@/service/api'
import { useToast } from 'wot-design-uni'
const userStore = useUserStore()
const loginForm = ref({ phone: '', verificationCode: '' })
const toast = useToast()

// 倒计时状态
const countdown = ref(0)
let timer: any = null
// 登录
const login = async () => {
  useUserStore().setUserInfo({ roleCode: 'maintenance' })

  try {
    if (!loginForm.value.phone || !loginForm.value.verificationCode) {
      return toast.warning('请输入账号和验证码')
    }
    userStore.clearUserInfo()
    await uni.showLoading({ title: '登录中' })
    const response: any = await loginApi({
      loginType: 'maintenance',
      phone: loginForm.value.phone,
      verificationCode: loginForm.value.verificationCode,
    })

    const jwtToken = response?.data?.jwtToken
    if (jwtToken) {
      // 1. 存储token
      userStore.setToken(jwtToken)
      // 2. 获取用户信息
      const { data: userInfo } = (await getUserInfo()) as IResData<IUserInfo>
      // 登录成功，存储用户信息跳转（商家/运维）首页
      userStore.setUserInfo({
        ...userInfo,
        roleCode: 'maintenance',
      })
      // 根据用户选择跳转对应的url
      const url = '/pages-maintenance/index/index'
      uni.reLaunch({ url })
    } else {
      // 登录失败处理
      toast.error(response.message || '登录失败')
    }
  } finally {
    // 无论成功还是失败，都要关闭加载提示
    uni.hideLoading()
  }
}
// 开始倒计时的功能
function startCountdown(seconds: number) {
  countdown.value = seconds

  // 每秒减少计时
  timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer) // 倒计时结束，清除定时器
    }
  }, 1000)
}
// 获取验证码方法
async function handleCaptchaAction() {
  console.log('handleCaptchaAction', countdown.value)
  if (countdown.value > 0) return // 如果还有倒计时，则不再点击
  const phone = loginForm.value.phone
  if (!phone) {
    return toast.warning('请输入手机号')
  }
  if (!/^1[3-9]\d{9}$/.test(phone)) {
    return toast.warning('提示请输入正确的手机号信息')
  }

  await sendVerificationCode({ loginType: 'maintenance', phone }).then((res) => {
    if (!res.success) {
      return toast.show(res.message)
    }
    startCountdown(60) // 开始60秒倒计时
    toast.success('验证码发送成功')
  })
}
// 页面销毁清除定时
onUnload(() => {
  clearInterval(timer)
})
</script>
<script lang="ts">
export default {
  options: {
    styleIsolation: 'shared',
  },
}
</script>
<style lang="scss" scoped>
:deep(.wd-input) {
  @apply h-[48px] flex flex-col px-4 rounded-3;
}
:deep(.wd-input.wd-input) {
  background-color: #f8f9fc;
}
:deep(.wd-input__value) {
  height: 100%;
}

.logo-img {
  flex-grow: 1;
  width: 60%;
  //不因为父元素的flex改变宽度
  object-fit: contain;
}

.login-background {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 10px;
  display: flex;
  justify-content: end;
  flex-direction: column;
  background: linear-gradient(180deg, #356afd 0%, #356afd 10%, #ffffff 70%);
}
:deep(.form-code) {
  display: grid;
  /* 定义两列，第一列占三分之二，第二列占三分之一 */
  grid-template-columns: 2fr 1fr;
  gap: 4px;
  .is-disabled {
    opacity: 1 !important;
  }
}

:deep(.wd-icon.wd-icon) {
  background-color: var(--page-bg-color);
}
</style>
