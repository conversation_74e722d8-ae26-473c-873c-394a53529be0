<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '登录',
  },
}
</route>
<template>
  <wd-form ref="form" :model="resetForm" :rules="rules" class="login-background" errorType="toast">
    <img alt="" class="login-logo" src="./imgs/logo.png" />
    <view class="grow-8 bg-white rounded-4" style="padding: 20px">
      <view class="gap-5 grid">
        <view class="fw-bold text-lg">请设置新密码</view>
        <wd-input
          v-model="resetForm.newPassword"
          class="h-[50px] flex flex-col px-4 !rounded-2"
          clearable
          no-border
          placeholder="8~20位、包含字母、数字、特殊符号"
          prop="newPassword"
          show-password
          use-prefix-slot
        >
          <template #prefix>
            <img class="mt-0.5" src="./imgs/lock.png" />
          </template>
        </wd-input>
        <wd-input
          v-model="resetForm.confirmPassword"
          class="h-[50px] flex flex-col px-4 !rounded-2"
          clearable
          no-border
          placeholder="请再次输入新密码"
          prop="confirmPassword"
          show-password
          use-prefix-slot
        >
          <template #prefix>
            <img class="mt-0.5" src="./imgs/lock.png" />
          </template>
        </wd-input>
        <wd-button class="w-full !rounded-4" type="primary" @click="confirmClick">确认</wd-button>
      </view>
    </view>
  </wd-form>
</template>

<script lang="ts" setup>
/**
 * @file 登录-重置密码页
 * <AUTHOR>
 * @date 2024/12/25
 */
import { editPassword } from '@/service/api'
import { FormRules } from 'wot-design-uni/components/wd-form/types'
import { useToast } from 'wot-design-uni'
const toast = useToast()
// onLoad中获取上一个页面传入旧密码-decodeURIComponent解码
onLoad((opt) => {
  resetForm.oldPassword = JSON.parse(decodeURIComponent(opt.pwd as string))
})
const form = ref()

const resetForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
})

const rules: FormRules = {
  newPassword: [
    {
      required: true,
      pattern:
        /^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[!@#$%^&*。；》《（）、~`()_+\-={}[\]|;:'",.<>/?]).{8,20}$/,
      message: '请输入8-20位，包含数字、字母和特殊符号',
    },
  ],
  confirmPassword: [
    {
      required: true,
      message: '请再次输入新密码',
      validator: (value) => {
        if (value && value === resetForm.newPassword) {
          return Promise.resolve()
        } else {
          return Promise.reject('两次输入的密码不一致')
        }
      },
    },
  ],
}
const confirmClick = () => {
  form.value
    .validate()
    .then(({ valid }) => {
      if (valid) {
        editPassword(resetForm).then((res) => {
          if (res.success) {
            toast.success('重置密码成功')
            uni.navigateBack()
          } else {
            toast.error(res.message)
          }
        })
      }
    })
    .catch((error) => {
      console.log(error, 'error')
    })
}
</script>

<style lang="scss" scoped>
:deep(.wd-input__value) {
  height: 100%;
}

.login-logo {
  flex-grow: 2;
  //不因为父元素的flex改变宽度
  object-fit: scale-down;
}

.login-background {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  padding: 10px;
  display: flex;
  justify-content: end;
  flex-direction: column;
  background: linear-gradient(180deg, #356afd 0%, #356afd 10%, #ffffff 70%);
}

:deep(.wd-input) {
  background-color: var(--page-bg-color);
}

:deep(.wd-button) {
  height: 50px !important;
}

:deep(.wd-icon) {
  background-color: var(--page-bg-color);
}
</style>
