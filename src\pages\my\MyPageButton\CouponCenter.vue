<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '领券中心' },
}
</route>
<template>
  <view class="p-4 pt-0">
    <CouponItem type="center" v-for="item in couponList" :key="item.id" :coupon="item"></CouponItem>
  </view>
</template>

<script setup lang="ts">
/**
 * @file 我的页面-优惠券中心
 * <AUTHOR>
 * @date 2025/06/04
 */
import CouponItem from './CouponItem.vue'
import { fetchCouponCenter } from '@/service/api'

const couponList = ref([])

const getCouponList = async () => {
  const { data } = await fetchCouponCenter({ status: 4, size: 99 })
  console.log('data', data)
  couponList.value = data.records
}
getCouponList()
</script>

<style scoped></style>
