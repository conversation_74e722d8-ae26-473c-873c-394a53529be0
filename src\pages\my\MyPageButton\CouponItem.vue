<template>
  <view class="bg-white rounded-lg relative mt-4 overflow-hidden">
    <view class="px-4 py-3" @click="onSelect">
      <view class="flex items-center text-[#6D3202]" :style="{ color: isFailure ? '#979998' : '#6D3202' }">
        <view class="text-base font-bold">{{ coupon.couponName }}</view>
        <view class="px-1 rounded ml-1 text-xs border border-[#f1ebe6] border-solid"
          :style="{ borderColor: isFailure ? '#f5f5f5' : '#f1ebe6' }">
          {{ couponTypeMap.get(coupon.couponType) }}
        </view>
        <view class="flex items-baseline ml-auto" v-if="coupon.couponPrice">
          <view class="text-sm">￥</view>
          <view class="text-lg font-bold">{{ coupon.couponPrice }}</view>
        </view>
      </view>
      <view class="flex justify-between text-xs color-[#979998]">
        <view>
          {{ coupon.couponStartTime?.slice(0, 10) }}-{{ coupon.couponEndTime?.slice(0, 10) }}
        </view>
        <view v-if="coupon.lowEliminationPrice">满{{ coupon.lowEliminationPrice }}元可用</view>
        <view v-else>无门槛</view>
      </view>
    </view>
    <view class="grid px-4 py-3 border-t text-xs color-[#979998] rule-area">
      <view class="flex justify-between">
        <view @click="onFoldChange" class="flex items-center">
          <view class="mr-0.5">使用规则</view>
          <wd-icon v-if="isFold" name="arrow-down" size="14px"></wd-icon>
          <wd-icon v-else name="arrow-up" size="14px"></wd-icon>
        </view>
        <template v-if="type === 'my'">
          <view v-if="coupon.relativeStatus === 1">已使用</view>
          <view v-else-if="coupon.relativeStatus === 2">已过期</view>
        </template>
        <template v-else-if="type === 'select'">
          <view class="-mr-10px" v-if="checkUse(coupon) === '可以使用'">
            <wd-checkbox v-model="coupon.checked" @change="handleChange" checked-color="#6D3202"></wd-checkbox>
          </view>
          <view v-else class="text-[#F9744B]">{{ checkUse(coupon) }}</view>
        </template>
        <template v-else-if="type === 'center'">
          <view class="px-2.5 text-sm text-white min-w-16 text-center leading-6 bg-[#6D3202] rounded-full"
            v-if="!coupon.relativeStatus" @click="onReceive(coupon.id)">
            立即领取
          </view>
          <view class="px-2.5 text-sm text-white min-w-16 text-center leading-6 bg-[#979998] rounded-full"
            v-if="coupon.relativeStatus === 3">
            已领取
          </view>
        </template>
      </view>
      <view class="px-2 text-justify content" :class="isFold ? 'isfold' : 'unfold'">
        <view class="text">{{ coupon.rule }}</view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
/**
 * @file 我的页面-优惠券
 * <AUTHOR>
 * @date 2025/06/04
 */
import dayjs from 'dayjs'
import { fetchReceiveCoupon } from '@/service/api'
import { useToast } from 'wot-design-uni'
const toast = useToast()

const props = withDefaults(
  defineProps<{
    coupon: any
    goodsPrice: number
    type: 'my' | 'center' | 'select'
  }>(),
  {
    type: 'my',
    goodsPrice: 0,
  },
)

const couponTypeMap = new Map([
  [0, '新人券'],
  [1, '满减券'],
  [2, '代金券'],
  [3, '免费喝一杯'],
])

const relativeStatusMap = new Map([
  [0, '未使用'],
  [1, '已使用'],
  [2, '已过期'],
  [3, '已领取'],
])

const isFailure = computed(() => [1, 2].includes(props.coupon.relativeStatus))

const isFold = ref<boolean>(true)
const emit = defineEmits(['checked'])

const onFoldChange = (e: any) => {
  console.log('e.detail.value', e)
  isFold.value = !isFold.value
}
function handleChange({ value }) {
  console.log('handleChange', value);
  emit('checked', value)
}
const onSelect = () => {
  props.coupon.checked = !props.coupon.checked
  emit('checked', props.coupon.checked)
}

const onReceive = (id: number) => {
  fetchReceiveCoupon({ id }).then((res) => {
    if (res.success) {
      props.coupon.relativeStatus = 3
      toast.success('领取成功')
    } else {
      toast.error(res.message)
    }
  })
}

const checkUse = (coupon: any) => {
  console.log('goodsPrice', props.goodsPrice)

  const startDate = dayjs(coupon.couponStartTime)
  const currentTime = dayjs()
  const inBefore = currentTime.isBefore(startDate)
  if (coupon.relativeStatus === 1) {
    return '已使用'
  } else if (coupon.relativeStatus === 2) {
    return '已过期'
  } else if (inBefore) {
    return '未到使用日期'
  } else if (props.goodsPrice < coupon?.lowEliminationPrice || 0) {
    return '未达到消费金额门槛'
  } else {
    return '可以使用'
  }
}
</script>

<style lang="scss" scoped>
.content {
  display: grid;
}

.isfold {
  grid-template-rows: 0fr;
  transition: grid-template-rows 0.3s ease;

  .text {
    overflow: hidden;
  }
}

.unfold {
  padding-top: 4px;
  grid-template-rows: 1fr;
}

.rule-area {
  position: relative;
  border-top: 1px dotted #ececec;

  &::before,
  &::after {
    content: '';
    position: absolute;
    top: -4px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #eff1f5;
  }

  &::before {
    left: -4px;
  }

  &::after {
    right: -4px;
  }
}
</style>
