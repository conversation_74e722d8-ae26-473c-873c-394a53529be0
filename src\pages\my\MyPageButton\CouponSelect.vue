<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '选择优惠券' },
}
</route>
<template>
  <view class="p-4">
    <view
      @click="onChecked(false, '')"
      class="bg-white py-2 rounded-full text-center text-sm text-[#656666]"
    >
      不使用优惠券
    </view>
    <view
      class="flex items-center justify-center h-80 text-sm text-[#979998] translate-y-24"
      v-if="couponList.length === 0"
    >
      暂无优惠券
    </view>
    <CouponItem
      v-else
      v-for="item in couponList"
      :key="item.id"
      :coupon="item"
      :goodsPrice="params.price"
      type="select"
      @checked="onChecked($event, item)"
    />
  </view>
</template>

<script setup lang="ts">
/**
 * @file 选择优惠券
 * <AUTHOR>
 * @date 2025/06/18
 */
import CouponItem from './CouponItem.vue'
import { fetchCouponList } from '@/service/api'
import { onLoad } from '@dcloudio/uni-app'

const params = ref({
  price: 0,
  couponId: 0,
})

let eventChannel: {
  emit: (eventName: string, ...args: any) => void
}
onLoad((p) => {
  console.log('params', p)
  params.value = p
  const instance = getCurrentInstance().proxy
  // @ts-expect-error - eventChannel
  eventChannel = instance.getOpenerEventChannel()
})

// relativeStatus 0：已使用 1：已过期 2：已领取
const couponList = ref([])

const getCouponList = async () => {
  const res = await fetchCouponList('')
  couponList.value = res.data?.records || []
  if (params.value.couponId) {
    for (const item of couponList.value) {
      if (item.id === params.value.couponId) {
        item.checked = true
        break
      }
    }
  }
}
getCouponList()

const onChecked = (checked: any, coupon: any) => {
  couponList.value.forEach((x) => {
    x.checked = x.id === coupon?.id
  })
  const payload = {
    couponId: coupon?.id,
    couponName: coupon?.couponName,
  }
  eventChannel.emit('select', payload)
  uni.navigateBack()
}
</script>

<style scoped></style>
