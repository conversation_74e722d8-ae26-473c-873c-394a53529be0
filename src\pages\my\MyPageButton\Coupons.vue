<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '我的优惠券' },
}
</route>
<template>
  <view class="p-4">
    <view @click="toCouponCenter" class="flex items-center rounded-lg h-16 pl-4 pr-2 bg-[#6D3202]">
      <view class="-mb-[17px] mr-4">
        <wd-img :width="50" :height="48" :src="CouponImg" />
      </view>
      <view class="text-base text-white">领券中心</view>
      <view class="ml-auto">
        <wd-icon name="arrow-right" size="20px" color="#FFF"></wd-icon>
      </view>
    </view>
    <CouponItem v-for="item in couponList" :key="item.id" :coupon="item" @checked="onChecked($event, item.id)" />
  </view>
</template>

<script setup lang="ts">
/**
 * @file 我的页面-优惠券
 * <AUTHOR>
 * @date 2025/06/04
 */
import CouponItem from './CouponItem.vue'
import CouponImg from '../imgs/icon_coupon2.png'
import { fetchCouponList } from '@/service/api'
import { onShow } from '@dcloudio/uni-app'

// relativeStatus 0：已使用 1：已过期 2：已领取
const couponList = ref([])
const getCouponList = async () => {
  const res = await fetchCouponList('')
  couponList.value = res.data?.records || []
}
onShow(() => {
  getCouponList()
})

const toCouponCenter = () => {
  uni.navigateTo({ url: '/pages/my/MyPageButton/CouponCenter' })
}
const currentCoupon = ref('')
const onChecked = (checked: any, id: any) => {
  currentCoupon.value = id || ''
  couponList.value.forEach((x) => {
    x.checked = x.id === id ? checked : false
  })
}
</script>

<style scoped></style>
