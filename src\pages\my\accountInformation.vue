<route lang="json5">
{
  style: { navigationBarTitleText: '账号信息' },
}
</route>
<template>
  <view class="p-2">
    <wd-cell-group class="rounded-3 overflow-hidden" custom-class="custom-info">
      <wd-cell
        v-for="data in detailLabel"
        :key="userStore.userInfo[data.field]"
        :title="data.label"
        :value="userStore.userInfo[data.field]"
      />
    </wd-cell-group>
    <wd-button icon="logout" block class="bg-white" plain custom-class="custom-btn" @click="logOut">
      退出登录
    </wd-button>
  </view>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store'
const userStore = useUserStore()
// 列表字段
const detailLabel = [
  { label: '账号', field: 'userName' },
  { label: '真实姓名', field: 'employeeName' },
  { label: '手机号码', field: 'mobile' },
  { label: '角色', field: 'roleName' },
  { label: '所属部门', field: 'orgName' },
  { label: '职位', field: 'job' },
]
// 登录跳转
const logOut = () => {
  userStore.clearUserInfo()
  uni.switchTab({ url: '/pages/index/index' })
}
</script>

<style scoped>
:deep(.custom-btn) {
  color: #f25555ff !important;
  border: none !important;
  border-radius: 12px !important;
  padding: 5px 0 !important;
  margin-top: 10px;
  box-sizing: content-box;
}
:deep(.wd-button__text) {
  display: flex !important;
  align-items: center;
}
</style>
