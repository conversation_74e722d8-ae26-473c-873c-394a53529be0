<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '我的' },
}
</route>
<template>
  <view class="my-container flex flex-col h-screen w-full overflow-hidden">
    <view class="flex-1 flex flex-col overflow-x-hidden">
      <view
        v-if="!userInfo?.userName"
        class="relative p-4 bg-white flex items-center justify-between"
      >
        <!-- 隐藏的实际触发按钮 -->
        <button
          class="get-phone"
          open-type="getPhoneNumber"
          @getphonenumber="getUserPhone"
          style="position: absolute; opacity: 0; width: 100%; height: 100%"
        ></button>
        <view class="flex items-center">
          <!-- 可见的UI元素 -->
          <image class="w-14 h-14 mr-2" :src="Avatar" />
          <view class="text-black font-bold text-xl">点击登录</view>
        </view>
      </view>
      <wd-cell v-else custom-class="user-maintenance" center>
        <template #title>
          <view class="flex items-center">
            <img alt="" class="w-14 h-14 mr-2" :src="Avatar" />
            <view class="flex flex-col">
              <view class="text-black text-lg font-bold">
                {{ userInfo?.userName || '微信用户' }}
              </view>
              <view class="text-gray text-sm">
                {{ phoneMask(useUserStore().userInfo?.mobile) }}
              </view>
            </view>
          </view>
        </template>
      </wd-cell>
      <view class="relative flex justify-around rounded-lg bg-white mt-2 mx-2 py-4">
        <button
          v-if="!userInfo?.userName"
          style="
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            z-index: 999;
            padding: 0;
          "
          open-type="getPhoneNumber"
          @getphonenumber="getUserPhone"
        ></button>
        <view
          v-for="item in orderList"
          :key="item.title"
          class="flex flex-col gap-2 items-center flex-1"
          @click="onCellClick(item)"
        >
          <wd-badge
            :modelValue="item.message"
            :right="4"
            custom-class="flex! flex-col gap-2 items-center"
          >
            <img :src="item.img" alt="" class="w-[56rpx] h-[56rpx] object-cover" />
            <view class="text-[#1A1A1A] text-sm">{{ item.title }}</view>
          </wd-badge>
        </view>
      </view>
      <view class="flex justify-around rounded-lg bg-white mt-2 mx-2 py-4">
        <view
          v-for="item in functionList"
          :key="item.title"
          class="flex flex-col gap-2 items-center flex-1"
          @click="onCellClick(item)"
        >
          <img :src="item.img" alt="" class="w-[56rpx] h-[56rpx] object-cover" />
          <view class="text-[#1A1A1A] text-sm">{{ item.title }}</view>
        </view>
      </view>
      <view class="btns mt-auto flex p-2 gap-2">
        <wd-button v-if="userInfo?.userName" type="primary" @click="logOut">
          <jumpTo />
          退出登录
        </wd-button>
        <wd-button v-else type="primary" @click="toMaintenance">运维端</wd-button>
      </view>
    </view>

    <TabBar v-model="activeTab"></TabBar>
  </view>
</template>

<script lang="ts" setup>
import { useUserStore } from '@/store'
import TabBar from '@/components/TabBar.vue'
import { getUserInfo, loginApi, fetchCouponList, getNotPayCountOrNotEvaluate } from '@/service/api'
import { getLoginCode } from '@/utils'
import Avatar from './imgs/avatar.svg?url'
import IconOrder from './imgs/icon_order.svg?url'
import IconReview from './imgs/icon_review.svg?url'
import IconCoupon from './imgs/icon_coupon.svg?url'
import IconCustomerService from './imgs/icon_customer_service.svg?url'
import IconPolicy from './imgs/icon_policy.svg?url'
import IconAbout from './imgs/icon_about.svg?url'
import { contactStaff, phoneMask } from '@/utils/consumer'
import { storeToRefs } from 'pinia'
import { onShow } from '@dcloudio/uni-app'

const { userInfo } = storeToRefs(useUserStore())

const activeTab = ref('my')
const userStore = useUserStore()

// 评价中心/优惠券/全部订单 功能字段
const orderList = ref([
  {
    title: '全部订单',
    path: '/pages/my/MyPageButton/Coupons',
    img: IconOrder,
    message: '',
  },
  {
    title: '评价中心',
    path: '/pages/my/MyPageButton/Coupons',
    img: IconReview,
    message: '',
  },
  {
    title: '优惠券',
    path: '/pages/my/MyPageButton/Coupons',
    img: IconCoupon,
    message: '',
  },
])

const couponUnusedStatus = 0

const getCouponTotal = async () => {
  // 优惠券数量
  const couponRes = (await fetchCouponList('')) as any
  const couponTotal = couponRes.data.records?.filter(
    (x) => x.relativeStatus === couponUnusedStatus,
  ).length
  // 未评价数量
  const noCommentsNum = await getNotPayCountOrNotEvaluate({
    type: 0,
  }).then((res: any) => {
    return res.data
  })
  // 未支付
  const unPaidNum = await getNotPayCountOrNotEvaluate({
    type: 1,
  }).then((res: any) => {
    return res.data
  })
  // 找到orderList优惠券赋值
  const couponItem = orderList.value.find((item) => item.title === '优惠券')
  // 找评价中心
  const messageItem = orderList.value.find((item) => item.title === '评价中心')
  // 找到未支付订单
  const unPaidItem = orderList.value.find((item) => item.title === '全部订单')
  couponItem.message = couponTotal || ''
  messageItem.message = noCommentsNum || ''
  unPaidItem.message = unPaidNum || ''
}
// 监听userInfo,如果有userName则请求getCouponTotal
watch(
  () => userInfo.value.userName,
  (newVal) => {
    if (newVal) {
      getCouponTotal()
    } else {
      // 清空message数据
      orderList.value.forEach((item) => {
        item.message = ''
      })
    }
  },
  {
    immediate: true,
    deep: true,
  },
)

onShow(() => {
  if (userInfo.value.userName) {
    getCouponTotal()
  }
})

// 登录跳转
const logOut = () => {
  userStore.clearUserInfo()
}

// 联系客服//隐私政策/关于我们 功能字段数据
const functionList = [
  {
    title: '联系客服',
    img: IconCustomerService,
  },
  {
    title: '隐私政策',
    path: '/pages/my/MyPageButton/Privacy',
    img: IconPolicy,
  },
  {
    title: '关于我们',
    path: '/pages/my/MyPageButton/About',
    img: IconAbout,
  },
]

const onCellClick = (item: any) => {
  // 没有登陆先让用户登录
  if (!userInfo.value.userName) {
    return
  }
  if (item.title === '优惠券') {
    uni.navigateTo({
      url: '/pages/my/MyPageButton/Coupons',
    })
  }
  if (item.title === '全部订单') {
    uni.switchTab({
      url: '/pages/order/index',
      // animationDuration: 0,
    })
  }
  if (item.title === '联系客服') {
    contactStaff()
  }
  if (item.title === '评价中心') {
    uni.navigateTo({ url: '/pages/evaluate-centre/index' })
  }
}

const getUserPhone = async (e) => {
  const code = await getLoginCode()
  // 用户同意登录
  if (e.detail.errMsg === 'getPhoneNumber:ok') {
    const params = { loginCode: code, phoneCode: e.detail.code, loginType: 'normal' }
    // 获取用户token接口
    await loginApi(params).then((res: any) => {
      useUserStore().setToken(res.data.jwtToken as string)
    })
    const userInfo = await getUserInfo().then((res) => {
      return res.data
    })
    useUserStore().setUserInfo(userInfo)
  }
}

// 运维端
const toMaintenance = () => {
  // 储存用户信息包括角色
  // useUserStore().setUserInfo({
  //   roleCode: 'maintenance',
  // })
  // uni.redirectTo({
  //   url: '/pages-maintenance/index/index',
  // })
  uni.navigateTo({ url: '/pages/login/index?title=maintenance' })
}
</script>
<script lang="ts">
export default { options: { styleIsolation: 'shared' } }
</script>
<style lang="scss" scoped>
:deep(.wd-badge__content.wd-badge__content) {
  top: 5px;
  right: 2px;
  background: #f9744b;
}

:deep(.user-maintenance) {
  .wd-cell__wrapper {
    @apply p-4;
    padding-left: 0 !important;
    padding-right: 0 !important;
  }

  .wd-cell__right {
    display: none;
  }
}

:deep(.btns) {
  .wd-button.wd-button {
    height: 100rpx;
    flex: 1;
    background-color: #fff;
    border-radius: 12px;
    color: rgba(26, 26, 26, 1);
  }
}
</style>
