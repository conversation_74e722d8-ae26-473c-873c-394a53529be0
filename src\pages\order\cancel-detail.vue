<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '已取消' },
}
</route>
<template>
  <view class="border-box p-2" style="--wot-button-primary-bg-color: #6d3202">
    <view class="flex flex-col gap-2 relative mb-2">
      <view
        class="flex flex-col gap-2 bg-white rounded-xl p-4"
        v-for="goods of goodsList"
        :key="goods.id"
      >
        <view
          class="w-full flex justify-between items-center pb-3 border-b border-b-[#EBEDF1] border-b-solid"
        >
          <view class="flex items-center">
            <view class="text-base text-black font-bold mr-1">{{ goods.carName }}</view>
            <view
              class="h-4 text-xs rounded text-[#6d3202] px-1 bg-[#f0eae5]"
              v-if="ORDER_TYPE_MAP[goods.orderType]"
            >
              {{ ORDER_TYPE_MAP[goods.orderType] }}
            </view>
          </view>
          <view
            class="flex items-center text-sm text-gray-500"
            :style="{ color: statusColorMap[goods.orderStatus] }"
          >
            <view>
              {{ orderStatusMap[goods.orderStatus] }}
            </view>
          </view>
        </view>
        <template v-if="Array.isArray(goods.miniProductOrderDetailInfo)">
          <view class="flex gap-2" v-for="item of goods.miniProductOrderDetailInfo" :key="item.id">
            <GoodsImage :src="item.goodsUrl" />
            <view class="flex flex-col grow justify-between">
              <view class="text-sm text-[#1a1a1a] font-bold">{{ item.goodsName }}</view>
              <view class="text-xs text-[#656666]" :class="{ '-mt-2': !item.promotionLabel }">
                {{ item.pellets }}
              </view>
              <view
                v-if="item.promotionLabel"
                class="flex items-center justify-between rounded text-xs text-[#6D3202] w-fit border border-solid border-[#F1EBE6] p-.5 px-1 whitespace-nowrap mt-2"
              >
                <text>{{ item.promotionLabel }}</text>
              </view>
              <view class="flex justify-between items-center text-sm">
                <view class="flex items-center text-[#6d3202]">
                  <text class="text-base font-semibold">
                    ¥{{ item.mainPromotionTotalPrice || item.mainTotalPrice }}
                  </text>
                  <text
                    v-if="
                      item.mainPromotionTotalPrice &&
                      item.mainPromotionTotalPrice !== item.mainTotalPrice
                    "
                    class="text-xs line-through text-gray-600 ml-1"
                  >
                    ¥{{ item.mainTotalPrice }}
                  </text>
                </view>
                <view class="text-[#656666]">
                  <text class="text-xs">X</text>
                  <text>{{ item.total }}</text>
                </view>
              </view>
            </view>
          </view>
        </template>
      </view>
    </view>
    <view class="flex flex-col mb-2 bg-white rounded-xl">
      <view class="flex flex-col p-4 gap-3">
        <view
          v-if="productOrderInfo.mainDiscountPrice"
          class="flex justify-between items-center text-sm"
        >
          <view class="text-[#979998]">商品直减</view>
          <view class="text-[#1a1a1a]">-¥{{ Math.abs(productOrderInfo.mainDiscountPrice) }}</view>
        </view>
        <view
          v-if="productOrderInfo.mainCouponTotalPrice"
          class="flex justify-between items-center text-sm"
        >
          <view class="text-[#979998]">优惠券</view>
          <view class="text-[#6D3202]">-¥{{ productOrderInfo.mainCouponTotalPrice }}</view>
        </view>
        <view
          class="h-px bg-gray-200"
          v-if="productOrderInfo.mainDiscountPrice || productOrderInfo.mainCouponTotalPrice"
        ></view>
        <view class="text-sm text-[#6d3202] flex items-center justify-end gap-5">
          <text
            v-if="productOrderInfo.mainPromotionTotalPrice !== productOrderInfo.mainTotalPrice"
            class="text-[#1a1a1a] ml-1"
          >
            <text v-if="productOrderInfo.mainCouponTotalPrice">券后</text>
            <text>已优惠</text>
            <text class="text-[#6D3202]">¥{{ productOrderInfo.mainTotalDiscountPrice }}</text>
          </text>
          <view class="flex items-center">
            <text class="text-[#1a1a1a] mr-1">应付:</text>
            <text class="font-bold">¥</text>
            <text class="text-lg font-semibold">
              {{ productOrderInfo.mainPayPrice }}
            </text>
          </view>
        </view>
      </view>
    </view>
    <view class="p-4 bg-white rounded-xl text-[#979998] text-sm">
      <view>
        <view class="flex justify-end items-center mt-2">
          <view class="mr-auto">订单号</view>
          <view class="text-[#1A1A1A]">{{ productOrderInfo.sysOrderNum }}</view>
          <view class="w-px h-2.5 mx-1.5 bg-[#BDBDBF]"></view>
          <view class="text-[#1A1A1A]" @click="onCopy(productOrderInfo.sysOrderNum)">复制</view>
        </view>
        <view class="flex justify-between mt-2">
          <view>下单时间</view>
          <view class="text-[#1A1A1A]">{{ productOrderInfo.orderTime }}</view>
        </view>
        <template v-if="productOrderInfo.orderType?.toString() === ORDER_TYPE.remote">
          <view class="flex justify-between mt-2">
            <view>取货时间</view>
            <view class="text-[#1A1A1A]">{{ pickupTime }}</view>
          </view>
          <view class="flex justify-between mt-2">
            <view>取货点</view>
            <view class="flex items-center justify-end text-sm text-[#1A1A1A]">
              <text class="whitespace-nowrap">{{ productOrderInfo.deliveryAddr }}</text>
              <wd-divider vertical />
              <text @click="handleNav" class="text-[#6d3202] whitespace-nowrap">导航</text>
            </view>
          </view>
        </template>
        <view class="flex justify-between mt-2">
          <view>出餐车辆</view>
          <view class="text-[#1A1A1A]">{{ productOrderInfo.carName }}</view>
        </view>
        <view class="flex justify-between mt-2">
          <view>运营区域</view>
          <view class="text-[#1A1A1A]">{{ productOrderInfo.areaName }}</view>
        </view>
      </view>
    </view>
    <view
      @click="contactStaff"
      class="flex justify-center items-center bg-white rounded-xl mt-2 mb-3 py-2.5 text-[#1a1a1a] text-sm"
    >
      <wd-icon name="service" size="18px"></wd-icon>
      <view class="ml-1">联系客服</view>
    </view>
  </view>
</template>
<script setup lang="ts">
// 已取消状态订单显示
import { getOrderDetails } from '@/service/api'
import { contactStaff } from '@/utils/consumer'
import { ORDER_TYPE_MAP, statusColorMap, orderStatusMap, ORDER_TYPE } from '@/types/consumer'
import dayjs from 'dayjs'
import Big from 'big.js'

const goodsList = ref<any[]>([])
const productOrderInfo = ref<any>({})
const getInfo = (orderId, sysOrderNum) => {
  const params: any = { type: 'mini' }
  if (orderId) params.orderId = orderId
  else if (sysOrderNum) params.sysOrderNum = sysOrderNum
  getOrderDetails(params).then((res: any) => {
    goodsList.value = res.data.miniProductOrderInfo
    productOrderInfo.value = res.data.productOrderInfo
    const {
      mainTotalPrice = 0,
      mainPromotionTotalPrice = 0,
      mainCouponTotalPrice = 0,
    } = res.data.productOrderInfo
    productOrderInfo.value = {
      ...res.data.productOrderInfo,
      mainDiscountPrice: Big(mainPromotionTotalPrice).minus(mainTotalPrice).toNumber(),
      mainTotalDiscountPrice: Big(mainTotalPrice)
        .minus(mainPromotionTotalPrice)
        .plus(mainCouponTotalPrice)
        .toNumber(),
      mainPayPrice: mainPromotionTotalPrice || mainTotalPrice,
    }
  })
}

// 线上订单显示取货时间
const pickupTime = computed(() => {
  const { pickupStartTime, pickupEndTime } = productOrderInfo.value
  if (pickupStartTime && pickupEndTime) {
    return `${dayjs(pickupStartTime).format('HH:mm')} - ${dayjs(pickupEndTime).format('HH:mm')}`
  }
  return '-'
})

// 导航
const handleNav = () => {
  const { lat, lon, deliveryAddr } = productOrderInfo.value
  uni.openLocation({
    latitude: lat,
    longitude: lon,
    name: deliveryAddr,
    address: deliveryAddr,
  })
}

const onCopy = (val) => {
  uni.setClipboardData({ data: val })
}

onLoad((options) => {
  getInfo(options.orderId, options.sysOrderNum)
})
</script>
