<template>
  <view class="flex flex-col gap-2">
    <!-- 车辆状态card -->
    <wd-card
      custom-style="--wot-card-margin: 0; margin: 0"
      v-if="data.orderType?.toString() === ORDER_TYPE.remote"
    >
      <template #title>
        <view class="flex justify-between items-center">
          <view class="text-[#1a1a1a] font-bold">
            车辆{{ data.carName }}
            {{ makeStatus.toString() === MakeStatus.WAITING ? '将为您提供服务' : '已到达，请取货' }}
          </view>
          <view class="text-sm text-[#6d3202]" @click="lookingProgress(data.id)">
            <text class="mr-1">查看送货进度</text>
            <wd-icon name="arrow-right" size="14px" />
          </view>
        </view>
      </template>
      <view class="w-full h-px bg-[#EBEDF1] -mt-1 mb-1"></view>
      <view style="--wot-cell-wrapper-padding: 2px; --wot-cell-padding: 0" class="pb-4">
        <wd-cell title="取货时间" :value="pickupTime" />
        <wd-cell title="取货点" center>
          <view class="flex items-center justify-end text-sm">
            <text class="whitespace-nowrap">{{ data.deliveryAddr }}</text>
            <wd-divider vertical />
            <text @click="handleNav" class="text-[#6d3202] whitespace-nowrap">导航</text>
          </view>
        </wd-cell>
      </view>
    </wd-card>

    <!-- 制作状态card -->
    <view class="rounded-xl bg-white p-4 flex flex-col gap-2 items-center justify-center">
      <view class="text-lg text-[#6d3202] font-bold">{{ data.topMakeDes || '-' }}</view>
      <view class="text-sm text-[#1a1a1a]">{{ data.middleMakeDes || '-' }}</view>
    </view>
    <!-- 取货口card -->
    <view class="rounded-xl bg-white p-4 flex flex-col gap-2 items-center justify-center">
      <view class="text-lg font-bold">取货口: {{ pickupPort || '-' }}</view>
      <view class="text-sm text-[#1a1a1a]" v-if="!orderCompleteStatus">制作完成后显示</view>
      <wd-button
        v-else
        custom-class="w-28"
        custom-style="--wot-button-primary-bg-color: #6d3202"
        type="primary"
        @click="handlePickup"
        :disabled="pickupCompleted"
      >
        {{ pickupCompleted ? '已取餐' : '立即开柜' }}
      </wd-button>
    </view>
    <!-- 取货码card -->
    <view class="rounded-xl bg-white p-4 pb-2 flex flex-col gap-4 items-center justify-center mb-2">
      <wd-steps custom-class="w-full" :active="activeStep" align-center>
        <wd-step
          title="已下单"
          :custom-style="
            activeStep === 0
              ? '--wot-steps-finished-color: #6d3202'
              : '--wot-steps-finished-color:: #BDBDBF'
          "
        >
          <template #icon>
            <image :src="IconTicket" size="32px" />
          </template>
        </wd-step>
        <wd-step
          title="制作中"
          :custom-style="
            activeStep === 1
              ? '--wot-steps-finished-color: #6d3202'
              : '--wot-steps-finished-color:: #BDBDBF'
          "
        >
          <template #icon>
            <image :src="activeStep === 1 ? IconMakingActive : IconMaking" size="32px" />
          </template>
        </wd-step>
        <wd-step
          title="请取餐"
          :custom-style="
            activeStep === 2
              ? '--wot-steps-finished-color: #6d3202'
              : '--wot-steps-finished-color:: #BDBDBF'
          "
        >
          <template #icon>
            <image :src="activeStep === 2 ? IconPickupActive : IconPickup" size="32px" />
          </template>
        </wd-step>
      </wd-steps>
      <view class="text-sm text-gray-500 -mt-2">{{ data.bottomMakeDes || '-' }}</view>
    </view>
  </view>
</template>
<script setup lang="ts">
import IconTicket from '../imgs/ticket.svg?url'
import IconMaking from '../imgs/making.svg?url'
import IconMakingActive from '../imgs/making-active.svg?url'
import IconPickup from '../imgs/pickup.svg?url'
import IconPickupActive from '../imgs/pickup-active.svg?url'
import { openBoxDoor } from '@/service/api'
import { useToast } from 'wot-design-uni'
import { MakeStatus, OrderStatus, ORDER_TYPE } from '@/types/consumer'
import dayjs from 'dayjs'
const toast = useToast()
const props = defineProps<{
  // 制作状态字段
  makeStatus: MakeStatus
  // 订单状态
  orderStatus: OrderStatus
  // 取货口字段
  pickupPort: string
  // 订单数据
  data: any
}>()
// 柜门是否打开
const boxDoorOpened = ref(false)

// 当前激活订单步骤
const activeStep = computed(() => {
  // 制作中
  if (
    (props.orderStatus?.toString() === OrderStatus.PAID &&
      props.makeStatus?.toString() === MakeStatus.WAITING) ||
    [MakeStatus.PROCESSING, MakeStatus.PARTIAL].includes(props.makeStatus?.toString() as MakeStatus)
  ) {
    return 1
  }
  // 请取餐
  if ([MakeStatus.COMPLETED].includes(props.makeStatus?.toString() as MakeStatus)) {
    return 2
  }
  return 0
})

// 订单是否完成 包含制作完成和部分制作完成
const orderCompleteStatus = computed(() => {
  return [MakeStatus.COMPLETED, MakeStatus.PARTIAL].includes(
    props.makeStatus?.toString() as MakeStatus,
  )
})

// 线上订单显示取货时间
const pickupTime = computed(() => {
  const { pickupStartTime, pickupEndTime } = props.data
  if (pickupStartTime && pickupEndTime) {
    return `${dayjs(pickupStartTime).format('HH:mm')} - ${dayjs(pickupEndTime).format('HH:mm')}`
  }
  return '-'
})

// 取餐完成
const pickupCompleted = computed(() => {
  return props.orderStatus?.toString() === OrderStatus.COMPLETED
})

// 立即开柜
const handlePickup = () => {
  openBoxDoor({ orderId: props.data.id }).then((res) => {
    if (res.success) {
      toast.success('开柜成功，请及时取餐')
      boxDoorOpened.value = true
    }
  })
}
// 查看送货进度
const lookingProgress = (orderId) => {
  uni.navigateTo({ url: '/pages/order/progress?orderId=' + orderId })
}
// 导航
const handleNav = () => {
  uni.openLocation({
    latitude: props.data.lat,
    longitude: props.data.lon,
    name: props.data.deliveryAddr,
    address: props.data.deliveryAddr,
  })
}
</script>
