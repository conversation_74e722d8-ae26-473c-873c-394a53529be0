<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '订单详情' },
}
</route>
<template>
  <view class="border-box p-2">
    <DetailCard
      :pickup-port="productOrderInfo.pickupPort"
      :make-status="productOrderInfo.makeStatus"
      :order-status="productOrderInfo.orderStatus"
      :data="productOrderInfo"
    />
    <view class="bg-white rounded-xl mb-2 p-4">
      <view class="flex flex-col gap-4 relative">
        <view class="flex gap-2" v-for="goods of goodsList" :key="goods.id">
          <GoodsImage :src="goods.goodsUrl" />
          <view class="flex flex-col grow justify-between">
            <view class="text-sm text-[#1a1a1a] font-bold">{{ goods.goodsName }}</view>
            <view class="text-xs text-[#656666]" :class="{ '-mt-2': !goods.promotionLabel }">
              {{ goods.pellets }}
            </view>
            <view
              v-if="goods.promotionLabel"
              class="flex items-center justify-between rounded text-xs text-[#6D3202] w-fit border border-solid border-[#F1EBE6] p-.5 px-1 whitespace-nowrap mt-2"
            >
              <text>{{ goods.promotionLabel }}</text>
            </view>
            <view class="flex justify-between items-center text-sm">
              <view class="flex items-center text-[#6d3202]">
                <text class="text-base font-semibold">
                  ¥{{ goods.mainPromotionTotalPrice || goods.mainTotalPrice }}
                </text>
                <text
                  v-if="
                    goods.mainPromotionTotalPrice &&
                    goods.mainPromotionTotalPrice !== goods.mainTotalPrice
                  "
                  class="text-xs line-through text-gray-600 ml-1"
                >
                  ¥{{ goods.mainTotalPrice }}
                </text>
              </view>
              <view class="text-[#656666]">
                <text class="text-xs">X</text>
                <text>{{ goods.total }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view class="flex flex-col mb-2 bg-white rounded-xl">
      <view class="flex flex-col p-4 gap-3">
        <view
          v-if="productOrderInfo.mainDiscountPrice"
          class="flex justify-between items-center text-sm"
        >
          <view class="text-[#979998]">商品直减</view>
          <view class="text-[#1a1a1a]">-¥{{ Math.abs(productOrderInfo.mainDiscountPrice) }}</view>
        </view>
        <view
          v-if="productOrderInfo.mainCouponTotalPrice"
          class="flex justify-between items-center text-sm"
        >
          <view class="text-[#979998]">优惠券</view>
          <view class="text-[#6D3202]">-¥{{ productOrderInfo.mainCouponTotalPrice }}</view>
        </view>
        <view
          class="h-px bg-gray-200"
          v-if="productOrderInfo.mainDiscountPrice || productOrderInfo.mainCouponTotalPrice"
        ></view>
        <view class="text-sm text-[#6d3202] flex items-center justify-end gap-5">
          <text
            v-if="productOrderInfo.mainPromotionTotalPrice !== productOrderInfo.mainTotalPrice"
            class="text-[#1a1a1a] ml-1"
          >
            <text v-if="productOrderInfo.mainCouponTotalPrice">券后</text>
            <text>已优惠</text>
            <text class="text-[#6D3202]">¥{{ productOrderInfo.mainTotalDiscountPrice }}</text>
          </text>
          <view class="flex items-center">
            <text class="text-[#1a1a1a] mr-1">应付:</text>
            <text class="font-bold">¥</text>
            <text class="text-lg font-semibold">
              {{ productOrderInfo.mainPayPrice }}
            </text>
          </view>
        </view>
      </view>
    </view>

    <view class="p-4 bg-white rounded-xl text-[#979998] text-sm">
      <view>
        <view class="flex justify-end items-center mt-2">
          <view class="mr-auto">订单号</view>
          <view class="text-[#1A1A1A]">{{ productOrderInfo.sysOrderNum }}</view>
          <view class="w-px h-2.5 mx-1.5 bg-[#BDBDBF]"></view>
          <view class="text-[#1A1A1A]" @click="onCopy(productOrderInfo.sysOrderNum)">复制</view>
        </view>
        <view class="flex justify-between mt-2">
          <view>下单时间</view>
          <view class="text-[#1A1A1A]">{{ productOrderInfo.orderTime }}</view>
        </view>
        <view class="flex justify-between mt-2">
          <view>支付时间</view>
          <view class="text-[#1A1A1A]">{{ productOrderInfo.payTime }}</view>
        </view>
        <view class="flex justify-between mt-2">
          <view>支付方式</view>
          <view class="text-[#1A1A1A]">{{ PAY_TYPE[productOrderInfo.payType] }}</view>
        </view>
        <view class="flex justify-between mt-2">
          <view>出餐车辆</view>
          <view class="text-[#1A1A1A]">{{ productOrderInfo.carName }}</view>
        </view>
        <view class="flex justify-between mt-2">
          <view>运营区域</view>
          <view class="text-[#1A1A1A]">{{ productOrderInfo.areaName }}</view>
        </view>
      </view>
    </view>
    <view
      @click="contactStaff"
      class="flex justify-center items-center bg-white rounded-xl mt-2 mb-3 py-2.5 text-[#1a1a1a] text-sm"
    >
      <wd-icon name="service" size="18px"></wd-icon>
      <view class="ml-1">联系客服</view>
    </view>
  </view>
</template>
<script setup>
import { getOrderDetails } from '@/service/api'
import { contactStaff } from '@/utils/consumer'
import DetailCard from './components/detail-card.vue'
import { PAY_TYPE } from '@/types/consumer'
import Big from 'big.js'

let intervalId = null
let orderId = null
const goodsList = ref([])
const productOrderInfo = ref({})
const getInfo = () => {
  getOrderDetails({ orderId, type: 'mini' }).then((res) => {
    goodsList.value = res.data.productOrderDetailInfo
    const data = res.data.productOrderInfo
    const { mainTotalPrice = 0, mainPromotionTotalPrice = 0, mainCouponTotalPrice = 0 } = data
    productOrderInfo.value = {
      ...data,
      // 活动优惠价格 不包含优惠券
      mainDiscountPrice: Big(mainPromotionTotalPrice).minus(mainTotalPrice).toNumber(),
      // 总优惠价格 包含优惠券
      mainTotalDiscountPrice: Big(mainTotalPrice)
        .minus(mainPromotionTotalPrice)
        .plus(mainCouponTotalPrice)
        .toNumber(),
      // 应付
      mainPayPrice: mainPromotionTotalPrice || mainTotalPrice,
    }
  })
}

const onCopy = (val) => {
  uni.setClipboardData({ data: val })
}

onLoad((options) => {
  orderId = options.orderId
  getInfo()
  intervalId = setInterval(() => {
    getInfo()
  }, 5000)
})

onUnload(() => {
  clearInterval(intervalId)
})
</script>
