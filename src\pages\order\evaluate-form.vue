<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '评价',
  },
}
</route>
<template>
  <view>
    <view class="p-2 flex flex-col gap-2">
      <!--车辆总评价-->
      <view class="py-5 px-4 bg-white rounded-3 flex flex-col gap-5">
        <view
          class="flex items-center justify-between"
          v-for="scoreItem in scoreFormLabel.carScore"
          :key="scoreItem.label"
        >
          <view class="label text-base text-[#979998]">{{ scoreItem.label }}</view>
          <view class="flex items-center gap-5">
            <wd-rate
              color="rgba(217, 220, 220, 1)"
              active-color="rgba(249, 116, 75, 1)"
              v-model="scoreForm.orderScore[scoreItem.field]"
              size="20px"
              space="26px"
            />
            <text class="text-base">
              {{ ratingTextMap[scoreForm.orderScore[scoreItem.field]] }}
            </text>
          </view>
        </view>
      </view>
      <view
        v-for="drinkItem in scoreForm.drinkScores"
        :key="drinkItem.id"
        class="py-5 px-4 bg-white rounded-3 flex flex-col gap-5"
      >
        <view class="flex items-center gap-2">
          <GoodsImage enablePreview width="56" height="56" :src="drinkItem.goodsUrl" />
          <view class="flex flex-col justify-start h-[56px] gap-1">
            <view class="text-l">{{ drinkItem.goodsName }}</view>
            <view class="text-sm text-[#656666]">{{ drinkItem.pellets }}</view>
          </view>
        </view>
        <view
          class="flex items-center justify-between"
          v-for="scoreItem in scoreFormLabel.drinkScore"
          :key="scoreItem.label"
        >
          <view class="label text-base text-[#979998]">{{ scoreItem.label }}</view>
          <view class="flex items-center gap-5">
            <wd-rate
              color="rgba(217, 220, 220, 1)"
              active-color="rgba(249, 116, 75, 1)"
              v-model="drinkItem[scoreItem.field]"
              size="20px"
              space="26px"
            />
            <text class="text-base">{{ ratingTextMap[drinkItem[scoreItem.field]] }}</text>
          </view>
        </view>
      </view>
      <view>
        <wd-textarea
          custom-class="rounded-t-2"
          v-model="scoreForm.userReview"
          :maxlength="200"
          clearable
          placeholder="说说口味、服务怎么样，给大家参考参考"
        />
        <wd-upload
          :max-size="5 * 1024 * 1024"
          custom-class="bg-white p-2"
          v-model:file-list="scoreForm.userEvaluateFiles"
          @oversize="handleFileOversize"
          multiple
          accept="image"
          :limit="5"
          :action="FILE_UPLOAD_URL"
          @success="handleFileSuccess"
        ></wd-upload>
        <view class="text-sm text-[#979998] bg-white rounded-b-2 p-2 pt-0">
          图片限制上传5张，每张大小限制5M
        </view>
      </view>
    </view>
    <view class="px-4 py-2 bg-white">
      <LAButton type="primary" block size="lg" @click="handleSubmit">发布</LAButton>
    </view>
    <wd-gap safe-area-bottom height="0"></wd-gap>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useUserStore } from '@/store'
import { EvaluationForm } from '@/pages/order/types'
import { scoreFormLabel, ratingTextMap } from '@/pages/evaluate-centre/types'
import { evaluate, FILE_UPLOAD_URL, getOrderDetails } from '@/service/api'
import { useToast } from 'wot-design-uni'

const toast = useToast()
const productOrderDetailInfo = ref([])
const productOrderInfo = ref<any>({})
const useStore = useUserStore()

const scoreForm = ref<EvaluationForm>({
  orderScore: {
    // 服务评分
    serverScore: 5,
    // 出餐效率评分
    efficiencyScore: 5,
    // 配送评分
    deliveryScore: 5,
    // 评价内容
    userReview: '',
    // 图片
    userEvaluateFiles: [],
  },
  drinkScores: [],
  userReview: '',
})
// 事件通道，在onLoad中进行获取
let eventChannel: {
  on: (eventName: string, cb: (...args: any) => void) => void
  emit: (eventName: string, ...args: any) => void
}
// 图片上传成功
const handleFileSuccess = (res: any) => {
  // 如果数量已经5张则提示
  if (scoreForm.value.orderScore.userEvaluateFiles.length >= 5) {
    return toast.show('图片数量不能超过5张')
  }
  const resData = JSON.parse(res.file.response)
  if (!resData.success) {
    return toast.show(resData.message)
  }
  res.file.url = resData.data.fullPath
  scoreForm.value.orderScore.userEvaluateFiles.push(res.file.url)
}
const handleFileOversize = (file: any) => {
  toast.show('图片大小不能超过5M')
}
const handleSubmit = async () => {
  // 处理数据提取images里的url
  const images = scoreForm.value.orderScore.userEvaluateFiles.map((item) => item)

  // 组装接口数据
  const params = {
    sysOrderNum: productOrderInfo.value.sysOrderNum,
    orderEvaluate: {
      carId: productOrderInfo.value.carId,
      carName: productOrderInfo.value.carName,
      areaId: productOrderInfo.value.areaId,
      areaName: productOrderInfo.value.areaName,
      orderUserMobile: productOrderInfo.value.orderUserMobile,
      ...scoreForm.value.orderScore,
      userEvaluateFiles: images,
      userReview: scoreForm.value.userReview,
    },
    orderGoodsEvaluates: scoreForm.value.drinkScores.map((item: any) => {
      return {
        goodsId: item.goodsId,
        sweet: item.sweet,
        temperature: item.temperature,
        cup: item.cup,
        total: item.total,
        tasteScore: item.tasteScore,
        healthScore: item.healthScore,
        costScore: item.costScore,
      }
    }),
  }
  // console.log('提交评价参数', params)

  await evaluate(params).then((res) => {
    if (res.statusCode === 101) {
      // 提交评价
      eventChannel.emit('refresh')
      toast.success('评价成功')
      setTimeout(uni.navigateBack, 500)
    }
  })
}

onLoad(async (options) => {
  const instance = getCurrentInstance().proxy
  // @ts-expect-error - eventChannel
  eventChannel = instance.getOpenerEventChannel()
  // 获取订单数据
  await getOrderDetails({ orderId: options.orderId, type: 'mini' }).then((res: any) => {
    productOrderDetailInfo.value = res.data.productOrderDetailInfo
    productOrderInfo.value = res.data.productOrderInfo
    // scoreForm的scoreForm循环并每一项加入productOrderDetailInfo且多tasteScore: 5,hygieneScore: 5,costPerformanceScore: 5,\
    productOrderDetailInfo.value.forEach((item: any) => {
      const model = {
        cup: item.pellets.split(',')[0],
        temperature: item.pellets.split(',')[1],
        sweet: item.pellets.split(',')[2],
      }
      const score = {
        tasteScore: 5,
        healthScore: 5,
        costScore: 5,
      }
      scoreForm.value.drinkScores.push(Object.assign(item, model, score))
    })
  })
})
</script>

<style scoped>
view {
  line-height: initial;
}
</style>
