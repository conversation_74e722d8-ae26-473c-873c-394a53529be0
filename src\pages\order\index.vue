<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '订单',
  },
}
</route>
<template>
  <view
    style="
      --wot-button-primary-bg-color: #6d3202;
      --wot-drop-menu-line-color: #6d3202;
      --wot-drop-menu-item-color-active: #6d3202;
    "
    class="h-full"
  >
    <!--    v-if="isLogined"-->
    <PageList
      v-if="isLogined"
      ref="pageListRef"
      :requestApi="getOrderList"
      :search="search"
      bottomSpace="160rpx"
    >
      <template #default="{ item, refresh }">
        <view class="p-4 bg-white rounded-xl m-2">
          <view
            @click="toDetails(item.id, item.orderStatus, item.sysOrderNum)"
            class="flex justify-between items-center pb-3 border-b border-b-[#EBEDF1] border-b-solid"
          >
            <view class="flex items-center">
              <view class="text-base text-black font-bold mr-1">{{ item.carName }}</view>
              <view
                class="h-4 text-xs rounded text-[#6d3202] px-1 bg-[#f0eae5]"
                v-if="ORDER_TYPE_MAP[item.orderType]"
              >
                {{ ORDER_TYPE_MAP[item.orderType] }}
              </view>
            </view>
            <view
              class="flex items-center text-sm text-gray-500"
              :style="{ color: statusColorMap[item.orderStatus] }"
            >
              <view>
                {{ orderStatusMap[item.orderStatus] }}
              </view>
              <wd-icon
                name="arrow-right"
                size="16px"
                :color="statusColorMap[item.orderStatus]"
              ></wd-icon>
            </view>
          </view>
          <view
            @click="toDetails(item.id, item.orderStatus, item.sysOrderNum)"
            class="flex justify-between items-center mt-4"
          >
            <view class="flex max-w-64">
              <view
                class="shrink-0 size-14 mr-1 rounded-lg overflow-hidden"
                v-for="img in item.goodsUrls?.slice(0, 4)"
                :key="img"
              >
                <GoodsImage :src="img" width="100%" height="100%" />
              </view>
              <view
                class="ml-3 text-sm text-[#1a1a1a] line-clamp-2"
                v-if="item.goodsNames.length === 1"
              >
                {{ item.goodsNames[0] }}
              </view>
            </view>
            <view class="text-sm">
              <view class="flex justify-end text-[#1A1A1A]">
                <view>￥</view>
                <view class="font-bold">{{ item.payerTotal }}</view>
              </view>
              <view class="text-[#656666] text-right">共{{ item.total }}件</view>
            </view>
          </view>
          <view
            class="flex justify-end mt-4"
            v-if="item.evaluate === 0 && item.orderStatus == OrderStatus.COMPLETED"
          >
            <LAButton
              @click="openEvaluate(item.id, refresh)"
              plain
              custom-style="width:78px"
              type="primary"
            >
              去评价
            </LAButton>
          </view>
        </view>
      </template>
    </PageList>
    <view v-else class="flex flex-col items-center justify-center h-[80vh] gap-4">
      <wd-status-tip image="search" tip="当前未登录，请先登录" />
      <button
        class="bg-[#6D3202] h-10 flex items-center justify-center text-white rounded-full font-bold px-6 text-base"
        open-type="getPhoneNumber"
        @getphonenumber="getUserPhone"
      >
        立即登录
      </button>
    </view>

    <TabBar v-model="activeTab"></TabBar>
  </view>
</template>

<script lang="ts" setup>
import { getOrderList, getUserInfo, loginApi } from '@/service/api'
import TabBar from '@/components/TabBar.vue'
import GoodsImage from '@/components/GoodsImage.vue'
import { useUserStore } from '@/store'
import { getLoginCode } from '@/utils'
import { onShow } from '@dcloudio/uni-app'
import { ORDER_TYPE_MAP, OrderStatus, statusColorMap, orderStatusMap } from '@/types/consumer'
import { useToast } from 'wot-design-uni'

const toast = useToast()
const useStore = useUserStore()
const { setToken, setUserInfo } = useStore
const activeTab = ref('order')
const pageListRef = ref(null)
const isLogined = ref(false)

const openEvaluate = (orderId, refresh) => {
  uni.navigateTo({
    url: `/pages/order/evaluate-form?orderId=${orderId}`,
    // 刷新列表
    ...refresh,
  })
}
watch(
  () => useStore.token,
  (newVal) => (isLogined.value = !!newVal),
  { immediate: true },
)

// 搜索插槽
const search = {
  // 搜索右侧表单
  searchForm: { show: false },
  options: [
    {
      prop: 'orderType',
      title: '全部类型',
      options: Object.entries(ORDER_TYPE_MAP).map(([key, value]) => ({
        label: value,
        value: key,
      })),
    },
    {
      prop: 'orderTime',
      slot: true, // 开启自定义插槽
      slotType: 'date', // 日期
      title: '全部时间',
      customClass: 'order-calendar',
    },
  ],
}

const getUserPhone = async (e) => {
  const code = await getLoginCode()
  // 用户同意登录
  if (e.detail.errMsg === 'getPhoneNumber:ok') {
    const params = { loginCode: code, phoneCode: e.detail.code, loginType: 'normal' }
    // 获取用户token接口
    await loginApi(params).then((res: any) => {
      setToken(res.data.jwtToken)
    })
    const userInfo = await getUserInfo().then((res: any) => {
      return res.data
    })
    setUserInfo(userInfo)
    pageListRef.value?.paging?.reload()
  } else {
    // 提示
    toast.error('授权失败')
  }
}
onShow(() => {
  if (isLogined.value) {
    pageListRef.value?.paging?.reload()
  }
})

// 跳转到详情页
const toDetails = (orderId, orderStatus, sysOrderNum) => {
  switch (orderStatus.toString()) {
    case OrderStatus.WAITING:
      uni.navigateTo({
        url: `/pages/order/unpaid-detail?orderId=${orderId || ''}&sysOrderNum=${sysOrderNum || ''}`,
      })
      break
    case OrderStatus.CANCELLED:
      uni.navigateTo({
        url: `/pages/order/cancel-detail?orderId=${orderId || ''}&sysOrderNum=${sysOrderNum || ''}`,
      })
      break
    case OrderStatus.COMPLETED:
      uni.navigateTo({
        url: `/pages/order/complete-detail?orderId=${orderId || ''}&sysOrderNum=${sysOrderNum || ''}`,
      })
      break
    default:
      uni.navigateTo({
        url: `/pages/order/details?orderId=${orderId || ''}&sysOrderNum=${sysOrderNum || ''}`,
      })
      break
  }
}
</script>
<script lang="ts">
export default { options: { styleIsolation: 'shared' } }
</script>
<style lang="scss" scoped>
.order-calendar {
  :deep(.uni-calendar-item--isDay),
  :deep(.uni-calendar-item--checked) {
    background-color: #6d3202;
    opacity: 1;
    border-radius: 100%;
  }

  :deep(.uni-calendar-item--isDay-text:not(.uni-calendar-item--isDay)) {
    color: #6d3202;
  }
}

:deep(.wd-overlay) {
  z-index: 10000;

  .get-phone-btn {
    width: 100%;
    height: 40px;
    border-radius: 8px;
    font-size: 16px;
    //font-weight: bold;
    //border: 1px solid #d8d8d8;

    &:after {
      border: none;
    }
  }
}

:deep(.order-calendar) {
  .uni-calendar-item--isDay,
  .uni-calendar-item--checked {
    background-color: #6d3202;
    opacity: 1;
    border-radius: 100%;
  }

  .uni-calendar-item--isDay-text:not(.uni-calendar-item--isDay) {
    color: #6d3202;
  }

  .wd-popup {
    text-align: center;
  }

  .wd-button {
    width: 90%;
    margin: 14px 0;
    font-weight: bold;
    font-size: 16px;
    border-radius: 40px !important;
  }
}
</style>
