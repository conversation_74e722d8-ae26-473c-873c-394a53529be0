<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '送货进度' },
}
</route>
<template>
  <view>
    <view class="fixed left-0 top-0 w-full box-border p-2 h-21 z-20">
      <view class="rounded-lg bg-white shadow-md flex flex-col p-4 gap-2">
        <view class="flex justify-between items-center">
          <view class="text-[#1a1a1a] font-bold text-lg">
            <!--  车辆{{ data.carName }}
          {{ makeStatus.toString() === MakeStatus.WAITING ? '将为您提供服务' : '已到达，请取货' }} -->
          </view>
          <view class="text-sm text-[#6d3202]" @click="onback">
            <text class="mr-1">查看订单</text>
            <wd-icon name="arrow-right" size="14px" />
          </view>
        </view>
        <view class="flex gap-2 items-center text-sm">
          <text class="text-[#1A1A1A]">距您500m</text>
          <text class="text-[#6D3202]">车辆到达后请及时取货</text>
        </view>
      </view>
    </view>
    <!-- 全屏地图 -->
    <map
      id="map"
      class="map"
      :longitude="centerLongitude"
      :latitude="centerLatitude"
      :scale="mapScale"
      :markers="markers"
      :polyline="polylines"
      :show-location="true"
      :enable-3D="false"
      :show-compass="false"
      :enable-zoom="true"
      :enable-scroll="true"
      :enable-rotate="false"
      :enable-overlooking="false"
      :enable-satellite="false"
      :enable-traffic="false"
      @callouttap="eventHandlers.onCalloutTap"
    >
      <cover-view slot="callout">
        <cover-view :marker-id="-1">
          <cover-image style="margin-bottom: 4px" :src="Navigation"></cover-image>
        </cover-view>
      </cover-view>
    </map>
  </view>
</template>

<script setup lang="ts">
import { useMap } from '@/hooks/useMap'
import { onLoad } from '@dcloudio/uni-app'
import { onUnmounted } from 'vue'
import MarkerCar from './imgs/MarkerCar.svg?url'
import Navigation from './imgs/Navigation.svg?url'
import MarkerPickupPoint from './imgs/MarkerPickupPoint.svg?url'
import { getSellCarList, getOrderDetails } from '@/service/api'

// 初始化地图 hooks
const {
  centerLongitude,
  centerLatitude,
  mapScale,
  markers,
  polylines,
  createEventHandlers,
  init,
  addPolyline,
  addMarker,
  updateMarker,
  removePolyline,
  clearMarkers,
} = useMap()

const onback = () => uni.navigateBack()

// 订单id
const orderId = ref()
// 订单详情
const orderDetail = ref()
// 取货点marker数据
const pickupMarkerData = ref()

// 获取订单详情信息
function fetchOrderDetail() {
  return getOrderDetails({ orderId: orderId.value, type: 'mini' }).then((res: any) => {
    if (res?.data?.productOrderInfo) orderDetail.value = res?.data?.productOrderInfo
    console.log('orderDetail.value', orderDetail.value, res?.data, res)
  })
}

// 获取奶茶车列表
function fetchCarList() {
  return getSellCarList({ runAreaId: orderDetail.value.areaId.value, carAuth: 1 }).then(
    (res: any) => {
      if (!Array.isArray(res.data?.records)) return

      // 筛选具有经纬度数据的点
      const hasLocationMarkers = res.data.records
        .map((item, index) => {
          const { location, id, carName } = item
          if (!location) return null
          const [longitude, latitude] = location.split(' ')
          const carId = orderDetail.value.carId
          const isDeliveryCar = carId === id
          return {
            label: carName,
            value: id,
            longitude,
            latitude,
            id: index,
            isDeliveryCar,
            // todo 替换为激活样式 marker
            iconPath: isDeliveryCar ? MarkerCar : MarkerCar,
            width: 40,
            borderColor: isDeliveryCar ? '#6d3202' : undefined,
            borderWidth: isDeliveryCar ? 4 : undefined,
            height: 40,
          }
        })
        .filter(Boolean)
      clearMarkers()
      addMarker(pickupMarkerData.value)
      // 奶茶车名称列表
      markers.value.push(...hasLocationMarkers)
    },
  )
}
// 获取轨迹
function fetchPolylinePath() {
  return new Promise((resolve) => {
    resolve(true)
  })
}

// 更新marker 更新polyline
async function updateMap() {
  await fetchCarList()
  const path = await fetchPolylinePath()
  removePolyline(0)
  // @ts-ignore
  const startPoint = markers.value.find((item) => item.isDeliveryCar)
  if (!startPoint) return

  // 添加更新后的线段
  const updatedPolyline = {
    points: [
      {
        longitude: pickupMarkerData.value.longitude,
        latitude: pickupMarkerData.value.latitude,
      },
      {
        longitude: startPoint.longitude,
        latitude: startPoint.latitude,
      },
    ],
    color: '#6d3202',
    width: 3,
    dottedLine: true,
    arrowLine: false,
  }

  addPolyline(updatedPolyline)
}

let intervalId: null | NodeJS.Timeout

onShow(() => {
  fetchOrderDetail().then(() => {
    const { lon, lat } = orderDetail.value
    pickupMarkerData.value = {
      longitude: lon,
      latitude: lat,
      id: -1,
      width: 86,
      height: 58,
      iconPath: MarkerPickupPoint,
      customCallout: {
        display: 'ALWAYS',
      },
    }
    addMarker(pickupMarkerData.value)
  })
  intervalId = setInterval(updateMap, 5000)
})

onHide(() => {
  console.log('执行了xx')
  clearInterval(intervalId)
})

watch(
  () => [centerLongitude.value, centerLatitude.value],
  () => {
    // addTestDottedLine()
  },
)

// 存储当前终点位置和定时器
let currentEndPoint = { longitude: 0, latitude: 0 }
let updateTimer: NodeJS.Timeout | null = null

// 动态更新marker和线段位置的函数
const updateMarkerAndLine = () => {
  if (!centerLongitude.value || !centerLatitude.value) return

  // 生成新的终点位置（在当前终点附近小幅移动）
  const moveStep = 0.0005 // 更小的移动步长，让动画更平滑
  currentEndPoint.longitude += (Math.random() - 0.5) * moveStep
  currentEndPoint.latitude += (Math.random() - 0.5) * moveStep

  // 限制移动范围，不要离起点太远
  const maxDistance = 0.01
  const deltaLng = currentEndPoint.longitude - centerLongitude.value
  const deltaLat = currentEndPoint.latitude - centerLatitude.value

  if (Math.abs(deltaLng) > maxDistance) {
    currentEndPoint.longitude = centerLongitude.value + (deltaLng > 0 ? maxDistance : -maxDistance)
  }
  if (Math.abs(deltaLat) > maxDistance) {
    currentEndPoint.latitude = centerLatitude.value + (deltaLat > 0 ? maxDistance : -maxDistance)
  }

  // 更新marker位置
  updateMarker(1, {
    longitude: currentEndPoint.longitude,
    latitude: currentEndPoint.latitude,
  })

  // 更新polyline - 先清除旧的，再添加新的
  if (polylines.value.length > 0) {
    removePolyline(0) // 移除第一条线段
  }

  // 添加更新后的线段
  const updatedPolyline = {
    points: [
      {
        longitude: centerLongitude.value,
        latitude: centerLatitude.value,
      },
      {
        longitude: currentEndPoint.longitude,
        latitude: currentEndPoint.latitude,
      },
    ],
    color: '#6d3202',
    width: 3,
    dottedLine: true,
    arrowLine: false,
  }

  addPolyline(updatedPolyline)

  console.log('已更新marker和线段位置:', currentEndPoint)
}

// 开始动态更新
const startDynamicUpdate = () => {
  if (updateTimer) return // 避免重复启动

  updateTimer = setInterval(() => {
    updateMarkerAndLine()
  }, 1000) // 每秒更新一次

  console.log('开始动态更新marker和线段')
}

// 停止动态更新
const stopDynamicUpdate = () => {
  if (updateTimer) {
    clearInterval(updateTimer)
    updateTimer = null
    console.log('停止动态更新')
  }
}

// 添加测试虚线的函数
const addTestDottedLine = () => {
  // 检查是否已经获取到位置信息
  if (centerLongitude.value && centerLatitude.value) {
    // 在当前位置附近生成一个终点（偏移约0.01度，大约1km）
    const endLongitude = centerLongitude.value + (Math.random() - 0.5) * 0.01
    const endLatitude = centerLatitude.value + (Math.random() - 0.5) * 0.01

    // 保存当前终点位置
    currentEndPoint = { longitude: endLongitude, latitude: endLatitude }

    // 创建虚线路径
    const testPolyline = {
      points: [
        {
          longitude: centerLongitude.value,
          latitude: centerLatitude.value,
        },
        {
          longitude: endLongitude,
          latitude: endLatitude,
        },
      ],
      color: '#6d3202',
      width: 3,
      dottedLine: true, // 设置为虚线
      arrowLine: false,
    }

    // 添加虚线到地图
    addPolyline(testPolyline)

    // 在终点添加marker，使用MarkerCar图标
    const endMarker = {
      id: 1, // 使用固定ID方便更新
      longitude: endLongitude,
      latitude: endLatitude,
      iconPath: MarkerCar,
      width: 32,
      height: 32,
      // 设置气泡信息
      callout: {
        content: '终点',
        color: '#ffffff',
        fontSize: 12,
        borderRadius: 6,
        bgColor: '#6d3202',
        padding: 8,
        display: 'BYCLICK' as const,
      },
    }

    // 添加marker到地图，marker会自动显示在polyline上方
    addMarker(endMarker)

    console.log('已添加测试虚线和终点标记:', {
      from: { longitude: centerLongitude.value, latitude: centerLatitude.value },
      to: { longitude: endLongitude, latitude: endLatitude },
      markerId: endMarker.id,
    })

    // 延迟启动动态更新，确保marker和polyline已经添加完成
    setTimeout(() => {
      startDynamicUpdate()
    }, 1000)
  } else {
    console.log('位置信息尚未获取，无法添加虚线')
  }
}

const eventHandlers = createEventHandlers({
  onCalloutTap(event, marker) {
    // 点击了取货点marker
    if (event.markerId === -1) {
      const { lat, lon, deliveryAddr } = orderDetail.value
      uni.openLocation({
        latitude: lat,
        longitude: lon,
        name: deliveryAddr,
        address: deliveryAddr,
      })
    }
  },
})

onLoad((options) => {
  orderId.value = options.orderId
  init()
})

// 页面卸载时清理定时器
onUnmounted(() => {
  stopDynamicUpdate()
})
</script>
<style scoped>
.map {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1;
}
</style>
