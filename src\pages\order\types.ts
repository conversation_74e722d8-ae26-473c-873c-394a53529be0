// 文件类型
export type imagesType = {
  url: string
  name: string
}
// 类型定义
export type CarScoreForm = {
  // 服务分
  serverScore: number
  // 出餐效率分
  efficiencyScore: number
  // 配送分
  deliveryScore: number
  // 用户评价
  userReview?: string
  // 评价附件地址
  userEvaluateFiles?: imagesType[]
}
// 单饮品类型定义
export type DrinkScoreForm = {
  // 单饮品id
  skuId: string
  // 口味评分
  tasteScore: number
  // 卫生评分
  hygieneScore: number
  // 性价比评分
  costPerformanceScore: number
}
// 提交表单的总类型
export type EvaluationForm = {
  orderScore: CarScoreForm
  drinkScores: DrinkScoreForm[]
  userReview?: string
}
// 定义评分等级类型
export type RatingLevel = 1 | 2 | 3 | 4 | 5
// 定义评分文本映射类型
export type RatingTextMap = {
  [key in RatingLevel]: string
}
