<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '切换运营区域' },
}
</route>
<template>
  <view>
    <map
      class="w-screen h-screen"
      :polygons="polygons"
      :markers="allMarkers"
      :show-location="true"
      :longitude="centerLongitude"
      :latitude="centerLatitude"
      @callouttap="eventHandlers.onCalloutTap"
    />
    <view class="area-select rounded-tl-xl rounded-tr-xl bg-white py-2">
      <wd-search
        placeholder="搜索运营区域"
        hide-cancel
        placeholder-left
        @change="handleSearch"
        custom-class="bg-transparent!"
      />
      <!-- 列表数据 -->
      <view class="overflow-y-auto flex-1 px-4">
        <view
          v-for="item in filteredList"
          :key="item.id"
          class="py-4 flex justify-between items-center text-sm border-b border-gray-200 border-b-solid"
          @click="goToProductPage(item)"
        >
          <view class="text-sm text-gray-900">{{ item.name }}</view>
          <view class="text-gray-400">距您{{ formatDecimal(item.distance) }}</view>
        </view>
        <wd-gap safe-area-bottom height="0"></wd-gap>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
/**
 * @file 区域搜索组件
 * <AUTHOR>
 * @date 2025/4/3
 */
import { getSellAreaByLocation } from '@/service/api'
import useNativeLocation from '@/hooks/useNativeLocation'
import { useBaseInfoStore } from '@/store'
import { formatDecimal } from '@/utils'
import { useMap } from '@/hooks/useMap'
import MarkerTransparent from './imgs/transparent.svg?url'
import { useCart } from '@/pages/goods-menu/useCart'
const { getRawLocation } = useNativeLocation()
const baseInfoStore = useBaseInfoStore()
const { setAreaInfo, setAreaId } = baseInfoStore

const areaList = ref<ListItem[]>([])
const searchKeyword = ref<string>('')
const cart = useCart()
const {
  polygons,
  allMarkers,
  centerLongitude,
  centerLatitude,
  addPolygon,
  createEventHandlers,
  init,
} = useMap({ showLocation: true, autoLocation: true })

// 过滤后的列表
const filteredList = computed(() => {
  return areaList.value.filter((item) =>
    item.name.toLowerCase().includes(searchKeyword.value.toLowerCase()),
  )
})
// 事件通道，在onLoad中进行获取
let eventChannel: {
  on: (eventName: string, cb: (...args: any) => void) => void
  emit: (eventName: string, ...args: any) => void
}
const goToProductPage = (data) => {
  setAreaInfo({
    distance: ~~data.distance,
    runAreaId: data.id,
    runAreaName: data.name,
  })
  // 切换区域时，清空购物车
  if (data.id !== baseInfoStore.areaId) {
    setAreaId(data.id)
    cart.clearCart()
  }

  eventChannel.emit('selectedArea')
  // 跳转到商品页面
  uni.reLaunch({
    url: `/pages/index/index?selectedArea=${data.id}`,
  })
}
// 处理搜索事件（假设wd-search返回事件对象，值在e.detail中）
const handleSearch = (e) => {
  searchKeyword.value = e.value
}

const eventHandlers = createEventHandlers({
  onCalloutTap(event) {
    const { markerId } = event

    const area = polygons.value.find((item) => item.id === Math.abs(+markerId))
    if (area) {
      goToProductPage(area.item)
    }
  },
})

onMounted(async () => {
  init()
  const location = await getRawLocation()
  const params: any = {}
  if (location?.longitude && location?.latitude) {
    params.location = [location.longitude, location.latitude].join(' ')
  }
  const res = await getSellAreaByLocation(params)
  if (Array.isArray(res.data)) {
    areaList.value = res.data.map((item) => ({ ...item, distance: ~~item.distance })) as ListItem[]
    areaList.value
      .map((item: any) => {
        const coordinates = JSON.parse(item.boxCoordinate)
        if (Array.isArray(coordinates) && coordinates.length > 0) {
          // 转换为地图需要的点格式
          return {
            points: coordinates.map((coord) => ({
              longitude: Number(coord[0]),
              latitude: Number(coord[1]),
            })),
            polygonName: item.name,
            polygonId: item.id,
            item,
          }
        }
        return null
      })
      .filter(Boolean)
      .forEach((item, index) => {
        addPolygon({
          id: index,
          points: item.points,
          strokeColor: '#6d3202',
          fillColor: '#6d320230',
          strokeWidth: 2,
          polygonId: item.polygonId,
          item: item.item,
          callout: {
            content: item.polygonName,
            bgColor: '#ffffff',
            fontSize: 12,
            borderRadius: 4,
            padding: 8,
            color: '#1A1A1A',
            display: 'ALWAYS',
          },
          markerIconPath: MarkerTransparent,
        })
      })
  }
})

onLoad(() => {
  const instance = getCurrentInstance().proxy
  // @ts-expect-error - eventChannel
  eventChannel = instance.getOpenerEventChannel()
})
</script>
<!--wot组件库的样式穿透-->
<script lang="ts">
// 类型定义
export interface ListItem {
  id: string
  name: string
  distance: number
}
export default {
  options: {
    styleIsolation: 'shared',
  },
}
</script>
<style scoped lang="scss">
.area-select {
  max-height: 60vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  position: fixed;
  bottom: 0;
}
</style>
