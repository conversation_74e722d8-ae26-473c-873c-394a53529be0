<route lang="json5" type="page">
{
  style: { navigationBarTitleText: '选择售卖车' },
}
</route>
<template>
  <PageList :requestApi="fetchList" :search="search">
    <template #default="{ item }">
      <wd-card custom-class="mt-2!" custom-title-class="p-0!">
        <wd-cell
          :title="item.code"
          :label="item.address"
          :style="{ '--wot-cell-arrow-color': OnlineStatus[item.status].color }"
          custom-class="rounded-lg pl-0!"
          custom-title-class="font-bold text-lg -mt-1"
          custom-label-class="text-[#1A1A1A]! text-sm!"
        >
          <view class="inline-flex gap-2 justify-between -mr-2 items-center">
            <text class="text-[#1A1A1A] text-sm!">距你{{ formatDecimal(item?.distance) }}</text>
            <text style="color: var(--wot-cell-arrow-color)">
              {{ OnlineStatus[item.status].text }}
            </text>
          </view>
        </wd-cell>
        <wd-button :round="false" block class="plain-orange-button" type="primary" size="large">
          选择商品
        </wd-button>
      </wd-card>
    </template>
  </PageList>
</template>
<script lang="ts" setup>
import { formatDecimal } from '@/utils'
const search = {
  placeholder: '售卖车名称/所在区域',
}

const OnlineStatus = {
  1: { text: '在线', color: 'rgba(0, 194, 144, 1)' },
  0: { text: '离线', color: 'red' },
}

const fetchList = () => {
  return Promise.resolve({
    data: [
      {
        code: 'L0001',
        distance: '100',
        status: 1,
        address: '成都市武侯区富顿地区',
      },
    ],
    total: 0,
  })
}
</script>
