export interface DeviceItem {
  // 设备编码
  code: string
  // 模式名称 人工 自动
  modeText: string
  // 电量值
  battery: number
  // 低电
  lowBattery: boolean
  // 设备当前状态
  status: number | string
  // 状态名称
  statusText: string
  // 是否故障
  isFault: boolean
  // 显示模式名称
  showMode: boolean
  // 是否显示电量标识
  showBattery: boolean
}
export interface StatusItem {
  // 设备状态值
  value: number
  // 状态名称
  label: string
  // 标识
  key: string
}

export type DeviceStatusRowSpace = {
  type: 'status'
  height: number
  replaceFields: { [key in keyof StatusItem]: string }
  data: StatusItem[]
}
export type DeviceRowSpace = {
  type: 'device-space'
  height: number
  replaceFields: { [key in keyof DeviceItem]: string }
  data: DeviceItem[]
}

export interface DeviceSpace {
  title: string
  rows: (DeviceStatusRowSpace | DeviceRowSpace)[]
}

// 挖机状态枚举
export enum BulldozerStatusEnum {
  // 只要不是0和-1都是在线中
  /** 离线*/
  OFFLINE = 0,
  /** 允许泊车*/
  PARKING = 1,
  /** 等待装载*/
  WAIT_LOAD = 2,
  /** 装载中*/
  LOADING = 3,
  /** 空闲中*/
  WAITING = 4,
  /** 故障*/
  FAULTS = -1,
}

// 挖机状态 (颜色可自定义)
export const BulldozerEnum = [
  {
    bg: 'rgba(234, 240, 255)',
    status: BulldozerStatusEnum.OFFLINE,
    color: 'rgba(53, 106, 253, 1)',
    text: '离线',
  },
  {
    bg: 'rgba(234, 240, 255)',
    status: BulldozerStatusEnum.PARKING,
    color: 'rgba(53, 106, 253, 1)',
    text: '允许泊车',
  },
  {
    bg: 'rgba(234, 240, 255)',
    status: BulldozerStatusEnum.WAIT_LOAD,
    color: 'rgba(53, 106, 253, 1)',
    text: '等待装载',
  },
  {
    bg: 'rgba(234, 240, 255)',
    status: BulldozerStatusEnum.LOADING,
    color: 'rgba(53, 106, 253, 1)',
    text: '装载中',
  },
  {
    bg: 'rgba(234, 240, 255)',
    status: BulldozerStatusEnum.WAITING,
    color: 'rgba(53, 106, 253, 1)',
    text: '空闲中',
  },
  {
    bg: 'rgba(234, 240, 255)',
    status: BulldozerStatusEnum.FAULTS,
    color: 'rgba(53, 106, 253, 1)',
    text: '故障',
  },
]

// 矿卡状态枚举
export enum TruckStatusEnum {
  /** 空闲中*/
  TRAIN_READY = 1000,
  /** 运行中*/
  TRAIN_BUSY = 1001,
  /** 充电中*/
  TRAIN_CHARGING = 1002,
  /** 故障中*/
  TRAIN_FAILURE = 1003,
  /** 离线中*/
  TRAIN_OFFLINE = 1006,
}

// 充电桩状态枚举
export enum ChargingPileStatusEnum {
  /** 正常*/
  CHARGING_PILE_READY = '1000',
  /** 使用中*/
  CHARGING_PILE_BUSY = '1001',
  /** 故障中*/
  CHARGING_PILE_FAILURE = '1002',
  /** 测试中*/
  CHARGING_PILE_TEST = '1003',
  /** 离线中*/
  CHARGING_PILE_OFFLINE = '1004',
}

// 破碎站状态枚举
export enum CrusherStatusEnum {
  /** 离线*/
  OFFLINE = 0,
  /** 允许卸矿*/
  ALLOW_UNLOAD = 1,
}

// 驾驶舱状态枚举
export enum CockpitStatusEnum {
  /** 离线*/
  OFFLINE = 0,
  /** 空闲*/
  IDLE = 1,
}
