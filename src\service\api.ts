import { http } from '@/utils/http'
/** 登录 */
export const loginApi = (params: any) => {
  return http({
    url: '/miniapi-server/app/auth/nonceLogin',
    method: 'POST',
    data: params,
  })
}

// 消费者用户401后重新登录
export const reLoginUrl = '/miniapi-server/app/auth/silentLogin'
export const reLogin = (params: any) => {
  return http({
    url: reLoginUrl,
    method: 'POST',
    data: params,
  })
}

/** 发送验证码 */
export const sendVerificationCode = (params: any) => {
  return http({
    url: '/usercenter-server/system/systemSmsRecord/sendVerificationCode',
    method: 'POST',
    data: params,
  })
}
// 重置密码
export const editPassword = (params: any) => {
  return http({
    url: '/usercenter-server/system/user/passwordChange',
    method: 'POST',
    data: params,
  })
}

/** 获取当前登录用户的信息 */
export const getUserInfo = () => {
  return http({
    url: '/usercenter-server/system/user/getCurrentDetailUser',
    method: 'POST',
  })
}

// 通用-查询车辆信息
export const getCarInfoById = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceSellCar/queryCarInfoByCarId',
    method: 'POST',
    data: params,
  })
}

// 获取消息列表
export const getMsgList = (params: any) => {
  return http({
    url: '/usercenter-server/system/msg/listQueryByPage',
    method: 'POST',
    data: params,
  })
}

// 文件上传
export const FILE_UPLOAD_URL = '/fileManage-server/upload/smallFile'

// 消费者-获取订单列表
export const getOrderList = (query: any) => {
  return http({
    url: '/product-server/product/productOrder/queryList',
    method: 'GET',
    query,
  })
}

// 消费者-获取订单详情
export const getOrderDetails = (query: any) => {
  return http({
    url: '/product-server/product/productOrder/orderDetail',
    method: 'GET',
    query,
  })
}

// 消费者-二次支付
export const payOrder = (query: any) => {
  return http({
    url: '/product-server/product/productOrder/pay',
    method: 'GET',
    query,
  })
}

// 消费者-动态计算购物车商品价格
export const calculateLowestPrice = (query: any) => {
  return http({
    url: '/product-server/product/productPromotionalActivities/calculateLowestPrice',
    method: 'POST',
    data: query,
    header: {
      'content-type': 'application/json;charset=UTF-8',
    },
  })
}

/* 商家端接口 */

// 获取售卖车列表
export const getSellCarList = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceSellCar/listQueryByPage',
    method: 'POST',
    data: params,
  })
}
// 获取售卖车列表搜索
export const getSellCarListSelect = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceSellCar/listQueryByMap',
    method: 'POST',
    data: params,
  })
}
// 获取售卖车详情
export const getSellCarDetail = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceSellCar/queryCarInfoByCarId',
    method: 'POST',
    data: params,
  })
}
// 获取距离区域中心点距离
export const getDistanceToAreaCenter = (params: any) => {
  return http({
    url: '/dispatch-server/dispatch/map/getDispatchMapDistance',
    method: 'POST',
    data: params,
  })
}

// 获取制作状态 车辆状态
export const getCarStatus = (params: any) => {
  return http({
    url: '/product-server/product/productOrder/queryOrderWait',
    method: 'POST',
    data: params,
  })
}
// 用户评论
export const evaluate = (params: any) => {
  return http({
    url: '/product-server/product/productOrderEvaluate/evaluate',
    method: 'POST',
    data: params,
    header: {
      'content-type': 'application/json;charset=UTF-8',
    },
  })
}
// 获取未支付订单数量和未评价订单数量
export const getNotPayCountOrNotEvaluate = (params: any) => {
  return http({
    url: '/product-server/product/productOrder/notPayCountOrNotEvaluate',
    method: 'GET',
    data: params,
  })
}
// 获取已评价订单列表
export const getAlreadyEvaluateList = () => {
  return http({
    url: '/product-server/product/productOrderEvaluate/alreadyEvaluateMiniList',
    method: 'GET',
  })
}
/* 运维端接口 */
// 标记当前目标点
export const markTargetPoint = (params: any) => {
  return http({
    url: '/dispatch-server/dispatch/pointManage/addPoint',
    method: 'POST',
    data: params,
  })
}

// 编辑目标点
export const editTargetPoint = (params: any) => {
  return http({
    url: '/dispatch-server/dispatch/pointManage/save',
    method: 'POST',
    data: params,
  })
}
// 获取地图区域列表
export const getMapAreaList = (params: any) => {
  return http({
    url: '/dispatch-server/dispatch/map/pagelist',
    method: 'POST',
    data: params,
  })
}
// 获取目标点列表
export const getTargetPointList = (params: any) => {
  return http({
    url: '/dispatch-server/dispatch/pointManage/pagelist',
    method: 'POST',
    data: params,
  })
}
// 删除目标点
export const deleteTargetPoint = (params: any) => {
  return http({
    url: '/dispatch-server/dispatch/pointManage/deleteById',
    method: 'DELETE',
    data: params,
  })
}
/* 消费者端接口 */
// 根据车辆id查询商品列表
export const getGoodsListByCar = (params: any) => {
  return http({
    url: '/product-server/product/productGoods/listQueryByCarId',
    method: 'POST',
    data: params,
  })
}

// 根据车辆id查询商品列表
export const getCartGoodsForUser = (params: any) => {
  return http({
    url: '/product-server/product/productGoods/queryLoadingConfigurationByUser',
    method: 'POST',
    data: params,
  })
}

// 创建订单
export const createOrder = (params: any) => {
  return http({
    url: '/product-server/product/productOrder/createOrder',
    method: 'POST',
    data: params,
    header: {
      'content-type': 'application/json;charset=UTF-8',
    },
  })
}
// 地图首页获取附近的车辆
export const getNearbyVehicleList = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceSellCar/consumersQueryCardPoint',
    method: 'GET',
    data: params,
  })
}
// 获取配送点位列表数据
export const getDeliveryPointList = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceSellCar/queryDeliveryPoint',
    method: 'GET',
    data: params,
  })
}

/* ------------------------------------------------------------------ */
/* =================================消费者================================== */
// 查询广告
export const fetchQueryAdvertises = (params: any) => {
  return http({
    url: '/dispatch-server/dispatch/adTaskManage/getAdTaskMaterialDtoByMap',
    method: 'POST',
    data: params,
  })
}
// 根据商品id和机器id查询商品详情
export const fetchMenuDetailById = (params: any) => {
  return http({
    url: '/product-server/product/productGoods/getProductGoodsDtoByMap',
    method: 'POST',
    data: params,
  })
}

// 根据机器id获取商品菜单信息
export const fetchMenuListByCarId = (params: any) => {
  return http({
    url: '/product-server/product/menuManage/getMenuListByCarId',
    method: 'POST',
    data: params,
  })
}

// 根据区域id获取商品菜单信息
export const fetchMenuListByAreaId = (params: any) => {
  return http({
    url: '/product-server/product/menuManage/getMenuListByAreaId',
    method: 'POST',
    data: params,
  })
}

// 获取系统员工列表
export const fetchEmployeeList = (params: any) => {
  return http({
    url: '/usercenter-server/system/employee/getEmployeeList',
    method: 'POST',
    data: params,
  })
}

// 根据定位获取售卖区域（根据距离排序）
export const getSellAreaByLocation = (params: any) => {
  return http({
    url: '/dispatch-server/dispatch/map/listQueryByMapOrderDistance',
    method: 'POST',
    data: params,
  })
}

// 根据定位以及取货时间获取所有的取货点（根据距离排序）
export const getDeliveryPointByLocation = (params: any) => {
  return http({
    url: '/dispatch-server/dispatch/pointManage/listQueryPointByMap',
    method: 'POST',
    data: params,
  })
}

// 根据sign获取订单详情
export const getOrderDetailBySign = (params: any) => {
  return http({
    url: '/product-server/product/productOrder/localSkipQuery',
    method: 'GET',
    data: params,
  })
}

// 立即开柜
export const openBoxDoor = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceSellCar/openBoxDoor',
    method: 'POST',
    data: params,
  })
}

// 待付款订单倒计时为0，重新刷新订单状态
export const refreshOrderStatus = (params: any) => {
  return http({
    url: '/product-server/product/productOrder/getDelayedMessage',
    method: 'POST',
    data: params,
  })
}

// 轮询接口用于判断用户是否活跃，控制车辆是否可以运行
export const refreshCarWaitTime = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceSellCar/refreshCarWaitTime',
    method: 'POST',
    data: params,
  })
}

// 查询我的优惠券
export const fetchCouponList = (params: any) => {
  return http({
    url: '/product-server/product/productCoupon/queryMiniList',
    method: 'GET',
    data: params,
  })
}
// 领券中心
export const fetchCouponCenter = (params: any) => {
  return http({
    url: '/product-server/product/productCoupon/queryUnclaimedList',
    method: 'GET',
    data: params,
  })
}
// 领券
export const fetchReceiveCoupon = (params: any) => {
  return http({
    url: '/product-server/product/productCoupon/collect',
    method: 'GET',
    data: params,
  })
}
