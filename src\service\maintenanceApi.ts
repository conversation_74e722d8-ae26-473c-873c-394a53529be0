import { http } from '@/utils/http'
// 运维端api

// 运维端-查询首页待办事项
export const getPending = () => {
  return http({
    url: '/wcs-server/wcs/deviceSellCar/middlePlatformPending',
    method: 'POST',
  })
}
// 补料-分页查询奶茶车列表
export const getSellCarList = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceSellCar/listQueryByPage',
    method: 'POST',
    data: params,
  })
}
// 补料-查询奶茶车列表
export const getSellCarListAll = (params?: any) => {
  return http({
    url: '/wcs-server/wcs/deviceSellCar/queryListForNotDelete',
    method: 'POST',
    data: params,
  })
}
// 补料-查询元器件列表
export const getComponentList = (params: any) => {
  return http({
    url: '/product-server/product/componentIngredient/listProductRestockVOByMap',
    method: 'POST',
    data: params,
  })
}
// 补料-根据原料类别id查询原料
export const getIngredientList = (params: any) => {
  return http({
    url: '/product-server/product/productIngredient/listQueryByMap',
    method: 'POST',
    data: params,
  })
}
// 补料-提交修改的原料
export const restockSubmit = (params: any) => {
  return http({
    url: '/wcs-server/device/restockRecord/restockSubmit',
    method: 'POST',
    data: params,
    header: {
      'Content-Type': 'application/json',
    },
  })
}
// 维修工单-查询维修工单列表
export const getRepairWorkOrderList = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceRepairWorkOrder/listQueryByPage',
    method: 'POST',
    data: params,
  })
}
// 查询(维修工单/维修记录/保养工单/保养记录)图片列表
export const getRepairWorkOrderFileList = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceRepairMaintainFile/list',
    method: 'POST',
    data: params,
  })
}
// 查询(维修工单/维修记录/保养工单/保养记录)评价记录
export const getEvaluateInfo = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceEvaluationInfo/queryInfoByTransactionId',
    method: 'POST',
    data: params,
  })
}
// 维修工单-查询维修工单详情
export const getRepairWorkOrderDetail = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceRepairWorkOrder/myGetById',
    method: 'POST',
    data: params,
  })
}
// 维修工单-新增维修工单
export const saveWorkOrder = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceRepairWorkOrder/mySave',
    method: 'POST',
    data: params,
  })
}
// 维修工单-查询维修记录列表
export const getRepairRecordList = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceRepairRecord/myList',
    method: 'POST',
    data: params,
  })
}
// 维修工单-查询维修经验记录
export const getRepairExperienceRecordList = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceRepairExperienceRecord/list',
    method: 'POST',
    data: params,
  })
}
// 维修工单-填写维修结果
export const modifyWorkOrder = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceRepairWorkOrder/modifyWorkOrder',
    method: 'POST',
    data: params,
  })
}
// 维修工单-转让工单
export const transferWorkOrder = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceRepairWorkOrder/transferWorkOrder',
    method: 'POST',
    data: params,
  })
}
// 维修工单-关闭工单
export const closeWorkOrder = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceRepairWorkOrder/closeWorkOrder',
    method: 'POST',
    data: params,
  })
}
// 维修经验库-查询维修经验
export const getRepairExperienceList = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceRepairExperience/listQueryByPage',
    method: 'POST',
    data: params,
  })
}
// 维修经验库-保存维修经验
export const saveRepairExperienceRecord = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceRepairExperienceRecord/save',
    method: 'POST',
    data: params,
  })
}
// 维修经验库-删除维修经验
export const deleteRepairExperienceRecord = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceRepairExperienceRecord/deleteById',
    method: 'DELETE',
    data: params,
  })
}
// 保养工单-查询所有保养周期
export const getAllCycleList = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceMaintenanceKnowledge/queryAllCycleList',
    method: 'POST',
    data: params,
  })
}
// 保养工单-查询保养工单列表
export const getMaintainWorkOrderList = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceMaintenanceWorkOrder/listQueryByPage',
    method: 'POST',
    data: params,
  })
}
// 保养工单-转让工单
export const transferMaintainWorkOrder = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceMaintenanceWorkOrder/transferWorkOrder',
    method: 'POST',
    data: params,
  })
}
// 保养工单-派工
export const assignPerson = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceMaintenanceWorkOrder/assignPerson',
    method: 'POST',
    data: params,
  })
}
// 保养工单-查询保养项目
export const getMaintenancePosition = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceWorkOrderKnowledge/getMaintenancePosition',
    method: 'POST',
    data: params,
  })
}
// 保养工单-查询保养工单详情
export const getMaintainWorkOrderDetail = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceMaintenanceWorkOrder/getDetailById',
    method: 'POST',
    data: params,
  })
}
// 保养工单-填写保养结果
export const modifyMaintainWorkOrder = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceMaintenanceWorkOrder/modifyWorkOrder',
    method: 'POST',
    data: params,
  })
}
// 保养工单-查询保养记录列表
export const getMaintainRecordList = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceMaintenanceRecord/myList',
    method: 'POST',
    data: params,
  })
}
// 保养经验库-查询保养经验
export const getMaintainExperienceList = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceMaintenanceKnowledge/listQueryByPage',
    method: 'POST',
    data: params,
  })
}
// 获取所有人员信息
export const getUserInfoList = (params: any) => {
  return http({
    url: '/usercenter-server/system/user/getUserInfoList',
    method: 'POST',
    data: params,
  })
}
// 补料-补料工单列表
export const getRestockList = (params: any) => {
  return http({
    url: '/wcs-server/device/restockWorkOrder/listQueryByPage',
    method: 'POST',
    data: params,
  })
}
// 补料-补料工单详情
export const getRestockDetail = (params: any) => {
  return http({
    url: '/wcs-server/device/restockWorkOrder/getWorkOrderDetailById',
    method: 'POST',
    data: params,
  })
}
// 补料-关闭工单
export const closeRestock = (params: any) => {
  return http({
    url: '/wcs-server/device/restockWorkOrder/closeWorkOrder',
    method: 'POST',
    data: params,
  })
}
// 补料-派工/转让工单
export const dispatchRestock = (params: any) => {
  return http({
    url: '/wcs-server/device/restockWorkOrder/transferWorkOrder',
    method: 'POST',
    data: params,
  })
}

// 清理-提交清理
export const submitClean = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceMachineComponent/clear',
    method: 'POST',
    data: params,
    header: {
      'Content-Type': 'application/json',
    },
  })
}
// 清理-查询车辆桶
export const getCleanBucketList = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceMachineComponent/queryList',
    method: 'GET',
    data: params,
  })
}

// 清理-查询清理工单列表
export const getCleanList = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceClearWorkOrder/queryList',
    method: 'GET',
    data: params,
  })
}

// 清理-派工
export const dispatchClean = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceClearWorkOrder/createOrUpdate',
    method: 'POST',
    data: params,
    header: {
      'Content-Type': 'application/json',
    },
  })
}
// 清理-转让工单
export const transferClean = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceClearWorkOrder/transfer',
    method: 'GET',
    data: params,
  })
}
// 清理-关闭工单
export const closeClean = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceClearWorkOrder/close',
    method: 'GET',
    data: params,
  })
}

// 清理-获取派工人列表
export const getCleanWorkerList = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceClearWorkOrder/queryUserList',
    method: 'GET',
    data: params,
  })
}

// 清理-获取负责人列表
export const getCleanLeaderList = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceClearWorkOrder/queryPicList',
    method: 'GET',
    data: params,
  })
}

// 清理-获取车辆列表
export const getCleanCarList = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceClearWorkOrder/queryCarList',
    method: 'GET',
    data: params,
  })
}
// 售卖车-原料校验列表
export const getOutletDebugList = (params: any) => {
  return http({
    url: '/product-server/product/componentIngredient/listQueryCurrentByMap',
    method: 'POST',
    data: params,
  })
}
// 售卖车-原料校验列表2
export const getOutletDebugList2 = (params: any) => {
  return http({
    url: '/product-server/product/componentIngredient/listQueryCurrentByMapForMini',
    method: 'POST',
    data: params,
  })
}

// 售卖车-原料校验提交
export const submitOutletDebug = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceComponentAdjust/save',
    method: 'POST',
    data: params,
  })
}

// 呼叫车辆
export const callCar = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceSellCar/callingCar',
    method: 'POST',
    data: params,
  })
}

// 恢复运营
export const recoverCar = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceSellCar/resumptionOperate',
    method: 'POST',
    data: params,
  })
}

// 目标点列表
export const getPointList = (params: any) => {
  return http({
    url: '/dispatch-server/dispatch/pointManage/listQueryByMap',
    method: 'POST',
    data: params,
  })
}

// 远程制动
export const remoteBrake = (params: any) => {
  return http({
    url: '/wcs-server/wcs/deviceSellCar/remoteBrake',
    method: 'POST',
    data: params,
  })
}
