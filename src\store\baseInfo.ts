import { defineStore } from 'pinia'
import { ref } from 'vue'
import { getCarStatus, getDistanceToAreaCenter, getSellCarDetail } from '@/service/api'

// Define interface for area info
interface AreaInfo {
  runAreaId?: string
  runAreaName?: string
  distance?: number
}

interface CarInfo {
  // 当前制作的饮品是自己的时候 订单详情id 用于查询订单使用
  orderDetailId?: string
  // 距离
  distance?: number
  // 车辆运行区域名称
  runAreaName?: string
  // 车辆名称
  carName?: string
  // 车辆设备码
  status?: number
  // 订单状态提示文字
  orderStatus?: string
  // 订单状态
  orderStatusCode?: 'waitAhead' | 'underProduction' | 'underMaintenance'
}

export const useBaseInfoStore = defineStore('baseInfo', () => {
  // 车辆id
  const carId = ref<string>('')
  // 售卖区域id
  const areaId = ref<string>('')
  // 小程序来源： 扫码进  线上下单 app下单
  const referer = ref<'scan' | 'remote' | 'app'>('remote')
  // 扫码参数
  const scanParams = ref<{ carId: string; timestamp: number }>({ carId: '', timestamp: 0 })

  // 售卖车信息
  const carInfo = ref<CarInfo>({})
  // 售卖区域信息
  const areaInfo = ref<AreaInfo>({})
  // 是否正在加载
  const loading = ref<boolean>(false)
  // carInfo 数据是否已加载
  const carInfoLoaded = ref<boolean>(false)
  // areaInfo 数据是否已加载
  const areaInfoLoaded = ref<boolean>(false)
  // 设置售卖车ID
  const setCarId = async (id: string) => {
    carId.value = id
  }
  // 设置售卖区域ID
  const setAreaId = (id: string) => {
    areaId.value = id
  }

  // 设置小程序来源
  const setReferer = (ref: 'scan' | 'remote' | 'app') => {
    referer.value = ref
  }

  // 设置扫码参数
  const setScanParams = (params: { carId: string; timestamp: number }) => {
    scanParams.value = params
  }

  async function updateOrderStatus() {
    await getCarStatus({ carId: carId.value }).then((res) => {
      if (res.success) {
        const { orderDetailId, type, cupNumber } = res.data as any
        console.log('orderDetailId, type, cupNumber12311:', orderDetailId, type, cupNumber)
        carInfo.value.orderDetailId = orderDetailId
        carInfo.value.orderStatusCode = type
        switch (type) {
          case 'waitAhead':
            carInfo.value.orderStatus =
              cupNumber > 0 ? `前方${cupNumber}杯制作中` : '无排队，下单后立即制作'
            break
          case 'underProduction':
            carInfo.value.orderStatus = `您有一杯制作中`
            break
          case 'underMaintenance':
            carInfo.value.orderStatus = '维护中'
            break
        }
      }
    })
  }

  // 从API获取售卖车信息
  const fetchCarInfo = async (location?: { latitude: number; longitude: number }) => {
    if (!carId.value) return

    loading.value = true
    try {
      const params: Record<string, any> = {
        carId: carId.value,
      }
      if (location) {
        params.lat = location.latitude
        params.lon = location.longitude
      }
      await getSellCarDetail(params).then((res: any) => {
        if (res.success) {
          carInfo.value = res.data
          areaInfo.value = {
            runAreaId: res.data.runAreaId,
            runAreaName: res.data.runAreaName,
          }
          setAreaId(res.data.runAreaId)
        }
      })

      await updateOrderStatus()

      carInfoLoaded.value = true
    } catch (error) {
      console.error('Failed to fetch car info:', error)
    } finally {
      loading.value = false
    }
  }

  // 根据位置获取售卖区域信息
  const fetchAreaInfoByLocation = async (location: { latitude: number; longitude: number }) => {
    loading.value = true
    try {
      const params: Record<string, any> = {}
      if (location) {
        params.location = `${location.longitude} ${location.latitude}`
      }
      getDistanceToAreaCenter(params).then((res: any) => {
        const { id, name, distance, rangeFlag } = res.data || {}

        carInfo.value = {
          runAreaName: name,
          distance: ~~distance,
        }

        areaInfo.value = {
          runAreaId: id,
          runAreaName: name,
          distance: ~~distance,
        }
        setAreaId(id)
        areaInfoLoaded.value = true
      })
    } catch (error) {
      console.error('Failed to fetch area info:', error)
    } finally {
      loading.value = false
    }
  }

  // 选择区域页面设置区域信息
  const setAreaInfo = (info: AreaInfo) => {
    areaInfo.value = info
    carInfo.value = {
      runAreaName: info.runAreaName,
      distance: info.distance,
    }
    areaInfoLoaded.value = true
  }

  // 重置store数据
  const reset = () => {
    carId.value = ''
    areaId.value = ''
    carInfo.value = {}
    areaInfo.value = {}
    referer.value = 'remote'
    areaInfoLoaded.value = false
    carInfoLoaded.value = false
  }

  return {
    // 状态
    carId,
    areaId,
    referer,
    carInfo,
    areaInfo,
    carInfoLoaded,
    areaInfoLoaded,
    loading,

    // 操作
    setCarId,
    setAreaId,
    setAreaInfo,
    fetchCarInfo,
    fetchAreaInfoByLocation,
    reset,
    setReferer,
    setScanParams,
    scanParams,
    updateOrderStatus,
  }
})
