// App.vue onShow 事件小程序在打开设置页面、打开相机时会被错误触发，所以增加此方法为了规避在上述条件下 App.vue OnShow 触发的问题
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useGlobalStore = defineStore(
  'global',
  () => {
    // 小程序位置授权状态
    // 是否授权 未授权 拒绝授权 已授权
    const permission = ref<'unperssion' | 'denied' | 'granted'>('unperssion')
    // 标记是否需要跳转
    const isJumpSelecting = ref<boolean>(true)
    // tabbar高度
    const tabbarHeight = ref(0)

    const setJumpSelecting = (val) => {
      isJumpSelecting.value = val
    }

    const setPermission = (value: 'unperssion' | 'denied' | 'granted') => {
      permission.value = value
    }

    const setTabbarHeight = (height: number) => {
      tabbarHeight.value = height
    }

    return {
      isJumpSelecting,
      setJumpSelecting,
      permission,
      setPermission,
      tabbarHeight,
      setTabbarHeight,
    }
  },
  {
    persist: true,
  },
)
