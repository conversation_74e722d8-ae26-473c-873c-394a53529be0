import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useUserStore = defineStore(
  'user',
  () => {
    const userInfo = ref<Partial<IUserInfo>>({})
    const token = ref<string>('')
    const setUserInfo = (val: Partial<IUserInfo>) => {
      userInfo.value = { ...userInfo.value, ...val }
    }
    // Set Token
    const setToken = (val: string) => {
      token.value = val
    }
    const clearUserInfo = () => {
      userInfo.value = {}
      token.value = ''
    }

    const isLogined = computed(() => !!token.value)

    const isMaintenance = computed(() => {
      return userInfo.value?.roleCode === 'maintenance'
    })

    const isConsumer = computed(() => {
      return userInfo.value?.roleCode === 'consumer'
    })

    return {
      userInfo,
      token,
      setToken,
      setUserInfo,
      clearUserInfo,
      isLogined,
      isMaintenance,
      isConsumer,
    }
  },
  {
    persist: true,
  },
)
