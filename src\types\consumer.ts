// 订单制作状态枚举
export enum MakeStatus {
  WAITING = '0', // 待制作
  PROCESSING = '1', // 制作中
  PARTIAL = '2', // 部分制作完成
  COMPLETED = '3', // 全部制作完成
  CANCELLED = '4', // 已取消
}

// 订单状态
export enum OrderStatus {
  WAITING = '0', // 待支付
  PAID = '1', // 已支付
  COMPLETED = '2', // 已完成
  CANCELLED = '3', // 已取消
}

// 单杯订单状态
export enum SingleOrderStatus {
  PENDING = '0', // 待制作
  IN_PROGRESS = '1', // 制作中
  FINISHED = '2', // 制作完成
  PICKED_UP = '3', // 取货完成
  CANCELLED = '4', // 已取消
  TIMEOUT = '5', // 超时丢弃
}

// 订单来源 0: 线上 1: 扫码 2: 触摸屏
export const ORDER_TYPE = { remote: '0', scan: '1', app: '2' }
// 订单来源 0: 线上 1: 现场 2: 本机
export const ORDER_TYPE_MAP = { 0: '线上购', 1: '现场购', 2: '本机购' }
// 订单状态颜色
export const statusColorMap = {
  [OrderStatus.WAITING]: '#F25555',
  [OrderStatus.PAID]: '#00C290',
  [OrderStatus.COMPLETED]: '#656666',
  [OrderStatus.CANCELLED]: '#656666',
}
// 订单状态
export const orderStatusMap = {
  [OrderStatus.WAITING]: '待付款',
  [OrderStatus.PAID]: '待取货',
  [OrderStatus.COMPLETED]: '已完成',
  [OrderStatus.CANCELLED]: '已取消',
}

export const PAY_TYPE = { 0: '微信支付' }
