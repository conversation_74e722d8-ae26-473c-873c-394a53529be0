/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
// 全局要用的类型放到这里
type IResData<T> = {
  statusCode: number
  message: string
  data: T
  jwtToken?: string
  success: boolean
  [key: string]: any
}
enum Role {
  // 用户
  USER = 'user',
  // 运维
  MAINTENANCE = 'maintenance',
  // 商家
  MERCHANT = 'merchant',
}
// uni.uploadFile文件上传参数
type IUniUploadFileOptions = {
  file?: File
  files?: UniApp.UploadFileOptionFiles[]
  filePath?: string
  name?: string
  formData?: any
}
type IUserInfo = {
  id: string
  userName?: string
  passWord?: string
  systemEmployeeId?: string
  type?: any
  sort?: any
  status?: string
  adminCode?: string
  wxOpenId?: string
  flag?: any
  remark?: string
  createAccount?: string
  createDate?: string
  roles?: any
  orgCode?: string
  orgId?: string
  orgName?: string
  adminRole?: boolean
  roleName?: string
  roleCode?: string
  employee?: any
  roleId?: string
  warehouseName?: string
  statusName?: string
  mobile?: string
  gender?: string
  major?: string
  systemOrgId?: string
  age?: number
  cerfificateType?: string
  identity?: string
  email?: string
  address?: string
  job?: string
  employeeName?: string
  genderName?: string
  cerfificateTypeName?: string
  tenantName?: string
}
interface TabItem {
  icon: string
  text: string
  pagePath: string
  badge?: number
}

declare type Recordable<T = any> = Record<string, T>
