// 联系客服
export const contactStaff = () => {
  const phone = import.meta.env.VITE_CUSTOMER_SERVICE_PHONE
  const res = uni.getSystemInfoSync()
  // ios系统默认有个模态框
  if (res.platform === 'ios') {
    uni.makePhoneCall({ phoneNumber: phone })
  } else {
    // 安卓手机手动设置一个showActionSheet
    uni.showActionSheet({
      itemList: [phone, '呼叫'],
      success: function (res) {
        if (res.tapIndex === 1) {
          uni.makePhoneCall({ phoneNumber: phone })
        }
      },
    })
  }
}

// 手机号脱敏
export const phoneMask = (phone: string) => {
  return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3')
}
