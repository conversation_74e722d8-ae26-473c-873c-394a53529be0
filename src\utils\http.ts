import { CustomRequestOptions } from '@/interceptors/request'
import { useUserStore } from '@/store'
import { ResponseStatus } from './httpEnum'
import { reLogin, reLoginUrl } from '@/service/api'
import { getLoginCode } from '.'

// 添加刷新 token 的状态标志和等待队列
let isRefreshing = false
let refreshSubscribers: Array<(token: string) => void> = []

// 将请求添加到等待队列
const addSubscriber = (callback: (token: string) => void) => {
  refreshSubscribers.push(callback)
}

// 执行等待队列中的请求
const onRefreshed = (token: string) => {
  refreshSubscribers.forEach((callback) => callback(token))
  refreshSubscribers = []
}

// 添加重新登录并重试请求的函数
const handleReLoginAndRetry = async <T>(options: CustomRequestOptions): Promise<IResData<T>> => {
  // 返回一个新的 Promise，用于处理重试逻辑
  return new Promise<IResData<T>>((resolve, reject) => {
    // 如果已经在刷新 token，将当前请求添加到等待队列
    if (isRefreshing) {
      addSubscriber((token) => {
        if (!token) {
          reject(new Error('自动重新登录失败'))
          return
        }
        // token 刷新后，使用新 token 重试请求
        if (options.header) {
          options.header.Authorization = `Bearer ${token}`
        } else {
          options.header = { Authorization: `Bearer ${token}` }
        }
        http<T>(options).then(resolve).catch(reject)
      })
      return
    }

    // 标记正在刷新 token
    isRefreshing = true

    // 执行刷新 token 的逻辑
    const refreshTokenRequest = async () => {
      try {
        const userStore = useUserStore()
        const phone = userStore.userInfo?.mobile
        if (!phone) {
          throw new Error('手机号为空')
        }
        const loginCode = await getLoginCode()
        const reLoginRes: any = await reLogin({ loginCode, phone })

        // 如果重新登录成功，更新用户信息
        if (reLoginRes.statusCode === 200 && reLoginRes.data) {
          const token = reLoginRes.data.jwtToken
          userStore.setToken(token)

          // 通知等待队列中的请求继续执行
          onRefreshed(token)

          // 更新当前请求的 token 并重试
          if (options.header) {
            options.header.Authorization = `Bearer ${token}`
          } else {
            options.header = { Authorization: `Bearer ${token}` }
          }

          // 重试当前请求
          http<T>(options).then(resolve).catch(reject)
        } else {
          throw new Error('自动重新登录失败')
        }
      } catch (error) {
        // 自动登录失败，清除用户信息并跳转到登录页
        const userStore = useUserStore()
        userStore.clearUserInfo()
        // 通知所有等待的请求，登录失败
        refreshSubscribers.forEach((callback) => callback(''))
        refreshSubscribers = []

        reject(error)
      } finally {
        // 重置刷新状态
        isRefreshing = false
      }
    }

    refreshTokenRequest()
  })
}

export const http = <T>(options: CustomRequestOptions) => {
  // 1. 返回 Promise 对象
  return new Promise<IResData<T>>((resolve, reject) => {
    const userStore = useUserStore()

    uni.request({
      header: {
        'content-type': 'application/x-www-form-urlencoded',
      },
      ...options,
      dataType: 'json',
      // 响应成功
      success(res) {
        // 对响应中的状态码进行判断执行对应的操作
        const response: IResData<T> = res.data as IResData<T>
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        const responseStatus = response.statusCode || response?.data?.statusCode

        // 处理登录失效的情况
        const isLoginExpired = [
          ResponseStatus.USER_NEED_AUTHORITIES,
          ResponseStatus.TOKEN_IS_BLACKLIST,
          ResponseStatus.LOGIN_IS_OVERDUE,
          ResponseStatus.FEIGN_GET_USER_FAILED,
        ].includes(responseStatus)
        if (isLoginExpired) {
          if (userStore.isMaintenance) {
            // 非user角色，执行原有逻辑
            userStore.clearUserInfo()
            uni.showToast({
              icon: 'none',
              title: (res.data as IResData<T>).message || '登录失效，请重新登录',
            })
            uni.navigateTo({ url: '/pages/login/index' })
            reject(res)
          } else {
            // 如果请求的是重新登录的接口，则不进行重试
            if (options.url === reLoginUrl) {
              userStore.clearUserInfo()
              uni.showToast({
                icon: 'none',
                title: (res.data as IResData<T>).message || '登录失效，请重新登录',
              })
              reject(res)
            } else {
              handleReLoginAndRetry<T>(options).then(resolve).catch(reject)
            }
          }
          return
        }
        // 状态码 2xx，参考 axios 的设计
        if (res.statusCode === 200) {
          // 2.1 提取核心数据 res.data
          resolve(res.data as IResData<T>)
        } else if (res.statusCode === 401 && !userStore.token) {
          // 没有token而且没有权限就跳转登录页
          // userStore.clearUserInfo()
          uni.showToast({ icon: 'none', title: '没有查看权限' })
          userStore.clearUserInfo()
          if (userStore.isMaintenance) {
            setTimeout(() => {
              // 跳转回登录页
              uni.redirectTo({ url: '/pages/login/index' })
            }, 800)
          }

          reject(res)
        } else {
          // 其他错误 -> 根据后端错误信息轻提示
          // toast.error('请求错误')
          reject(res)
        }
      },
      // 响应失败
      fail(err) {
        uni.showToast({
          icon: 'none',
          title: '网络错误，换个网络试试',
        })
        reject(err)
      },
    })
  })
}

// uni.uploadFile封装
export const uniFileUpload = <T>(options: CustomRequestOptions) => {
  // 1. 返回 Promise 对象
  return new Promise<IResData<T>>((resolve, reject) => {
    uni.uploadFile({
      ...options,
      // 响应成功
      success(res) {
        // 状态码 2xx，参考 axios 的设计
        if (res.statusCode >= 200 && res.statusCode < 300) {
          // 文件上传接口的rea.data的类型为string，这里转一下
          const resData = JSON.parse(res.data) as IResData<T>
          resolve(resData)
        } else if (res.statusCode === 401) {
          // 401错误  -> 清理用户信息，跳转到登录页
          // userStore.clearUserInfo()
          // uni.navigateTo({ url: '/pages/login/login' })
          reject(res)
        } else {
          // 其他错误 -> 根据后端错误信息轻提示
          uni.showToast({ icon: 'none', title: '文件上传错误' })
          reject(res)
        }
      },
      // 响应失败
      fail(err) {
        uni.showToast({ icon: 'none', title: '网络错误，换个网络试试' })
        reject(err)
      },
    })
  })
}
