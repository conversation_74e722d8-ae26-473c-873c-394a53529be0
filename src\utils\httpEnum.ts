/**
 * @description：请求配置
 */
export enum ResultEnum {
  SUCCESS = 101,
  success = 200,
  ERROR = 500,
  OVERDUE = 401,
  TIMEOUT = 5000,
  TYPE = 'success',
}
// 服务器响应状态枚举
export enum ResponseStatus {
  /** 成功 */
  SUCCESS = 101,
  /** 失败 */
  FAILURE = 102,
  /** 上传成功 */
  UPLOAD_SUCCESS = 104,
  /** 上传失败 */
  UPLOAD_FAILURE = 105,
  /** 参数验证失败 */
  DATA_VALIDATION_FAILURE = 106,
  /** 用户名不存在 */
  USERNAME_NOT_FOUND = 103,
  /** 用户未登录 */
  USER_NEED_AUTHORITIES = 201,
  /** 用户或密码错误 */
  USER_LOGIN_FAILED = 400,
  /** 用户登录成功 */
  USER_LOGIN_SUCCESS = 200,
  /** 用户无权访问 */
  USER_NO_ACCESS = 300,
  /** 用户登出成功 */
  USER_LOGOUT_SUCCESS = 100,
  /** 此token为黑名单 */
  TOKEN_IS_BLACKLIST = 206,
  /** 登录已失效 */
  LOGIN_IS_OVERDUE = 207,
  /** FEIGN 获取当前用户失败 */
  FEIGN_GET_USER_FAILED = 208,
}

/**
 * @description：请求方法
 */
export enum RequestEnum {
  GET = 'GET',
  POST = 'POST',
  PATCH = 'PATCH',
  PUT = 'PUT',
  DELETE = 'DELETE',
}

/**
 * @description：常用的 contentTyp 类型
 */
export enum ContentTypeEnum {
  // json
  JSON = 'application/json;charset=UTF-8',
  // text
  TEXT = 'text/plain;charset=UTF-8',
  // form-data 一般配合qs
  FORM_URLENCODED = 'application/x-www-form-urlencoded;charset=UTF-8',
  // form-data 上传
  FORM_DATA = 'multipart/form-data;charset=UTF-8',
}
