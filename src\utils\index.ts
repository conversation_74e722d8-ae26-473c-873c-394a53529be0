import pagesJson from '@/pages.json'
import { useUserStore } from '@/store'

/** 判断当前页面是否是tabbar页  */
export const getIsTabbar = () => {
  if (!Object.keys(pagesJson).includes('tabBar')) {
    return false
  }
  const pages = getCurrentPages()
  const lastPage = getLastItem(pages)
  const currPath = lastPage.route
  return !!pagesJson.tabBar.list.find((e) => e.pagePath === currPath)
}

/**
 * 获取当前页面路由的 path 路径和 redirectPath 路径
 * path 如 ‘/pages/login/index’
 * redirectPath 如 ‘/pages/demo/base/route-interceptor’
 */
export const currRoute = () => {
  const pages = getCurrentPages()
  // console.log('pages:', pages)

  const lastPage = getLastItem(pages)
  const currRoute = (lastPage as any).$page
  // console.log('lastPage.$page:', currRoute)
  // console.log('lastPage.$page.fullpath:', currRoute.fullPath)
  // console.log('lastPage.$page.options:', currRoute.options)
  // console.log('lastPage.options:', (lastPage as any).options)
  // 经过多端测试，只有 fullPath 靠谱，其他都不靠谱
  const { fullPath } = currRoute as { fullPath: string }
  // console.log('fullPath', fullPath)
  // eg: /pages/login/index?redirect=%2Fpages%2Fdemo%2Fbase%2Froute-interceptor (小程序)
  // eg: /pages/login/index?redirect=%2Fpages%2Froute-interceptor%2Findex%3Fname%3Dfeige%26age%3D30(h5)
  return getUrlObj(fullPath)
}

const ensureDecodeURIComponent = (url: string) => {
  if (url.startsWith('%')) {
    return ensureDecodeURIComponent(decodeURIComponent(url))
  }
  return url
}
/**
 * 解析 url 得到 path 和 query
 * 比如输入url: /pages/login/index?redirect=%2Fpages%2Fdemo%2Fbase%2Froute-interceptor
 * 输出: {path: /pages/login/index, query: {redirect: /pages/demo/base/route-interceptor}}
 */
export const getUrlObj = (url: string) => {
  const [path, queryStr] = url.split('?')
  const query: Record<string, string> = {}
  if (queryStr) {
    queryStr.split('&').forEach((item) => {
      const [key, value] = item.split('=')
      query[key] = ensureDecodeURIComponent(value) // 这里需要统一 decodeURIComponent 一下，可以兼容h5和微信y
    })
    return { path, query }
  }

  return { path }
}
/**
 * 得到所有的需要登录的pages，包括主包和分包的
 * 这里设计得通用一点，可以传递key作为判断依据，默认是 needLogin, 与 route-block 配对使用
 * 如果没有传 key，则表示所有的pages，如果传递了 key, 则表示通过 key 过滤
 */
export const getAllPages = (key = 'needLogin') => {
  // 这里处理主包
  const pages = [
    ...pagesJson.pages
      .filter((page) => !key || page[key])
      .map((page) => ({
        ...page,
        path: `/${page.path}`,
      })),
  ]
  // 这里处理分包
  const subPages: any[] = []
  pagesJson.subPackages.forEach((subPageObj) => {
    // console.log(subPageObj)
    const { root } = subPageObj

    subPageObj.pages
      .filter((page) => !key || page[key])
      .forEach((page: { path: string } & Record<string, any>) => {
        subPages.push({
          ...page,
          path: `/${root}/${page.path}`,
        })
      })
  })
  const result = [...pages, ...subPages]
  // console.log(`getAllPages by ${key} result: `, result)
  return result
}

/**
 * 得到所有的需要登录的pages，包括主包和分包的
 * 只得到 path 数组
 */
export const getNeedLoginPages = (): string[] => getAllPages('needLogin').map((page) => page.path)

/**
 * 得到所有的需要登录的pages，包括主包和分包的
 * 只得到 path 数组
 */
export const needLoginPages: string[] = getAllPages('needLogin').map((page) => page.path)
/**
 * 处理距离自动处理转化为km/m
 * 格式化数值为最多两位小数且末尾不为零，无效值返回'-'
 * @example
 * formatDecimal(10.126)  // "10.13"
 * formatDecimal(0.102)    // "0.1"
 * formatDecimal(null)     // "-"
 */
export const formatDecimal = (value: unknown): string => {
  // 检查是否为 null 或 undefined
  if (value === null || value === undefined) {
    return '-'
  }

  // 转换为数值并验证有效性
  const num = Number(value)
  if (isNaN(num) || typeof value === 'boolean') {
    return ' - '
  }

  // 处理零值
  if (num === 0) return '0m'

  // 判断是否大于等于 1000
  if (num >= 1000) {
    const kmValue = num / 1000
    // 四舍五入到两位小数
    const formatted = kmValue.toFixed(2)
    // 去除小数末尾的零
    const trimmed = formatted.replace(/0+$/, '')
    // 如果小数部分为空，去掉小数点
    const result = trimmed.endsWith('.') ? trimmed.slice(0, -1) : trimmed
    return `${result}km`
  } else {
    // 四舍五入到两位小数
    const formatted = num.toFixed(2)
    // 去除小数末尾的零
    const trimmed = formatted.replace(/0+$/, '')
    // 如果小数部分为空，去掉小数点
    const result = trimmed.endsWith('.') ? trimmed.slice(0, -1) : trimmed
    return `${result}m`
  }
}
/** 主要是处理 arr.at(-1) 在安卓机上运行报错的 兼容性问题 */
export const getArrElementByIdx = (arr: any[], index: number) => {
  if (index < 0) return arr[arr.length + index]
  if (index >= arr.length) return undefined
  return arr[index]
}

export const getLastItem = (arr: any[]) => getArrElementByIdx(arr, -1)
// 对象合并子对象
export const mergeNestObject = (
  obj: { [key: string]: any },
  nestObjKeys: string[] | string,
  useParentKeys = [],
) => {
  if (!nestObjKeys || nestObjKeys.length === 0) return obj
  nestObjKeys = Array.isArray(nestObjKeys) ? nestObjKeys : [nestObjKeys]

  const newObj = { ...obj }
  try {
    let nestObj = {}
    for (const nestObjKey of nestObjKeys) {
      if (typeof obj[nestObjKey] === 'string') {
        nestObj = JSON.parse(obj[nestObjKey])
      }
    }
    Object.assign(newObj, nestObj)
    for (const key of useParentKeys) {
      newObj[key] = obj[key]
    }
    return newObj
  } catch (e) {
    console.error('数据异常：', e)
    return obj
  }
}

// 显示值
export const showValue = (value: any, enums = {}, defaultValue = '-') => {
  if (value === undefined || value === null) return defaultValue
  return enums[value] || value
}
// 过滤掉对象中值为空的属性
export const filterEmptyValues = (obj: object) => {
  // 使用 Object.entries 将对象转换为键值对数组
  const entries = Object.entries(obj)

  // 过滤掉值为空的键值对
  const filteredEntries = entries.filter(([key, value]) => {
    // 检查值是否为：空字符串、null、undefined、空数组
    return (
      value !== '' &&
      value !== null &&
      value !== undefined &&
      !(Array.isArray(value) && value.length === 0)
    )
  })

  // 将过滤后的键值对转换回对象
  const filteredObj = Object.fromEntries(filteredEntries)
  return filteredObj
}
// 传（数组对象，数组对象需要修改/添加的字段key，数组对象中需要用到的字段key）把‘修改/添加的字段的值’改为‘用到的字段的值-用到的字段的值’的形式
export const changeDataFormat = (arr: any[], key: string, key2: string, showName: string) => {
  if (arr)
    arr.forEach((item) => {
      item[key] = `${showName}-${item[key2]}`
    })
  return arr
}
// 显示值后缀
export const showValueSuffix = (value: any, suffix: string) => {
  const result = showValue(value)
  if (result === '-') return result
  return `${result}${suffix}`
}
export interface TaskItem {
  id: string
  taskId: string
  name: string
  type: string | null
  sort: number
  status: number
  startTime: string | null
  endTime: string | null
  dataInfo: any | null
  createAccount: string | null
  createDate: string
}
// 任务流程辅助函数 返回需要显示的三个步骤
// 条件：查找status === 1的项,则状态为执行中。
// 查看此项的索引,如果是第一项返回列表索引0，1，2
// 如果是最后一项返回列表索引的最后三项如1，2，3，4，5，6，7，8  则返回6，7，8
// 如果不是第一项，则返回列表索引当前项，前一项，后一项
export const getTaskStatus = (list: TaskItem[]) => {
  const activeIdx = getActive(list)
  if (activeIdx === 0) return list.slice(0, 3)
  if (activeIdx === list.length - 1) return list.slice(list.length - 3)
  return list.slice(activeIdx - 1, activeIdx + 2)
}
// 写一个函数传入数组对象,根据数组对象中的status字段返回,正在执行中对象的索引
export const getActive = (list: TaskItem[]) => {
  if (list?.length) return list.findIndex((v) => Number(v.status) === 1)
  return 0
}
// 返回激活的状态索引（0，1，2）
export const getActiveIdx = (list: TaskItem[]) => {
  const num = list.findIndex((v) => Number(v.status) === 1)
  // 第一项则激活组件激活态为‘0’
  if (num === 0) return 0
  if (num === list.length - 1) return 2
  // 其他则取中间‘1’
  return 1
}

// 请求基地址
export const REQUEST_BASE_URL =
  import.meta.env.VITE_PLATFORM === 'wx'
    ? import.meta.env.VITE_SERVER_BASEURL
    : import.meta.env.DEV
      ? '/wx'
      : import.meta.env.VITE_SERVER_BASEURL

/**
 * 获取该微信用户的code
 */
export async function getLoginCode() {
  return new Promise((resolve, reject) => {
    uni.login({
      onlyAuthorize: true,
      success: (res) => {
        resolve(res.code)
      },
      fail: (err) => reject(err),
    })
  })
}

// 小程序获取用户手机号对应的code
export const getUserPhoneCode = async (e) => {
  return new Promise((resolve, reject) => {
    if (e.detail.errMsg === 'getPhoneNumber:ok') {
      resolve(e.detail.code)
    } else {
      reject(e.detail.errMsg)
    }
  })
}

// 获取车辆状态显示
export const getDeviceStatus = (status?: number | string) => {
  if (!status) return { text: '-', color: '#656666' }
  switch (+status) {
    case 1006:
      return { text: '离线', color: '#656666' }
    default:
      return { text: '在线', color: 'rgba(0, 194, 144, 1)' }
  }
}

// 解析url
export const parseUrl = (url: string) => {
  const [path, queryStr] = url.split('?')
  const query: Record<string, string> = {}
  if (queryStr) {
    queryStr.split('&').forEach((item) => {
      const [key, value] = item.split('=')
      query[key] = value
    })
  }
  return { path, query }
}
