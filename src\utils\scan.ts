// 消费者扫码业务功能封装
import { useCart } from '@/pages/goods-menu/useCart'
import { ORDER_TYPE } from '@/types/consumer'
import { parseUrl } from '@/utils'

const { clearCart } = useCart()

export const scanCode = (
  types: Exclude<keyof typeof ORDER_TYPE, 'remote'>[],
  options: {
    navigateToPaymentPage?: boolean
    scanSuccess?: (url: string) => void
    appSuccess?: (url: string) => void
  } = {},
) => {
  const { navigateToPaymentPage, scanSuccess, appSuccess } = Object.assign(
    { navigateToPaymentPage: true },
    options,
  )
  uni.scanCode({
    scanType: ['qrCode'],
    success: (res) => {
      const { path, query } = parseUrl(res.result)
      const baseUrl = import.meta.env.VITE_SERVER_BASEURL
      // 现场扫码购买
      if (types.includes('scan') && res.scanType === 'QR_CODE' && path === `${baseUrl}/home`) {
        const url = '/pages/index/index?c=' + query.c + '&t=' + query.t + '&s=' + query.s
        if (scanSuccess) {
          scanSuccess(url)
        } else {
          console.log('scanCode relaunch')
          uni.reLaunch({ url })
        }
      } else if (
        types.includes('app') &&
        res.scanType === 'QR_CODE' &&
        path === `${baseUrl}/payments` &&
        query.sign
      ) {
        // 本机购 扫码支付
        clearCart()
        const sign = query.sign
        const url = `/pages/goods-menu/payments/index?sign=${sign}`
        const params = { url }
        if (appSuccess) {
          appSuccess(url)
          return
        }
        if (navigateToPaymentPage) {
          uni.navigateTo(params)
        } else {
          uni.redirectTo(params)
        }
      } else {
        uni.showToast({ title: '请扫描正确的二维码', icon: 'none' })
      }
    },
  })
}
